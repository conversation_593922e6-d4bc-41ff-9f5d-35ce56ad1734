@charset "UTF-8";
@import "element";

// im全局样式变量
:root {
  // 主色
  --im-color-primary: #{$--color-primary};
  --im-color-primary-light-1: #{$--color-primary-light-1};
  --im-color-primary-light-2: #{$--color-primary-light-2};
  --im-color-primary-light-3: #{$--color-primary-light-3};
  --im-color-primary-light-4: #{$--color-primary-light-4};
  --im-color-primary-light-5: #{$--color-primary-light-5};
  --im-color-primary-light-6: #{$--color-primary-light-6};
  --im-color-primary-light-7: #{$--color-primary-light-7};
  --im-color-primary-light-8: #{$--color-primary-light-8};
  --im-color-primary-light-9: #{$--color-primary-light-9};

  --im-color-sucess: #{$--color-success};
  --im-color-warning: #{$--color-warning};
  --im-color-danger: #{$--color-danger};
  --im-color-info: #{$--color-info};

  // 文字颜色
  --im-text-color: #{$--color-text-regular};
  --im-text-color-light: #999999;
  --im-text-color-lighter: #C0C4CC;

  // 文字大小
  --im-font-size: #{$--font-size-base};
  --im-font-size-small: #{$--font-size-small};
  --im-font-size-smaller: #{$--font-size-extra-small};
  --im-font-size-large: #{$--font-size-medium};
  --im-font-size-larger: #{$--font-size-large};
  --im-font-family: #{$--font-family};


  // 边框颜色
  --im-border: 1px solid #EBEEF5;

  // 阴影
  --im-box-shadow: #{$--box-shadow-base};
  --im-box-shadow-light: #{$--box-shadow-light};
  --im-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, .12);
  --im-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, .08), 0px 12px 32px rgba(0, 0, 0, .12), 0px 8px 16px -8px rgba(0, 0, 0, .16);

  // 背景色
  --im-background: #F3F3F3;
  --im-background-active: #F1F1F1;
  --im-background-active-dark: #E9E9E9;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  margin: 0;
  overflow: hidden;
}

section {
  height: 100%;
}

.el-dialog__body {
  padding: 10px 20px !important;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 1px;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: hsla(0, 0%, 73%, .5);
}

::-webkit-scrollbar-track {
  border-radius: 4px;
}

.search-input {
  .el-input__inner {
    border: unset !important;
  }

}
