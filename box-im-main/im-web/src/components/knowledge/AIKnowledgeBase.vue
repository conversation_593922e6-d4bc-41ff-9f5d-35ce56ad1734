<template>
  <div class="ai-knowledge-base">
    <!-- 文件上传表单 -->
    <el-card class="form-card">
      <div slot="header">
        <span>文档上传</span>
      </div>
      
      <el-form ref="uploadForm" :model="uploadForm" :rules="uploadRules" label-width="120px">
        <el-form-item label="用户名" prop="user_name">
          <el-input v-model="uploadForm.user_name" placeholder="请输入用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="上传文件" prop="infile">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :file-list="fileList"
            accept=".json">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传 JSON 文件</div>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitUpload">提交</el-button>
          <el-button @click="resetUpload">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 原有的AI知识库表单 -->
    <el-card class="form-card">
      <div slot="header">
        <span>AI知识库管理</span>
        <div class="internal-stats">
          <el-tag type="success">已提交：{{internalCount}}条</el-tag>
          <el-tag type="warning" style="margin-left: 10px">{{p1}}元一条，共有:{{internalMoney}}元</el-tag>
        </div>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="用户名" prop="user_name">
          <el-input v-model="form.user_name" placeholder="请输入用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="标题" prop="instruction">
          <el-input type="textarea" v-model="form.instruction" placeholder="请输入标题"></el-input>
        </el-form-item>
        
        <el-form-item label="问题" prop="input">
          <el-input type="textarea" v-model="form.input" placeholder="请输入输入问题"></el-input>
        </el-form-item>
        
        <el-form-item label="答案" prop="output">
          <el-input type="textarea" v-model="form.output" placeholder="请输入输出答案"></el-input>
        </el-form-item>
        
        <el-form-item label="数据集文件名" prop="group_name">
          <el-input v-model="form.group_name" placeholder="请输入数据集文件名"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AIKnowledgeBase',
  data() {
    return {
      form: {
        user_name: '',
        instruction: '',
        input: '',
        output: '',
        group_name: ''
      },
      rules: {
        user_name: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        instruction: [
          // 移除必填验证
        ],
        input: [
          { required: true, message: '请输入输入内容', trigger: 'blur' }
        ],
        output: [
          { required: true, message: '请输入输出内容', trigger: 'blur' }
        ],
        group_name: [
          { required: true, message: '请输入数据集文件名', trigger: 'blur' }
        ]
      },
      internalCount: 0,
      internalMoney: 0,
      p1: 0,
      uploadForm: {
        user_name: '',
        infile: null
      },
      uploadRules: {
        user_name: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        infile: [
          { required: true, message: '请选择要上传的文件', trigger: 'change' }
        ]
      },
      fileList: []
    }
  },
  created() {
    // 从store获取用户名
    const userName = this.$store.state.userStore.userInfo.nickName
    if (userName) {
      this.form.user_name = userName
      this.uploadForm.user_name = userName
    }
    // 获取统计信息
    this.fetchInternalStats()
  },
  methods: {
    async fetchInternalStats() {
      try {
        const userName = this.$store.state.userStore.userInfo.nickName;
        if (!userName) {
          console.log('等待用户信息加载...');
          return;
        }
        const response = await axios.get('http://*************:6432/ai_date_count_by_name', {
          params: {
            name: userName
          }
        });
        if (response.data.status_code === 200) {
          this.internalCount = response.data.data;
          this.internalMoney = response.data.money1;
          this.p1 = response.data.p1;
        }
      } catch (error) {
        console.error('获取内部知识库统计失败:', error);
        this.$message.error('获取内部知识库统计失败');
      }
    },
    async submitForm() {
      try {
        await this.$refs.form.validate()
        // 将表单数据包装成数组
        const requestData = [{
          user_name: this.form.user_name,
          instruction: this.form.instruction,
          input: this.form.input,
          output: this.form.output,
          group_name: this.form.group_name
        }]
        
        const response = await axios.post('http://*************:6432/save_ai_date_pair', requestData)
        
        if (response.data) {
          // 使用弹窗显示提交结果
          this.$alert(
            `提交结果：\n插入成功：${response.data.inserted_count} 条\n重复数据：${response.data.duplicate_count} 条`,
            '提交结果',
            {
              confirmButtonText: '确定',
              callback: () => {
                this.resetForm()
                this.fetchInternalStats()
              }
            }
          )
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请重试')
      }
    },
    resetForm() {
      this.$refs.form.resetFields()
      // 重置后重新设置用户名
      const userName = this.$store.state.userStore.userInfo.nickName
      if (userName) {
        this.form.user_name = userName
        this.uploadForm.user_name = userName
      }
    },
    // 文件上传前的验证
    beforeUpload(file) {
      const isJSON = file.type === 'application/json' || file.name.endsWith('.json')
      if (!isJSON) {
        this.$message.error('只能上传 JSON 文件！')
        return false
      }
      return true
    },
    
    // 文件选择改变时的处理
    handleFileChange(file, fileList) {
      if (file.status === 'ready') {
        this.uploadForm.infile = file.raw
        this.fileList = fileList
      }
    },
    
    // 提交上传
    async submitUpload() {
      try {
        await this.$refs.uploadForm.validate()
        
        if (!this.uploadForm.infile) {
          this.$message.warning('请选择要上传的文件')
          return
        }
        
        // 再次验证文件类型
        if (!this.uploadForm.infile.type.includes('json') && !this.uploadForm.infile.name.endsWith('.json')) {
          this.$message.error('只能上传 JSON 文件！')
          return
        }
        
        const formData = new FormData()
        formData.append('infile', this.uploadForm.infile)
        formData.append('user_name', this.uploadForm.user_name)
        
        const response = await axios.post('http://*************:6432/save_file', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.data) {
          this.$message.success('文件上传成功')
          this.resetUpload()
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        this.$message.error('文件上传失败，请重试')
      }
    },
    
    // 重置上传表单
    resetUpload() {
      this.$refs.uploadForm.resetFields()
      this.fileList = []
      this.uploadForm.infile = null
      // 重置后重新设置用户名
      const userName = this.$store.state.userStore.userInfo.nickName
      if (userName) {
        this.uploadForm.user_name = userName
      }
    },
  },
  watch: {
    '$store.state.userStore.userInfo.nickName': {
      handler(newVal) {
        if (newVal) {
          this.form.user_name = newVal
          this.uploadForm.user_name = newVal
          this.fetchInternalStats()
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-knowledge-base {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  
  .form-card {
    margin-bottom: 20px;
    background-color: #fff;
    
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
  
  .el-textarea {
    width: 100%;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-input__inner) {
    width: 100%;
  }

  .internal-stats {
    float: right;
    .el-tag {
        font-size: 14px;
        padding: 8px 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 120px;
      }
  }

  .upload-demo {
    width: 100%;
    
    :deep(.el-upload) {
      width: 100%;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
    }
  }
  
  .el-form-item:last-child {
    margin-bottom: 0;
  }
}
</style> 