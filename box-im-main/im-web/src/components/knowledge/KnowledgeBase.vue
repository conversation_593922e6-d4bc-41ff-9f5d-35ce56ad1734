<template>
  <div class="test-page">
    <div class="knowledge-header">
      <h2>外部知识记录</h2>
      <div class="knowledge-stats">
        <el-tag type="success">这个月已提交：{{knowledgeCount}}条</el-tag>
        <el-tag type="warning" style="margin-left: 10px">知识记录{{p1}}元一条，共有:{{knowledgeMoney}}元</el-tag>
        <el-tag type="warning" style="margin-left: 10px">部门共提交:{{internal_count}}条</el-tag>
      </div>
    </div>
    <el-form :model="formData" :rules="rules" ref="testForm" label-width="120px">
      <el-form-item label="用户名：" prop="user_name">
        <el-input v-model="formData.user_name" placeholder="请输入用户名"></el-input>
      </el-form-item>
      
      <el-form-item label="标题：" prop="instruction">
        <el-input 
          v-model="formData.instruction" 
          type="textarea" 
          :rows="4"
          placeholder="请输入标题，例如：'RabbitMQ 的常见使用场景有哪些？'"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="问题：" prop="input">
        <el-input 
          v-model="formData.input" 
          type="textarea" 
          :rows="4"
          placeholder="请输入内容"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="答案：" prop="output">
        <el-input 
          v-model="formData.output" 
          type="textarea" 
          :autosize="{ minRows: 6, maxRows: 20 }"
          placeholder="请输入答案内容"
        ></el-input>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm('testForm')">提交</el-button>
        <el-button @click="resetForm('testForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'KnowledgeBase',
  data() {
    return {
      knowledgeCount: 0,
      knowledgeMoney: 0,
      formData: {
        user_name: '',
        instruction: '',
        input: '',
        output: ''
      },
      rules: {
        user_name: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        input: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
        output: [
          { required: true, message: '请输入答案内容', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    async fetchKnowledgeStats() {
      try {
        const userName = this.$store.state.userStore.userInfo.nickName;
        if (!userName) {
          console.log('等待用户信息加载...');
          return;
        }
        const response = await axios.get(`http://*************:6432/knowledge_count_by_name`, {
          params: {
            name: userName
          }
        });
        this.knowledgeCount = response.data.data;
        this.knowledgeMoney = response.data.money1;
        this.internal_count = response.data.internal_count;
        this.p1=response.data.p1;
      } catch (error) {
        console.error('获取外部知识库统计失败:', error);
        this.$message.error('获取外部知识库统计失败');
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 创建独立的 axios 实例
          const instance = axios.create({
            baseURL: 'http://record.jenasi.ai:99',
            withCredentials: false,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });

          instance.post('/save_knowledge_base', this.formData)
            .then((response) => {
              this.$message.success("保存成功");
              console.log("保存成功：", response.data);
              // 清除表单内容
              this.resetForm('testForm');
              // 重置表单数据
              this.formData = {
                user_name: this.$store.state.userStore.userInfo.nickName,
                instruction: '',
                input: '',
                output: ''
              };
            })
            .catch((error) => {
              if (error.response) {
                // 处理 307 重定向
                if (error.response.status === 307) {
                  const redirectUrl = error.response.headers.location;
                  if (redirectUrl) {
                    instance.post(redirectUrl, this.formData)
                      .then((response) => {
                        this.$message.success("保存成功");
                        console.log("保存成功：", response.data);
                      })
                      .catch((error) => {
                        this.$message.error("保存失败：" + (error.message || '未知错误'));
                        console.error("保存失败：", error);
                      });
                    return;
                  }
                }
              }
              this.$message.error("保存失败：" + (error.message || '未知错误'));
              console.error("保存失败：", error);
            });
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    mounted() {
      this.fetchKnowledgeStats();
    }
  },
  watch: {
    '$store.state.userStore.userInfo.nickName': {
      handler(newVal) {
        if (newVal) {
          this.formData.user_name = newVal;
          this.fetchKnowledgeStats();
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  
  .knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #303133;
    }

    .knowledge-stats {
      .el-tag {
        font-size: 14px;
        padding: 8px 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 120px;
      }
    }
  }
}
</style> 