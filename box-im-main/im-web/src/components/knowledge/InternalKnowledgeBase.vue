<template>
  <div class="internal-knowledge-base">
    <div class="internal-header">
      <h2>内部知识库管理</h2>
      <div class="internal-stats">
        <el-tag type="success">已提交：{{internalCount}}条</el-tag>
        <el-tag type="warning" style="margin-left: 10px">{{p1}}元一条，共有:{{internalMoney}}元</el-tag>
        <el-tag type="warning" style="margin-left: 10px">工作记录流程文档{{p2}}元一条，共有:{{internalMoney1}}元</el-tag>
        <el-tag type="warning" style="margin-left: 10px">部门共提交:{{internal_count}}条</el-tag>
      </div>
    </div>
    <el-form :model="formData" :rules="rules" ref="internalKnowledgeForm" label-width="120px">
      <el-form-item label="用户名：" prop="user_name">
        <el-input v-model="formData.user_name" placeholder="请输入用户名"></el-input>
      </el-form-item>
      
      <el-form-item label="标题：" prop="instruction">
        <el-input 
          v-model="formData.instruction" 
          type="textarea" 
          :rows="4"
          placeholder="请输入标题，例如：'RabbitMQ 的常见使用场景有哪些？'"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="问题：" prop="input">
        <el-input 
          v-model="formData.input" 
          type="textarea" 
          :rows="4"
          placeholder="请输入问题内容"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="答案：" prop="output">
        <el-input 
          v-model="formData.output" 
          type="textarea" 
          :autosize="{ minRows: 6, maxRows: 20 }"
          placeholder="请输入答案内容"
        ></el-input>
      </el-form-item>

      <el-form-item label="图片：" prop="images">
        <el-upload
          class="image-uploader"
          action="#"
          :http-request="handleImageUpload"
          :show-file-list="false"
          :before-upload="beforeImageUpload"
        >
          <img v-if="imageUrl" :src="imageUrl" class="uploaded-image">
          <el-button v-else type="primary">点击上传图片</el-button>
        </el-upload>
      </el-form-item>

      <el-form-item label="文档：" prop="files">
        <el-upload
          class="file-uploader"
          action="#"
          :http-request="handleFileUpload"
          :before-upload="beforeFileUpload"
          :file-list="fileList"
          multiple
        >
          <el-button type="primary">点击上传文档</el-button>
          <div slot="tip" class="el-upload__tip">支持上传 .doc, .docx, .pdf, .txt, .md, .xls, .xlsx, .ppt, .pptx 格式的文件，单个文件不超过50MB</div>
        </el-upload>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm('internalKnowledgeForm')">提交</el-button>
        <el-button @click="resetForm('internalKnowledgeForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'InternalKnowledgeBase',
  data() {
    return {
      internalCount: 0,
      internalMoney: 0,
      internalMoney1: 0,
      formData: {
        user_name: '',
        instruction: '',
        input: '',
        output: ''
      },
      imageUrl: '',
      imageFile: null,
      fileList: [], // 存储上传的文件列表
      rules: {
        user_name: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        input: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ],
        output: [
          { required: true, message: '请输入答案内容', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    async fetchInternalStats() {
      try {
        const userName = this.$store.state.userStore.userInfo.nickName;
        if (!userName) {
          console.log('等待用户信息加载...');
          return;
        }
        const response = await axios.get(`http://192.168.1.177:6432/internal_count_by_name`, {
          params: {
            name: userName
          }
        });
        this.internalCount = response.data.data;
        this.internalMoney = response.data.money1;
        this.internalMoney1 = response.data.money2;
        this.internal_count = response.data.internal_count;
        this.p1=response.data.p1;
        this.p2=response.data.p2;
      } catch (error) {
        console.error('获取内部知识库统计失败:', error);
        this.$message.error('获取内部知识库统计失败');
      }
    },
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    handleImageUpload(options) {
      this.imageFile = options.file;
      this.imageUrl = URL.createObjectURL(options.file);
    },
    beforeFileUpload(file) {
      const validTypes = ['.doc', '.docx', '.pdf', '.txt','.md','.xls','.xlsx','.ppt','.pptx'];
      const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
      const isValidType = validTypes.includes(extension);
      const isLt10M = file.size / 1024 / 1024 < 50;

      if (!isValidType) {
        this.$message.error('只能上传 .doc, .docx, .pdf, .txt, .md, .xls, .xlsx, .ppt, .pptx 格式的文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 50MB!');
        return false;
      }
      return true;
    },
    handleFileUpload(options) {
      this.fileList.push({
        name: options.file.name,
        file: options.file
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 创建 FormData 对象
          const formData = new FormData();
          
          // 添加图片文件
          if (this.imageFile) {
            formData.append('images', this.imageFile);
          }

          // 添加文档文件
          this.fileList.forEach((file, index) => {
            formData.append(`files`, file.file);
          });

          // 添加文本数据
          const textData = {
            user_name: this.formData.user_name,
            instruction: this.formData.instruction,
            input: this.formData.input,
            output: this.formData.output
          };
          formData.append('data', JSON.stringify(textData));

          // 创建独立的 axios 实例
          const instance = axios.create({
            baseURL: 'http://record.jenasi.ai:99',
            withCredentials: false,
            headers: {
              'Content-Type': 'multipart/form-data',
              'Accept': 'application/json'
            }
          });

          instance.post('/save_internal_knowledge_base', formData)
            .then((response) => {
              this.$message.success("保存成功");
              console.log("保存成功：", response.data);
              // 清除表单内容
              this.resetForm('internalKnowledgeForm');
              // 重置图片和文件
              this.imageUrl = '';
              this.imageFile = null;
              this.fileList = [];
            })
            .catch((error) => {
              if (error.response) {
                // 处理 307 重定向
                if (error.response.status === 307) {
                  const redirectUrl = error.response.headers.location;
                  if (redirectUrl) {
                    instance.post(redirectUrl, formData)
                      .then((response) => {
                        this.$message.success("保存成功");
                        console.log("保存成功：", response.data);
                        // 清除表单内容
                        this.resetForm('internalKnowledgeForm');
                        // 重置图片和文件
                        this.imageUrl = '';
                        this.imageFile = null;
                        this.fileList = [];
                      })
                      .catch((error) => {
                        this.$message.error("保存失败：" + (error.message || '未知错误'));
                        console.error("保存失败：", error);
                      });
                    return;
                  }
                }
              }
              this.$message.error("保存失败：" + (error.message || '未知错误'));
              console.error("保存失败：", error);
            });
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },
    resetForm(formName) {
      // 保存当前用户名
      const currentUserName = this.formData.user_name;
      this.$refs[formName].resetFields();
      // 恢复用户名
      this.formData.user_name = currentUserName;
      this.imageUrl = '';
      this.imageFile = null;
      this.fileList = [];
    }
  },
  mounted() {
    this.fetchInternalStats();
  },
  watch: {
    '$store.state.userStore.userInfo.nickName': {
      handler(newVal) {
        if (newVal) {
          this.formData.user_name = newVal;
          this.fetchInternalStats();
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.internal-knowledge-base {
  padding: 20px;
  
  .internal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #303133;
    }

    .internal-stats {
      .el-tag {
        font-size: 14px;
        padding: 8px 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 120px;
      }
    }
  }

  .image-uploader {
    .uploaded-image {
      width: 200px;
      height: 200px;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  .file-uploader {
    .el-upload__tip {
      line-height: 1.2;
      padding-top: 5px;
      color: #909399;
    }
  }
}
</style> 