<template>
  <div class="work-log">
    <div class="work-log-header">
      <h2>工作日志管理</h2>
      <div class="work-log-count">
        <el-tag type="success">这个月已提交工作日志：{{workLogCount}}条</el-tag>
      </div>
    </div>
    <div class="work-log-content">
      <!-- 当前编辑的表单 -->
      <el-form :model="currentWorkLog" :rules="rules" ref="workLogForm" label-width="120px">
        <el-form-item label="日期：" prop="date">
          <el-date-picker
            v-model="currentWorkLog.date"
            type="date"
            placeholder="请选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="员工姓名：" prop="employeeName">
          <el-input v-model="currentWorkLog.employeeName" placeholder="请输入员工姓名"></el-input>
        </el-form-item>
        
        <el-form-item label="任务描述：" prop="taskDescription">
          <div class="task-description-container">
            <el-input 
              v-model="currentWorkLog.taskDescription" 
              type="textarea" 
              :autosize="{ minRows: 4, maxRows: 20 }"
              placeholder="请输入任务描述，例如：做了哪些事情，有没有遇到什么问题，遇到问题怎样解决的"
            ></el-input>
            <el-button 
              type="primary" 
              class="add-record-btn"
              @click="addWorkLog">
              <i class="el-icon-plus"></i>
              添加记录
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="工作时间：" prop="timeRange">
          <div class="time-range-container">
            <div class="time-pickers">
              <el-time-picker
                v-model="currentWorkLog.startTimeObj"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                  arrowControl: false,
                  isRange: false,
                  useArrow: false,
                  scrollbar: true,
                  popperClass: 'time-picker-popper',
                  hourStep: 1,
                  minuteStep: 1,
                  secondStep: 0,
                  showSeconds: false,
                  wheelMode: true,
                  loop: true
                }"
                placeholder="开始时间"
                @change="() => calculateDuration()">
              </el-time-picker>
              <span class="time-separator">至</span>
              <el-time-picker
                v-model="currentWorkLog.endTimeObj"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                  arrowControl: false,
                  isRange: false,
                  useArrow: false,
                  scrollbar: true,
                  popperClass: 'time-picker-popper',
                  hourStep: 1,
                  minuteStep: 1,
                  secondStep: 0,
                  showSeconds: false,
                  wheelMode: true,
                  loop: true
                }"
                placeholder="结束时间"
                @change="() => calculateDuration()">
              </el-time-picker>
            </div>
            <div class="quick-duration" v-if="currentWorkLog.startTimeObj">
              <el-button 
                size="mini"
                type="primary"
                plain
                @click="setDuration(0.5)">
                半小时
              </el-button>
              <el-button 
                v-for="hours in 8" 
                :key="hours"
                size="mini"
                type="primary"
                plain
                @click="setDuration(hours)">
                {{hours}}小时
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="备注：" prop="remarks">
          <el-input v-model="currentWorkLog.remarks" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <!-- 历史记录列表 -->
      <div class="work-log-history" v-if="workLogList.length > 0">
        <h3>历史记录</h3>
        <div class="history-list">
          <div v-for="(item, index) in workLogList" :key="index" class="history-item">
            <div class="history-header">
              <span class="employee">{{item.employeeName}}</span>
              <span class="date">{{item.date}}</span>
              <span class="time">{{item.startTime}} - {{item.endTime}}</span>
              <span class="duration">{{item.duration}}</span>
              <div class="history-actions">
                <el-button type="primary" size="mini" @click="editHistoryItem(index)">编辑</el-button>
                <el-button type="danger" size="mini" @click="removeHistoryItem(index)">删除</el-button>
              </div>
            </div>
            <div class="history-content">
              <div class="task">任务描述：{{item.taskDescription}}</div>
              <div class="remarks" v-if="item.remarks">备注：{{item.remarks}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="work-log-actions">
      <el-button type="success" @click="submitAllForms">提交所有记录</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'WorkLog',
  data() {
    return {
      workLogCount: 0,
      currentWorkLog: {
        date: new Date().toISOString().split('T')[0],
        employeeName: '',
        taskDescription: '',
        startTimeObj: '',
        endTimeObj: '',
        startTime: '',
        endTime: '',
        duration: '',
        remarks: ''
      },
      rules: {
        date: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        employeeName: [
          { required: true, message: '请输入员工姓名', trigger: 'blur' }
        ],
        taskDescription: [
          { required: true, message: '请输入任务描述', trigger: 'blur' }
        ],
        startTimeObj: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTimeObj: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        timeRange: [
          { 
            validator: (rule, value, callback) => {
              if (!this.currentWorkLog.startTimeObj || !this.currentWorkLog.endTimeObj) {
                callback(new Error('请选择工作时间'));
              } else {
                callback();
              }
            }, 
            trigger: 'change' 
          }
        ]
      }
    };
  },
  computed: {
    workLogList() {
      return this.$store.state.workLogStore.workLogList;
    }
  },
  methods: {
    async fetchWorkLogCount() {
      try {
        const userName = this.$store.state.userStore.userInfo.nickName;
        if (!userName) {
          console.log('等待用户信息加载...');
          return;
        }
        const response = await axios.get(`http://192.168.1.177:6432/worklog_count_by_name`, {
          params: {
            name: userName
          }
        });
        this.workLogCount = response.data.data;
      } catch (error) {
        console.error('获取工作日志数量失败:', error);
        this.$message.error('获取工作日志数量失败');
      }
    },
    addWorkLog() {
      // 验证当前表单
      this.$refs.workLogForm.validate((valid) => {
        if (valid) {
          // 将当前记录添加到列表
          this.$store.commit('workLogStore/addWorkLog', {...this.currentWorkLog});
          
          // 获取当前用户名
          const userName = this.$store.state.userStore.userInfo.nickName;
          
          // 重置当前表单
          this.currentWorkLog = {
            date: new Date().toISOString().split('T')[0],
            employeeName: userName || '', // 设置用户名
            taskDescription: '',
            startTimeObj: '',
            endTimeObj: '',
            startTime: '',
            endTime: '',
            duration: '',
            remarks: ''
          };
          
          // 重置表单验证
          this.$refs.workLogForm.resetFields();

          // 保存到localStorage
          this.saveToLocalStorage();
        }
      });
    },
    calculateDuration() {
      if (this.currentWorkLog.startTimeObj && this.currentWorkLog.endTimeObj) {
        const startParts = this.currentWorkLog.startTimeObj.split(':');
        const endParts = this.currentWorkLog.endTimeObj.split(':');
        
        const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
        const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
        
        let diffMinutes = endMinutes - startMinutes;
        
        // 处理跨天的情况
        if (diffMinutes < 0) {
          diffMinutes += 24 * 60;
        }
        
        const diffHours = Math.round((diffMinutes / 60) * 10) / 10;
        
        this.currentWorkLog.duration = diffHours + "小时";
        this.currentWorkLog.startTime = this.currentWorkLog.startTimeObj + ":00";
        this.currentWorkLog.endTime = this.currentWorkLog.endTimeObj + ":00";
      }
      // 触发表单验证
      this.$refs.workLogForm.validateField('timeRange');
    },
    async submitAllForms() {
      // 创建独立的 axios 实例
      const instance = axios.create({
        baseURL: 'http://worklog.jenasi.ai:99',
        withCredentials: false,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      try {
        // 准备提交的数据
        const postData = [...this.workLogList];
        
        // 如果当前表单有数据，则验证并添加到提交列表
        if (this.currentWorkLog.taskDescription) {
          const valid = await new Promise((resolve) => {
            this.$refs.workLogForm.validate((isValid) => {
              resolve(isValid);
            });
          });

          if (!valid) {
            this.$message.warning('请完善当前表单信息');
            return;
          }
          postData.push({...this.currentWorkLog});
        }

        // 检查是否有数据需要提交
        if (postData.length === 0) {
          this.$message.warning('没有需要提交的记录');
          return;
        }

        // 提交所有记录
        const response = await instance.post('/api/work_logs', postData);
        
        // 处理响应数据
        const { total_count, success_count, fail_count } = response.data;
        
        // 显示提交结果弹窗
        this.$alert(
          `提交结果：\n总条数：${total_count}\n成功：${success_count}\n失败：${fail_count}`,
          '提交结果',
          {
            confirmButtonText: '确定',
            callback: () => {
              if (success_count > 0) {
                this.$message.success("工作日志保存成功");
                // 获取当前用户名
                const userName = this.$store.state.userStore.userInfo.nickName;
                // 清空所有记录
                this.$store.commit('workLogStore/clearWorkLogs');
                this.currentWorkLog = {
                  date: new Date().toISOString().split('T')[0],
                  employeeName: userName || '', // 设置用户名
                  taskDescription: '',
                  startTimeObj: '',
                  endTimeObj: '',
                  startTime: '',
                  endTime: '',
                  duration: '',
                  remarks: ''
                };
                this.$refs.workLogForm.resetFields();

                // 保存到localStorage
                this.saveToLocalStorage();
              }
            }
          }
        );
      } catch (error) {
        if (error.response) {
          if (error.response.status === 307) {
            const redirectUrl = error.response.headers.location;
            if (redirectUrl) {
              try {
                // 准备提交的数据
                const postData = [...this.workLogList];
                
                // 如果当前表单有数据，则验证并添加到提交列表
                if (this.currentWorkLog.taskDescription) {
                  const valid = await new Promise((resolve) => {
                    this.$refs.workLogForm.validate((isValid) => {
                      resolve(isValid);
                    });
                  });

                  if (!valid) {
                    this.$message.warning('请完善当前表单信息');
                    return;
                  }
                  postData.push({...this.currentWorkLog});
                }

                // 检查是否有数据需要提交
                if (postData.length === 0) {
                  this.$message.warning('没有需要提交的记录');
                  return;
                }

                const response = await instance.post(redirectUrl, postData);
                
                // 处理响应数据
                const { total_count, success_count, fail_count } = response.data;
                
                // 显示提交结果弹窗
                this.$alert(
                  `提交结果：\n总条数：${total_count}\n成功：${success_count}\n失败：${fail_count}`,
                  '提交结果',
                  {
                    confirmButtonText: '确定',
                    callback: () => {
                      if (success_count > 0) {
                        this.$message.success("工作日志保存成功");
                        // 获取当前用户名
                        const userName = this.$store.state.userStore.userInfo.nickName;
                        // 清空所有记录
                        this.$store.commit('workLogStore/clearWorkLogs');
                        this.currentWorkLog = {
                          date: new Date().toISOString().split('T')[0],
                          employeeName: userName || '', // 设置用户名
                          taskDescription: '',
                          startTimeObj: '',
                          endTimeObj: '',
                          startTime: '',
                          endTime: '',
                          duration: '',
                          remarks: ''
                        };
                        this.$refs.workLogForm.resetFields();

                        // 保存到localStorage
                        this.saveToLocalStorage();
                      }
                    }
                  }
                );
              } catch (error) {
                this.$message.error("工作日志保存失败：" + (error.message || '未知错误'));
              }
              return;
            }
          }
        }
        this.$message.error("工作日志保存失败：" + (error.message || '未知错误'));
      }
    },
    // 保存到localStorage
    saveToLocalStorage() {
      localStorage.setItem('workLogList', JSON.stringify(this.workLogList));
    },

    // 从localStorage加载
    loadFromLocalStorage() {
      const savedList = localStorage.getItem('workLogList');
      if (savedList) {
        const parsedList = JSON.parse(savedList);
        // 清空当前列表并加载保存的数据
        this.$store.commit('workLogStore/clearWorkLogs');
        parsedList.forEach(item => {
          this.$store.commit('workLogStore/addWorkLog', item);
        });
      }
    },

    // 编辑历史记录
    editHistoryItem(index) {
      // 将选中的历史记录设置为当前编辑的表单
      this.currentWorkLog = {...this.workLogList[index]};
      // 删除该条历史记录
      this.$store.commit('workLogStore/removeWorkLog', index);
      // 保存到localStorage
      this.saveToLocalStorage();
      // 滚动到表单顶部
      this.$nextTick(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    },

    // 删除历史记录
    removeHistoryItem(index) {
      this.$confirm('确认删除该条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.commit('workLogStore/removeWorkLog', index);
        // 保存到localStorage
        this.saveToLocalStorage();
        this.$message.success('删除成功');
      }).catch(() => {
        // 取消删除
      });
    },

    // 设置工作时长
    setDuration(hours) {
      if (!this.currentWorkLog.startTimeObj) {
        this.$message.warning('请先选择开始时间');
        return;
      }

      const startParts = this.currentWorkLog.startTimeObj.split(':');
      const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
      const endMinutes = startMinutes + hours * 60;
      
      // 处理跨天的情况
      const totalMinutes = endMinutes % (24 * 60);
      const endHours = Math.floor(totalMinutes / 60);
      const endMins = totalMinutes % 60;
      
      // 格式化结束时间
      this.currentWorkLog.endTimeObj = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      
      // 计算并设置时长
      this.currentWorkLog.duration = hours + "小时";
      this.currentWorkLog.startTime = this.currentWorkLog.startTimeObj + ":00";
      this.currentWorkLog.endTime = this.currentWorkLog.endTimeObj + ":00";
    },
  },
  watch: {
    '$store.state.userStore.userInfo.nickName': {
      handler(newVal) {
        if (newVal) {
          this.currentWorkLog.employeeName = newVal;
          this.fetchWorkLogCount();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.fetchWorkLogCount();
    // 加载保存的记录
    this.loadFromLocalStorage();
  }
};
</script>

<style lang="scss" scoped>
.work-log {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  
  .work-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #303133;
    }

    .work-log-count {
      .el-tag {
        font-size: 14px;
        padding: 8px 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 120px;
      }
    }
  }

  .work-log-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
    margin-bottom: 20px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #F5F7FA;
      border-radius: 3px;
    }

    .work-log-history {
      margin-top: 30px;
      
      h3 {
        font-size: 16px;
        color: #606266;
        margin-bottom: 15px;
      }

      .history-list {
        .history-item {
          padding: 15px;
          border-bottom: 1px solid #EBEEF5;
          background-color: #f8f9fa;
          margin-bottom: 10px;
          border-radius: 4px;
          
          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }

          .history-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #606266;
            font-size: 14px;
            flex-wrap: wrap;
            gap: 15px;

            .employee {
              font-weight: 500;
              color: #303133;
            }

            .date {
              color: #409EFF;
            }

            .time {
              color: #67C23A;
            }

            .duration {
              color: #E6A23C;
              font-weight: 500;
            }

            .history-actions {
              margin-left: auto;
              display: flex;
              gap: 8px;
            }
          }

          .history-content {
            .task {
              color: #303133;
              margin-bottom: 8px;
              line-height: 1.5;
              white-space: pre-wrap;
            }

            .remarks {
              color: #909399;
              font-size: 13px;
              white-space: pre-wrap;
            }
          }
        }
      }
    }
  }

  .work-log-actions {
    position: sticky;
    bottom: 0;
    text-align: center;
    padding: 10px 0;
    background-color: #fff;
    border-top: 1px solid #EBEEF5;
    z-index: 10;
  }

  .time-range-container {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .time-pickers {
      display: flex;
      align-items: center;
      gap: 10px;

      .time-separator {
        color: #606266;
        font-size: 14px;
      }
    }

    .quick-duration {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 5px;

      .el-button {
        margin: 0;
      }
    }
  }
}

:deep(.time-picker-popper) {
  .el-time-spinner__wrapper {
    overflow-y: auto;
    max-height: 200px;
    
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #F5F7FA;
      border-radius: 3px;
    }
  }
  
  .el-time-spinner__list {
    padding: 0;
    margin: 0;
    list-style: none;
    transition: transform 0.3s ease;
    
    li {
      padding: 8px 0;
      text-align: center;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.active {
        color: #409EFF;
        font-weight: bold;
      }
    }
  }

  .el-time-spinner__item {
    &.active:not(.disabled) {
      color: #409EFF;
      font-weight: bold;
    }
    
    &:hover:not(.disabled) {
      background-color: #f5f7fa;
    }
  }

  // 移除箭头相关样式
  .el-time-spinner__arrow {
    display: none;
  }

  // 优化滑动效果
  .el-time-spinner__item {
    transition: all 0.3s ease;
    
    &.up {
      transform: translateY(-100%);
    }
    
    &.down {
      transform: translateY(100%);
    }
  }

  // 添加惯性滚动效果
  .el-time-spinner__wrapper {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

.task-description-container {
  position: relative;
  
  .add-record-btn {
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 1;
    padding: 8px 15px;
    font-size: 14px;
    
    i {
      margin-right: 5px;
    }
  }
}
</style> 