<template>
    <div class="memo-container">
      <iframe :src="url" class="iframe-content" frameborder="0"></iframe>
    </div>
  </template>
  
  <script>
  export default {
    name: 'Memo',
    data() {
      return {
        url: ''
      }
    },
    created() {
      const accessToken = sessionStorage.getItem('accessToken');
      let baseUrl = 'http://*************:6188/';
      if (accessToken) {
        if (baseUrl.indexOf('?') > -1) {
          baseUrl += `&accessToken=${encodeURIComponent(accessToken)}`;
        } else {
          baseUrl += `?accessToken=${encodeURIComponent(accessToken)}`;
        }
      }
      this.url = baseUrl;
    }
  }
  </script>
  
  <style scoped lang="scss">
  .memo-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  
    .iframe-content {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
    }
  }
  </style> 