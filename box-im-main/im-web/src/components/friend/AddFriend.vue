<template>
  <el-dialog
      title="添加好友"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
      class="add-friend-dialog"
  >
    <div class="search-container">
      <el-input
          placeholder="输入用户名或昵称搜索"
          v-model="searchText"
          clearable
          @keyup.enter.native="onSearch()"
      >
        <el-button
            slot="append"
            icon="el-icon-search"
            @click="onSearch()"
        />
      </el-input>
    </div>

    <el-scrollbar v-loading="loading" style="height: 400px">
      <template v-if="sortedUsers.length > 0">
        <div
            v-for="user in sortedUsers"
            :key="user.id"
            class="user-item"
            v-show="user.id !== currentUserId"
        >
          <div class="item">
            <div class="avatar">
              <head-image
                  :name="user.nickName"
                  :url="user.headImage"
                  :online="user.online"
              />
            </div>
            <div class="add-friend-text">
              <div class="text-user-name">
                <div>{{ user.userName }}</div>
                <div :class="['online-status', { online: user.online }]">
                  {{ user.online ? '[在线]' : '[离线]' }}
                </div>
              </div>
              <div class="text-nick-name">昵称：{{ user.nickName }}</div>
            </div>
            <el-button
                type="success"
                size="mini"
                v-show="!isFriend(user.id)"
                @click="onAddFriend(user)"
            >
              添加
            </el-button>
            <el-button
                type="info"
                size="mini"
                plain
                disabled
                v-show="isFriend(user.id)"
            >
              已添加
            </el-button>
          </div>
        </div>
      </template>
      <div v-else-if="loading" class="empty-tip">
        <div class="loading-text">加载中...</div>
      </div>
      <div v-else class="empty-tip">
        <el-empty description="暂无用户数据" />
      </div>
    </el-scrollbar>

    <div class="pagination-container" v-if="users.length > 0 && !isSearchMode">
      <el-pagination
          :current-page="pagination.current"
          :page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[6, 12, 24]"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
      />
    </div>
  </el-dialog>
</template>

<script>
import HeadImage from '../common/HeadImage.vue'

export default {
  name: 'AddFriendDialog',
  components: { HeadImage },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchText: '',
      users: [],
      loading: false,
      currentUserId: null,
      isSearchMode: false,
      pagination: {
        current: 1,
        size: 6,
        total: 0
      }
    }
  },
  computed: {
    sortedUsers() {
      if (!this.users || this.users.length === 0) return [];

      return [...this.users].sort((a, b) => {
        const aIsFriend = this.isFriend(a.id);
        const bIsFriend = this.isFriend(b.id);

        if (aIsFriend && !bIsFriend) return 1;
        if (!aIsFriend && bIsFriend) return -1;
        return 0;
      });
    }
  },
  watch: {
    dialogVisible: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.currentUserId = this.$store.state.userStore.userInfo?.id
          this.loadAllUsers()
          this.isSearchMode = false
          this.searchText = ''
        } else {
          this.searchText = ''
          this.pagination.current = 1
          this.users = []
        }
      }
    }
  },
  methods: {
    async loadAllUsers() {
      this.loading = true
      this.isSearchMode = false
      try {
        const data = await this.$http.get('/user/findByName', {
          params: {
            name: ''
          }
        })
        this.users = data.filter(user => user.id !== this.currentUserId)
      } catch (error) {
        console.error('加载用户失败:', error)
        this.$message.error('用户列表加载失败')
      } finally {
        this.loading = false
      }
    },

    async loadUsers() {
      this.loading = true
      try {
        try {
          const data = await this.$http.get('/api/all', {
            params: {
              current: this.pagination.current,
              size: this.pagination.size,
              keyword: this.searchText.trim()
            }
          })

          this.users = data.records.filter(
              user => user.id !== this.currentUserId
          )
          this.pagination.total = data.total
        } catch (apiError) {
          this.onSearch()
        }
      } catch (error) {
        console.error('加载用户失败:', error)
        this.$message.error('用户列表加载失败')
      } finally {
        this.loading = false
      }
    },

    onSearch() {
      this.loading = true
      this.isSearchMode = true
      this.$http({
        url: "/user/findByName",
        method: "get",
        params: {
          name: this.searchText || ''
        }
      }).then((data) => {
        this.users = data.filter(user => user.id !== this.currentUserId)
        this.loading = false
      }).catch(error => {
        console.error('搜索用户失败:', error)
        this.$message.error('搜索失败')
        this.loading = false
      })
    },

    handlePageChange(newPage) {
      this.pagination.current = newPage
      this.loadUsers()
    },

    handleSizeChange(newSize) {
      this.pagination.size = newSize
      this.pagination.current = 1
      this.loadUsers()
    },

    onClose() {
      this.users = []
      this.searchText = ''
      this.$emit('close')
    },

    handleClose(done) {
      this.users = []
      this.searchText = ''
      this.$emit('close')
      done()
    },

    async onAddFriend(user) {
      try {
        await this.$http.post('/friend/add', null, {
          params: { friendId: user.id }
        })

        this.$message.success('添加成功')
        this.$store.commit('addFriend', {
          id: user.id,
          nickName: user.nickName,
          headImage: user.headImage,
          online: user.online
        })

        this.users = [...this.users]
      } catch (error) {
        this.$message.error(error.response?.data?.message || '添加失败')
      }
    },

    isFriend(userId) {
      return this.$store.state.friendStore.friends.some(f => f.id === userId)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-friend-dialog {
  .search-container {
    padding: 0 15px 15px;
  }

  .user-item {
    padding: 0 15px;

    .item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #eee;

      .avatar {
        flex-shrink: 0;
        margin-right: 15px;
      }

      .add-friend-text {
        flex: 1;
        min-width: 0;

        .text-user-name {
          display: flex;
          align-items: center;
          font-size: 14px;
          margin-bottom: 4px;

          .online-status {
            font-size: 12px;
            color: #999;
            margin-left: 8px;

            &.online {
              color: #67c23a;
            }
          }
        }

        .text-nick-name {
          font-size: 12px;
          color: #999;
        }
      }

      .el-button {
        flex-shrink: 0;
        margin-left: 15px;
      }
    }
  }

  .empty-tip {
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-text {
      color: #909399;
      font-size: 14px;
    }
  }

  .pagination-container {
    padding: 15px;
    display: flex;
    justify-content: center;
  }
}
</style>