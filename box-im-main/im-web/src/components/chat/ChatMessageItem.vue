<template>
  <div class="chat-msg-item " :class="active ? 'active' : ''">
    <div class="chat-msg-tip"
         v-if="msgInfo.type == $enums.MESSAGE_TYPE.RECALL || msgInfo.type == $enums.MESSAGE_TYPE.TIP_TEXT">
      {{ msgInfo.content }}

      <!--新增重新编辑按钮，只对自己发送的消息可见-->
      <button
          v-if="msgInfo.type === $enums.MESSAGE_TYPE.RECALL || msgInfo.sendId === mine.id"
          class="reedit-button"
          @click="handleReedit">
        重新编辑
      </button>

    </div>
    <div class="chat-msg-tip" v-if="msgInfo.type == $enums.MESSAGE_TYPE.TIP_TIME">
      {{ $date.toTimeText(msgInfo.sendTime) }}
    </div>
    <div class="chat-msg-normal " v-if="isNormal" :class="{ 'chat-msg-mine': mine }">
      <div class="head-image">
        <head-image :name="showName" :size="38" :url="headImage" :id="msgInfo.sendId"></head-image>
      </div>
      <div class="chat-msg-content">
        <div v-if="mode == 1 && msgInfo.groupId && !msgInfo.selfSend" class="chat-msg-top">
          <span>{{ showName }}</span>
        </div>
        <div v-if="mode == 2" class="chat-msg-top">
          <span>{{ showName }}</span>
          <span>{{ $date.toTimeText(msgInfo.sendTime) }}</span>
        </div>
        <div class="chat-msg-bottom" @contextmenu.prevent="showMessageMenu($event)">
          <div ref="chatMsgBox">
            <div class="chat-msg-text" v-if="msgInfo.type == $enums.MESSAGE_TYPE.TEXT"
                v-html="highlightedContent"></div>
            <div class="chat-msg-image" v-if="msgInfo.type == $enums.MESSAGE_TYPE.IMAGE">
              <div v-loading="loading" element-loading-text="上传中.."
                   element-loading-background="rgba(0, 0, 0, 0.4)">
                <img class="send-image" :src="JSON.parse(msgInfo.content).thumbUrl"
                     @click="showFullImageBox()" loading="lazy" />
              </div>
              <span title="发送失败" v-show="loadFail" @click="onSendFail"
                    class="send-fail el-icon-warning"></span>
            </div>
            <div class="chat-msg-video" v-if="msgInfo.type == $enums.MESSAGE_TYPE.VIDEO">
              <video class="send-video" controls preload="none"
                     :poster="JSON.parse(msgInfo.content).coverUrl"
                     :src="JSON.parse(msgInfo.content).videoUrl" />
              <span title="发送失败" v-show="loadFail" @click="onSendFail"
                    class="send-fail el-icon-warning"></span>
            </div>
            <div class="chat-msg-file" v-if="msgInfo.type == $enums.MESSAGE_TYPE.FILE">
              <div class="chat-file-box" v-loading="loading">
                <div class="chat-file-info">
                  <el-link class="chat-file-name" :underline="true" target="_blank" type="primary"
                           :href="data.url" :download="data.name">{{ data.name }}</el-link>
                  <div class="chat-file-size">{{ fileSize }}</div>
                </div>
                <div class="chat-file-icon">
                  <span type="primary" class="el-icon-document"></span>
                </div>
              </div>
              <span title="发送失败" v-show="loadFail" @click="onSendFail"
                    class="send-fail el-icon-warning"></span>
            </div>
          </div>
          <div class="chat-msg-voice" v-if="msgInfo.type == $enums.MESSAGE_TYPE.AUDIO" @click="onPlayVoice()">
            <audio controls :src="JSON.parse(msgInfo.content).url"></audio>
          </div>
          <div class="chat-action chat-msg-text" v-if="isAction">
						<span v-if="msgInfo.type == $enums.MESSAGE_TYPE.ACT_RT_VOICE" title="重新呼叫"
                  @click="$emit('call')" class="iconfont icon-chat-voice"></span>
            <span v-if="msgInfo.type == $enums.MESSAGE_TYPE.ACT_RT_VIDEO" title="重新呼叫"
                  @click="$emit('call')" class="iconfont icon-chat-video"></span>
            <span>{{ msgInfo.content }}</span>
          </div>
          <div class="quote-message" v-if="msgInfo.quoteMessage"
						@contextmenu.prevent.stop="showQuoteMenu($event)">
						<chat-quote-message :msgInfo="msgInfo.quoteMessage"
							@click.native.stop="$emit('locateQuote', msgInfo)"
							:showName="quoteShowName"></chat-quote-message>
					</div>
          <div class="chat-msg-status" v-if="!isAction && mode==1">
						<span class="chat-readed" v-show="msgInfo.selfSend && !msgInfo.groupId
							&& msgInfo.status == $enums.MESSAGE_STATUS.READED">已读</span>
            <span class="chat-unread" v-show="msgInfo.selfSend && !msgInfo.groupId
							&& msgInfo.status != $enums.MESSAGE_STATUS.READED">未读</span>
          </div>
          <div class="chat-receipt" v-show="msgInfo.receipt && mode==1" @click="onShowReadedBox">
            <span v-if="msgInfo.receiptOk" class="icon iconfont icon-ok" title="全体已读"></span>
            <span v-else>
							<span class="read-count">{{ msgInfo.readedCount }}人已读</span>
							<span class="unread-count" v-if="groupMembers && groupMembers.length > 0">{{ groupMembers.filter(m => !m.quit).length - msgInfo.readedCount }}人未读</span>
						</span>
          </div>
        </div>
      </div>
    </div>
    <right-menu v-show="menu && rightMenu.show" :pos="rightMenu.pos" :items="menuItems"
                @close="rightMenu.show = false" @select="onSelectMenu"></right-menu>
    <chat-group-readed ref="chatGroupReadedBox" :msgInfo="msgInfo" :groupMembers="groupMembers"></chat-group-readed>
  </div>
</template>

<script>
import HeadImage from "../common/HeadImage.vue";
import RightMenu from '../common/RightMenu.vue';
import ChatGroupReaded from './ChatGroupReaded.vue';
import ChatQuoteMessage from "./ChatQuoteMessage.vue";
export default {
  name: "messageItem",
  components: {
    HeadImage,
    RightMenu,
    ChatGroupReaded,
    ChatQuoteMessage
  },
  props: {
    mode: {
      type: Number,
      default: 1
    },
    active: {
      type: Boolean,
      default: false
    },
    mine: {
      type: Boolean,
      required: true
    },
    headImage: {
      type: String,
      required: true
    },
    showName: {
      type: String,
      required: true
    },
    quoteShowName: {
      type: String,
      default: ''
    },
    msgInfo: {
      type: Object,
      required: true
    },
    groupMembers: {
      type: Array
    },
    menu: {
      type: Boolean,
      default: true
    },
    searchText: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      menuItems: [],
      rightMenu: {
        show: false,
        pos: {
          x: 0,
          y: 0
        }
      }
    }
  },
  methods: {
    //新增，重新编辑功能
    handleReedit() {
      // 触发重新编辑事件，将消息传递给父组件
      this.$emit('reedit', this.msgInfo);
    },

    onSendFail() {
      this.$message.error("该文件已发送失败，目前不支持自动重新发送，建议手动重新发送")
    },
    showFullImageBox() {
      let imageUrl = JSON.parse(this.msgInfo.content).originUrl;
      if (imageUrl) {
        this.$store.commit('showFullImageBox', imageUrl);
      }
    },
    onPlayVoice() {
      if (!this.audio) {
        this.audio = new Audio();
      }
      this.audio.src = JSON.parse(this.msgInfo.content).url;
      this.audio.play();
      this.onPlayVoice = 'RUNNING';
    },
    showMenu(e) {
      this.rightMenu.pos = {
        x: e.x,
        y: e.y
      };
      this.rightMenu.show = "true";
    },
    showMessageMenu(e) {
      this.menuItems = [];

      // 添加复制选项（对文本消息）
      if (this.msgInfo.type === this.$enums.MESSAGE_TYPE.TEXT) {
        this.menuItems.push({
          key: 'COPY',
          name: '复制',
          icon: 'el-icon-document-copy'
        });
      }

      this.menuItems.push({
        key: 'DELETE',
        name: '删除'
      });

      if (this.msgInfo.selfSend && this.msgInfo.id > 0) {
        this.menuItems.push({
          key: 'RECALL',
          name: '撤回',
        });
      }
      if (this.$msgType.isNormal(this.msgInfo.type)) {
        this.menuItems.push({
          key: 'QUOTE',
          name: '引用'
        });
      }
      this.showMenu(e);
    },
    showQuoteMenu(e) {
      this.menuItems = [];
      if (this.msgInfo.quoteMessage &&
          this.msgInfo.quoteMessage.status != this.$enums.MESSAGE_STATUS.RECALL) {
        this.menuItems.push({
          key: 'LOCATE_QUOTE',
          name: '定位到原消息'
        });
      }
      this.showMenu(e);
    },
    onSelectMenu(item) {
      // 菜单id转驼峰作为事件key
      let eventKey = item.key.toLowerCase().replace(/_([a-z])/g, (g) => g[1].toUpperCase());

      // 特殊处理复制操作，直接在这里执行，而不是传递给父组件
      if (eventKey === 'copy') {
        this.copy();
      } else {
        // 其他操作传递给父组件处理
        this.$emit(eventKey, this.msgInfo);
      }
    },
    onShowReadedBox() {
      let rect = this.$refs.chatMsgBox.getBoundingClientRect();
      this.$refs.chatGroupReadedBox.open(rect);
    },
    // 新增复制功能的处理方法
    copy() {
      if (this.msgInfo.type === this.$enums.MESSAGE_TYPE.TEXT) {
        // 获取纯文本内容（去除HTML标签和特殊格式）
        const textToCopy = this.msgInfo.content;

        try {
          // 使用clipboard API复制文本
          navigator.clipboard.writeText(textToCopy).then(() => {
            this.$message.success('复制成功');
          }).catch(err => {
            // 备用方法
            this.fallbackCopyTextToClipboard(textToCopy);
          });
        } catch (err) {
          // 对于不支持clipboard API的浏览器，使用备用方法
          this.fallbackCopyTextToClipboard(textToCopy);
        }
      }
    },

    // 备用复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement("textarea");
      textArea.value = text;

      // 确保textarea不可见
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        if (successful) {
          this.$message.success('复制成功');
        } else {
          this.$message.error('复制失败，请手动复制');
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }

      document.body.removeChild(textArea);
    },
  },
  computed: {
    loading() {
      return this.msgInfo.loadStatus && this.msgInfo.loadStatus === "loading";
    },
    loadFail() {
      return this.msgInfo.loadStatus && this.msgInfo.loadStatus === "fail";
    },
    data() {
      return JSON.parse(this.msgInfo.content)
    },
    fileSize() {
      let size = this.data.size;
      if (size > 1024 * 1024) {
        return Math.round(size / 1024 / 1024) + "M";
      }
      if (size > 1024) {
        return Math.round(size / 1024) + "KB";
      }
      return size + "B";
    },
    isAction() {
      return this.$msgType.isAction(this.msgInfo.type);
    },
    isNormal() {
      const type = this.msgInfo.type;
      return this.$msgType.isNormal(type) || this.$msgType.isAction(type)
    },
    // 处理HTML文本，包括链接和表情
    htmlText() {
      let color = this.msgInfo.selfSend ? 'white' : '';
      // 先将换行符转换为<br>标签，并确保连续的空格也能显示
      let text = this.msgInfo.content
        .replace(/\r\n/g, '<br>')  // 处理Windows换行
        .replace(/\n/g, '<br>')    // 处理Unix换行
        .replace(/\r/g, '<br>')    // 处理Mac换行
        .replace(/ /g, '&nbsp;');  // 处理空格
      console.log('原始文本:', JSON.stringify(this.msgInfo.content));
      console.log('处理后的HTML:', text);

      // 然后将链接替换为HTML链接
      text = this.$url.replaceURLWithHTMLLinks(text, color);
      // 最后转换表情符号
      return this.$emo.transform(text);
    },
    // 添加计算属性用于处理高亮显示的内容
    highlightedContent() {
      if (!this.searchText || !this.msgInfo.content) {
        return this.htmlText;
      }

      // 使用正则表达式高亮搜索文本
      try {
        const regex = new RegExp(this.searchText.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
        return this.htmlText.replace(regex, match =>
            `<span class="search-highlight">${match}</span>`
        );
      } catch(e) {
        console.error("搜索高亮处理错误:", e);
        return this.htmlText;
      }
    }
  }
}
</script>

<style lang="scss">
//新增，重新编辑功能样式
.reedit-button {
  margin-left: 8px;
  padding: 2px 8px;
  background-color: var(--im-color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background-color: var(--im-color-primary-light);
  }
}

.active {
  background: #eee;
}


.chat-msg-item {
	padding: 2px 10px;
	border-radius: 10px;
	&.active {
		background: #E1EAF7;
	}

  .chat-msg-tip {
    line-height: 50px;
    font-size: var(--im-font-size-small);
    color: var(--im-text-color-light);
    word-break: break-all;
  }



  .chat-msg-normal {
    position: relative;
    font-size: 0;
    padding-left: 48px;
    min-height: 50px;
    margin:5px 0;

    .head-image {
      position: absolute;
      width: 40px;
      height: 40px;
      top: 0;
      left: 0;
    }

    .chat-msg-content {
      text-align: left;

      .send-fail {
        color: #e60c0c;
        font-size: 30px;
        cursor: pointer;
        margin: 0 20px;
      }

      .chat-msg-top {
        display: flex;
        flex-wrap: nowrap;
        color: var(--im-text-color-light);
        font-size: var(--im-font-size);
        line-height: 20px;

        span {
          margin-right: 12px;
        }
      }

      .chat-msg-bottom {
        display: inline-block;
        padding-right: 300px;
        padding-left: 5px;

        .chat-msg-text {
          position: relative;
          line-height: 26px;
          padding: 6px 10px;
          background-color: var(--im-background);
          border-radius: 10px;
          font-size: var(--im-font-size);
          text-align: left;
          display: block;
          white-space: pre-wrap;
          word-break: break-all;
          overflow-wrap: break-word;
          max-width: 100%;
          min-width: 50px;
          width: fit-content;

          /* 优化链接样式 */
          a {
            display: inline;
            word-break: break-word;
            white-space: normal;
            max-width: 100%;
            vertical-align: baseline;
          }

          /* 确保br标签能够正确换行 */
          br {
            display: block;
            content: "";
            margin-top: 0.5em;
            line-height: 1.5;
          }

          &:after {
            content: "";
            position: absolute;
            left: -10px;
            top: 13px;
            width: 0;
            height: 0;
            border-style: solid dashed dashed;
            border-color: #eee transparent transparent;
            overflow: hidden;
            border-width: 10px;
          }
        }

        .chat-msg-image {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;

          .send-image {
            min-width: 200px;
            min-height: 150px;
            max-width: 400px;
            max-height: 300px;
            border-radius: 8px;
            cursor: pointer;
          }
        }


        .chat-msg-video {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;

          .send-video {
            min-width: 200px;
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            object-fit: contain;
          }
        }


        .chat-msg-file {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;
          cursor: pointer;
          margin-bottom: 2px;

          .chat-file-box {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            min-height: 60px;
            box-shadow: var(--im-box-shadow-light);
            border-radius: 4px;
            padding: 10px 15px;

            .chat-file-info {
              flex: 1;
              height: 100%;
              text-align: left;
              font-size: 14px;
              margin-right: 10px;

              .chat-file-name {
                display: inline-block;
                min-width: 160px;
                max-width: 220px;
                font-size: 14px;
                margin-bottom: 4px;
                white-space: pre-wrap;
                word-break: break-all;
              }

              .chat-file-size {
                font-size: var(--im-font-size-smaller);
                color: var(--im-text-color-light);
              }
            }

            .chat-file-icon {
              font-size: 44px;
              color: #d42e07;
            }
          }

          .send-fail {
            color: #e60c0c;
            font-size: 30px;
            cursor: pointer;
            margin: 0 20px;
          }

        }

        .chat-msg-voice {
          font-size: 14px;
          cursor: pointer;

          audio {
            height: 45px;
            padding: 5px 0;
          }
        }

        .chat-action {
          display: flex;
          align-items: center;

          .iconfont {
            cursor: pointer;
            font-size: 22px;
            padding-right: 8px;
          }
        }

        .quote-message {
          display: block;
          margin-top: 3px;
          cursor: pointer;
        }

        .chat-msg-status {
          display: block;

          .chat-readed {
            font-size: 12px;
            color: var(--im-text-color-light);
          }

          .chat-unread {
            font-size: var(--im-font-size-smaller);
            color: var(--im-color-danger);
          }
        }

        .chat-receipt {
          font-size: var(--im-font-size-smaller);
          cursor: pointer;
          color: var(--im-text-color-light);

          .icon-ok {
            font-size: 20px;
            color: var(--im-color-success);
          }

          .read-count {
            color: var(--im-color-success);
            margin-right: 8px;
          }

          .unread-count {
            color: var(--im-text-color-light);
          }
        }

        .chat-at-user {
          padding: 2px 5px;
          border-radius: 3px;
          cursor: pointer;
        }
      }
    }


    &.chat-msg-mine {
      text-align: right;
      padding-left: 0;
      padding-right: 48px;

      .head-image {
        left: auto;
        right: 0;
      }

      .chat-msg-content {
        text-align: right;

        .chat-msg-top {
          flex-direction: row-reverse;

          span {
            margin-left: 12px;
            margin-right: 0;
          }
        }

        .chat-msg-bottom {
          padding-left: 180px;
          padding-right: 5px;

          .chat-msg-text {
            margin-left: 10px;
            background-color: var(--im-color-primary-light-2);
            color: #fff;
            white-space: pre-wrap;
            word-break: break-word;
            overflow-wrap: break-word;
            width: fit-content;

            &:after {
              left: auto;
              right: -10px;
              border-top-color: var(--im-color-primary-light-2);
            }
          }

          .chat-msg-image {
            flex-direction: row-reverse;
          }

          .chat-msg-video {
            flex-direction: row-reverse;
          }

          .chat-msg-file {
            flex-direction: row-reverse;
          }

          .chat-action {
            flex-direction: row-reverse;

            .iconfont {
              transform: rotateY(180deg);
            }
          }
        }
      }
    }

  }
}

.search-highlight {
  background-color: #ffff00;
  color: #000;
  font-weight: bold;
  padding: 0 2px;
  border-radius: 2px;
}
</style>