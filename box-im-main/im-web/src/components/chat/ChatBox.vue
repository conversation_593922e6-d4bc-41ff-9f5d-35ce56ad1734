<template>
  <div class="chat-box" @click="onClickChatBox()" @mousemove="readedMessage()">
    <el-container>
      <el-header height="50px">
        <span>{{ title }}</span>
        <div v-if="searchText" class="search-navigation">
          <el-button 
            size="mini" 
            :disabled="!hasPreviousMatch" 
            @click="navigateToPreviousMatch"
            icon="el-icon-arrow-up">
            上一条
          </el-button>
          <el-button 
            size="mini" 
            :disabled="!hasNextMatch" 
            @click="navigateToNextMatch"
            icon="el-icon-arrow-down">
            下一条
          </el-button>
          <span class="search-count" v-if="matchedMessageIndices.length > 0">
            {{ currentMatchIndex + 1 }}/{{ matchedMessageIndices.length }}
          </span>
        </div>
        <span title="群聊信息" v-show="this.chat.type == 'GROUP'" class="btn-side el-icon-more"
              @click="showSide = !showSide"></span>
      </el-header>
      <el-main style="padding: 0;">
        <el-container>
          <el-container class="content-box">
            <el-main class="im-chat-main" id="chatScrollBox" @scroll="onScroll">
              <div class="im-chat-box">
                <ul>
                  <!-- <在li里面新增了@reedit="onReeditMessage关于重新编辑撤回消息的 -->
                  <li v-for="(msgInfo, idx) in chat.messages" :key="idx">
                    <chat-message-item v-if="idx >= showMinIdx" :id="msgInfo.id"
                                       @call="onCall(msgInfo.type)" :active="activeMessageIdx == idx"
                                       :mine="msgInfo.sendId == mine.id" :headImage="headImage(msgInfo)"
                                       :showName="showName(msgInfo)" :msgInfo="msgInfo"
                                       :quoteShowName="showName(msgInfo.quoteMessage)" :groupMembers="groupMembers"
                                       :searchText="searchText"
                                       @locateQuote="onLocateQuoteMessage" @delete="onDeleteMessage"
                                       @recall="onRecallMessage" @quote="onQuoteMessage" @reedit="onReeditMessage" @copy="onCopyMessage">
                    </chat-message-item>
                  </li>
                </ul>
              </div>

              <div class="im-chat-box">
								<div v-for="(msgInfo, idx) in chat.messages" :key="idx">
									<chat-message-item v-if="idx >= showMinIdx && (showMaxIdx < 0 || idx < showMaxIdx)"
										:id="msgInfo.id" @call="onCall(msgInfo.type)" :active="activeMessageIdx == idx"
										:mine="msgInfo.sendId == mine.id" :headImage="headImage(msgInfo)"
										:showName="showName(msgInfo)" :msgInfo="msgInfo"
										:quoteShowName="showName(msgInfo.quoteMessage)" :group="group"
										:groupMembers="groupMembers" @locateQuote="onLocateQuoteMessage"
										@delete="onDeleteMessage" @recall="onRecallMessage" @quote="onQuoteMessage"
										@top="onTopMessage" @reedit="onReeditMessage" @copy="onCopyMessage">
									</chat-message-item>
								</div>
							</div>

            </el-main>
            <el-footer height="220px" class="im-chat-footer">
              <div class="chat-tool-bar">
                <div title="表情" class="icon iconfont icon-emoji" ref="emotion"
                     @click.stop="showEmotionBox()">
                </div>
                <div title="发送图片">
                  <file-upload :action="'/image/upload'" :maxSize="20 * 1024 * 1024"
                               :fileTypes="['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif']"
                               @before="onImageBefore" @success="onImageSuccess" @fail="onImageFail">
                    <i class="el-icon-picture-outline"></i>
                  </file-upload>
                </div>
                <div title="发送视频">
                  <file-upload :action="'/video/upload'" :maxSize="500 * 1024 * 1024"
                               :fileTypes="['video/mp4', 'video/ogg', 'video/webm']" @before="onVideoBefore"
                               @success="onVideoSuccess" @fail="onVideoFail">
                    <i class="el-icon-film"></i>
                  </file-upload>
                </div>
                <div title="发送文件">
                  <file-upload ref="fileUpload" :action="'/file/upload'" :maxSize="500 * 1024 * 1024"
                               @before="onFileBefore" @success="onFileSuccess" @fail="onFileFail">
                    <i class="el-icon-wallet"></i>
                  </file-upload>
                </div>
                <div title="回执消息" v-show="chat.type == 'GROUP'" class="icon iconfont icon-receipt"
                     :class="isReceipt ? 'chat-tool-active' : ''" @click="onSwitchReceipt">
                </div>
                <div title="发送语音" class="el-icon-microphone" @click="showRecordBox()">
                </div>
                <div title="语音通话" v-show="chat.type == 'PRIVATE'" class="el-icon-phone-outline"
                     @click="showPrivateVideo('voice')">
                </div>
                <div title="语音通话" v-show="chat.type == 'GROUP'" class="el-icon-phone-outline"
                     @click="onGroupVideo()">
                </div>
                <div title="视频通话" v-show="chat.type == 'PRIVATE'" class="el-icon-video-camera"
                     @click="showPrivateVideo('video')">
                </div>
                <div title="聊天记录" class="el-icon-chat-dot-round" @click="showHistoryBox()"></div>
              </div>
              <div class="send-content-area">
                <ChatInput :ownerId="group.ownerId" ref="chatInput" :group-members="groupMembers"
                           @submit="sendMessage" />
                <div v-if="quoteMessage" class="quote-message">
                  <chat-quote-message :showName="showName(quoteMessage)" :msgInfo="quoteMessage">
                  </chat-quote-message>
                  <div class="quote-remove" @click="onQuoteMessage(null)"><i
                      class="el-icon-close"></i></div>
                </div>
                <div class="send-btn-area">
                  <el-button type="primary" icon="el-icon-s-promotion"
                             @click="notifySend()">发送</el-button>
                </div>
              </div>
            </el-footer>
          </el-container>
          <el-aside class="chat-group-side-box" width="320px" v-if="showSide">
            <chat-group-side :group="group" :groupMembers="groupMembers" @reload="loadGroup(group.id)">
            </chat-group-side>
          </el-aside>
        </el-container>
      </el-main>
      <emotion ref="emoBox" @emotion="onEmotion"></Emotion>
      <chat-record :visible="showRecord" @close="closeRecordBox" @send="onSendRecord"></chat-record>
      <group-member-selector ref="rtcSel" :groupId="group.id" @complete="onInviteOk"></group-member-selector>
      <rtc-group-join ref="rtcJoin" :groupId="group.id"></rtc-group-join>
      <!-- <chat-history :visible="showHistory" :chat="chat" :friend="friend" :group="group"
                    :groupMembers="groupMembers" @close="closeHistoryBox"></chat-history> -->

      <chat-history ref="chatHistory" :chat="chat" :friend="friend" :group="group" @locateQuote="locateMessage"
          :groupMembers="groupMembers"></chat-history>
    
    </el-container>
  </div>
</template>

<script>
import ChatGroupSide from "./ChatGroupSide.vue";
import ChatMessageItem from "./ChatMessageItem.vue";
import FileUpload from "../common/FileUpload.vue";
import Emotion from "../common/Emotion.vue";
import ChatRecord from "./ChatRecord.vue";
import ChatHistory from "./ChatHistory.vue";
import ChatAtBox from "./ChatAtBox.vue"
import GroupMemberSelector from "../group/GroupMemberSelector.vue"
import RtcGroupJoin from "../rtc/RtcGroupJoin.vue"
import ChatInput from "./ChatInput";
import ChatQuoteMessage from "./ChatQuoteMessage.vue";

export default {
  name: "chatPrivate",
  components: {
    ChatInput,
    ChatMessageItem,
    FileUpload,
    ChatGroupSide,
    Emotion,
    ChatRecord,
    ChatHistory,
    ChatAtBox,
    GroupMemberSelector,
    RtcGroupJoin,
    ChatQuoteMessage
  },
  props: {
    chat: {
      type: Object
    }
  },
  data() {
    return {
      userInfo: {},
      group: {},
      groupMembers: [],
      sendImageUrl: "",
      sendImageFile: "",
      placeholder: "",
      isReceipt: true, // 是否回执消息
      showRecord: false, // 是否显示语音录制弹窗
      showSide: false, // 是否显示群聊信息栏
      // showHistory: false, // 是否显示历史聊天记录
      lockMessage: false, // 是否锁定发送，
      showMinIdx: 0, // 下标低于showMinIdx的消息不显示，否则页面会很卡置
      activeMessageIdx: -1, //选择消息idx
      quoteMessage: null, // 被引用的消息
      reqQueue: [], // 等待发送的请求队列
      isSending: false, // 是否正在发
      searchText: "", // 搜索文本
      highlightedMessages: [], // 高亮显示的消息
      matchedMessageIndices: [], // 匹配消息的索引数组
      currentMatchIndex: -1, // 当前匹配消息的索引
    }
  },
  created() {
    // 监听高亮消息事件
    this.$store.watch(
      (state) => state.searchDetail,
      (newVal) => {
        if (newVal) {
          this.onHighlightMessages(newVal);
          this.$store.commit('clearSearchDetail');
        }
      }
    );
  },
  beforeDestroy() {
    // 清除搜索数据
    this.$store.commit('clearSearchDetail');
  },
  methods: {
    // 高亮显示搜索匹配的消息
    onHighlightMessages({searchText, messages}) {
      this.searchText = searchText;
      this.highlightedMessages = messages;
      
      // 获取所有匹配消息的索引
      this.matchedMessageIndices = messages.map(msg => 
        this.chat.messages.findIndex(m => 
          (m.id && m.id === msg.id) || 
          (m.sendTime && m.sendTime === msg.sendTime)
        )
      ).filter(idx => idx >= 0);

      if (this.matchedMessageIndices.length > 0) {
        // 设置当前匹配索引为第一个
        this.currentMatchIndex = 0;
        // 滚动到第一条匹配消息
        this.scrollToMessage(this.matchedMessageIndices[0]);
      }
    },

    // 导航到上一条匹配消息
    navigateToPreviousMatch() {
      if (this.hasPreviousMatch) {
        this.currentMatchIndex--;
        this.scrollToMessage(this.matchedMessageIndices[this.currentMatchIndex]);
      }
    },

    // 导航到下一条匹配消息
    navigateToNextMatch() {
      if (this.hasNextMatch) {
        this.currentMatchIndex++;
        this.scrollToMessage(this.matchedMessageIndices[this.currentMatchIndex]);
      }
    },

    // 滚动到指定索引的消息
    scrollToMessage(index) {
      this.$nextTick(() => {
        // 确保消息可见
        this.showMinIdx = Math.min(this.showMinIdx, Math.max(index - 10, 0));
        
        // 设置活动消息索引以高亮显示
        this.activeMessageIdx = index;
        
        // 滚动到消息位置
        const messageElements = document.querySelectorAll('.im-chat-box li');
        if (messageElements && messageElements[index]) {
          messageElements[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      });
    },

    //新增
    onReeditMessage(msgInfo) {
      // 如果消息不是由当前用户发送的，则不允许编辑
      if (msgInfo.sendId !== this.mine.id) {
        this.$message.warning("只能编辑自己发送的消息");
        return;
      }

      // 检查消息是否有原始内容
      if (!msgInfo.originalContent) {
        this.$message.warning("无法恢复原始消息内容");
        return;
      }

      // 如果是文本消息，直接设置到编辑器中
      if (msgInfo.originalType === this.$enums.MESSAGE_TYPE.TEXT) {
        // 设置输入框内容
        if (this.$refs.chatInput) {
          this.$refs.chatInput.setContent(msgInfo.originalContent);
          this.$refs.chatInput.focus();
        } else {
          this.$message.error("编辑器组件未找到");
        }
      } else {
        // 对于非文本消息，提示用户重新上传
        this.$message.info("非文本消息需要重新上传");
      }

      // 如果原消息有引用，恢复引用
      if (msgInfo.quoteMessage) {
        this.quoteMessage = msgInfo.quoteMessage;
      }
    },

    moveChatToTop() {
      let chatIdx = this.$store.getters.findChatIdx(this.chat);
      this.$store.commit("moveTop", chatIdx);
    },
    onClickChatBox() {
      // 关闭表情窗口
      this.$refs.emoBox.close();
      // 取消消息选中
      this.activeMessageIdx = -1;
    },
    onCall(type) {
      if (type == this.$enums.MESSAGE_TYPE.ACT_RT_VOICE) {
        this.showPrivateVideo('voice');
      } else if (type == this.$enums.MESSAGE_TYPE.ACT_RT_VIDEO) {
        this.showPrivateVideo('video');
      }
    },
    onSwitchReceipt() {
      this.isReceipt = !this.isReceipt;
      this.refreshPlaceHolder();
    },
    onImageSuccess(data, file) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(data);
      msgInfo.receipt = this.isReceipt;
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.$store.commit("insertMessage", [msgInfo, this.chat]);
      })
    },
    onImageFail(e, file) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
    },
    onImageBefore(file) {
      // 被封禁提示
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let url = URL.createObjectURL(file);
      let data = {
        originUrl: url,
        thumbUrl: url
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        fileId: file.uid,
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.IMAGE,
        readedCount: 0,
        loadStatus: "loading",
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
      // // 会话置顶
      // this.moveChatToTop();
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象保存
      file.msgInfo = msgInfo;
    },
    onFileSuccess(url, file) {
      let data = {
        name: file.name,
        size: file.size,
        url: url
      }
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(data);
      msgInfo.receipt = this.isReceipt
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.refreshPlaceHolder();
        this.$store.commit("insertMessage", [msgInfo, this.chat]);
      })
    },
    onFileFail(e, file) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
    },
    onFileBefore(file) {
      // 被封禁提示
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let url = URL.createObjectURL(file);
      let data = {
        name: file.name,
        size: file.size,
        url: url
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.FILE,
        loadStatus: "loading",
        readedCount: 0,
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
      // // 会话置顶
      // this.moveChatToTop();
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象透传
      file.msgInfo = msgInfo;
    },
    onVideoSuccess(data, file) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(data);
      msgInfo.receipt = this.isReceipt;
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.$store.commit("insertMessage", [msgInfo, this.chat]);
      })
    },
    onVideoFail(e, file) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
    },
    onVideoBefore(file) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let url = URL.createObjectURL(file);
      let data = {
        videoUrl: url
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.VIDEO,
        loadStatus: "loading",
        readedCount: 0,
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
      // 会话置顶
      // this.moveChatToTop();
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象透传
      file.msgInfo = msgInfo;
    },
    onCloseSide() {
      this.showSide = false;
    },
    onScroll(e) {
      let scrollElement = e.target
      let scrollTop = scrollElement.scrollTop
      if (scrollTop < 30) { // 在顶部,不滚动的情况
        // 多展示20条信息
        this.showMinIdx = this.showMinIdx > 20 ? this.showMinIdx - 20 : 0;
      }
    },
    showEmotionBox() {
      let width = this.$refs.emotion.offsetWidth;
      let left = this.$elm.fixLeft(this.$refs.emotion);
      let top = this.$elm.fixTop(this.$refs.emotion);
      this.$refs.emoBox.open({
        x: left + width / 2,
        y: top
      })
    },
    onEmotion(emoText) {
      this.$refs.chatInput.insertEmoji(emoText);
    },
    showRecordBox() {
      this.showRecord = true;
    },
    closeRecordBox() {
      this.showRecord = false;
    },
    showPrivateVideo(mode) {
      if (!this.isFriend) {
        this.$message.error("你已不是对方好友,无法呼叫");
        return
      }
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let rtcInfo = {
        mode: mode,
        isHost: true,
        friend: this.friend,
      }
      // 通过home.vue打开单人视频窗口
      this.$eventBus.$emit("openPrivateVideo", rtcInfo);
    },
    onGroupVideo() {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      this.$http({
        url: "/webrtc/group/info?groupId=" + this.group.id,
        method: 'GET'
      }).then((rtcInfo) => {
        if (rtcInfo.isChating) {
          // 已在通话中，可以直接加入通话
          this.$refs.rtcJoin.open(rtcInfo);
        } else {
          // 邀请成员发起通话
          let ids = [this.mine.id];
          let maxChannel = this.$store.state.configStore.webrtc.maxChannel;
          this.$refs.rtcSel.open(maxChannel, ids, ids, []);
        }
      })

    },
    onInviteOk(members) {
      if (members.length < 2) {
        return;
      }
      let userInfos = [];
      members.forEach(m => {
        userInfos.push({
          id: m.userId,
          nickName: m.showNickName,
          headImage: m.headImage,
          isCamera: false,
          isMicroPhone: true
        })
      })
      let rtcInfo = {
        isHost: true,
        groupId: this.group.id,
        inviterId: this.mine.id,
        userInfos: userInfos
      }
      // 通过home.vue打开多人视频窗口
      this.$eventBus.$emit("openGroupVideo", rtcInfo);
    },
    // showHistoryBox() {
    //   this.showHistory = true;
    // },

    showHistoryBox() {
			this.$refs.chatHistory.open();
		},

    // closeHistoryBox() {
    //   this.showHistory = false;
    // },
    onSendRecord(data) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let msgInfo = {
        content: JSON.stringify(data),
        type: 3,
        receipt: this.isReceipt
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      this.sendMessageRequest(msgInfo).then((m) => {
        m.selfSend = true;
        this.$store.commit("insertMessage", [m, this.chat]);
        // 保持输入框焦点
        this.$refs.chatInput.focus();
        // 滚动到底部
        this.scrollToBottom();
        // 关闭录音窗口
        this.showRecord = false;
        this.isReceipt = false;
        this.refreshPlaceHolder();
      })
    },
    fillTargetId(msgInfo, targetId) {
      if (this.chat.type == "GROUP") {
        msgInfo.groupId = targetId;
      } else {
        msgInfo.recvId = targetId;
      }
    },
    notifySend() {
      console.log("notifySend调用");
      if(this.$refs.chatInput) {
        console.log("ChatInput组件引用存在");
        this.$refs.chatInput.submit();
      } else {
        console.error("ChatInput组件引用不存在");
      }
    },
    async sendMessage(fullList) {
      console.log("[ChatBox] sendMessage received fullList:", fullList);

      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      
      // 检查消息是否为空
      if (!fullList || fullList.length === 0) {
        console.warn("[ChatBox] Empty message list received, nothing to send");
        return;
      }

      let textContent = '';
      let atUserIds = [];
      let filesToSend = [];
      let imagesToSend = [];

      // 1. 分离文本、图片和文件
      for (const item of fullList) {
        if (item.type === 'text') {
          textContent += item.content;
          if (item.atUserIds && item.atUserIds.length > 0) {
            atUserIds = [...new Set([...atUserIds, ...item.atUserIds])]; // 合并并去重
          }
        } else if (item.type === 'image' && item.content && item.content.file) {
          imagesToSend.push(item.content.file);
        } else if (item.type === 'file' && item.content && item.content.file) {
          filesToSend.push(item.content.file);
        }
      }

      textContent = textContent.trim();
      console.log(`[ChatBox] Parsed: Text='${textContent}', Images=${imagesToSend.length}, Files=${filesToSend.length}`);

      // 重要：移除提前的输入框清空，确保消息成功发送后再清空
      // 使用临时变量保存需要使用的数据
      let quoteMessageId = this.quoteMessage ? this.quoteMessage.id : null;
      let isReceipt = this.isReceipt && this.chat.type === "GROUP";

      // 2. 发送文本消息 (如果存在)
      if (textContent || (atUserIds.length > 0 && !textContent)) { // 发送条件：有文本，或者只有@信息
        console.log("[ChatBox] Sending text message...");
        try {
            let msgInfo = {
                content: textContent,
                atUserIds: atUserIds,
                type: this.$enums.MESSAGE_TYPE.TEXT,
                receipt: isReceipt,
                quoteMessageId: quoteMessageId
            };
            this.fillTargetId(msgInfo, this.chat.targetId);
       
            // 使用封装好的发送逻辑
            await this.apiRequest('/message/' + this.chat.type.toLowerCase() + '/send', 'post', msgInfo).then((m) => {
                console.log("文本消息发送成功:", m);
                m.selfSend = true;
                this.$store.commit("insertMessage", [m, this.chat]);
            });
             // 清空引用和回执标记 (如果文本消息发送成功)
             this.setQuoteMessage(null);
             this.isReceipt = false;
             this.refreshPlaceHolder();

        } catch (e) {
            console.error("文本消息发送失败:", e);
            // 根据需要处理错误，例如提示用户
            this.$message.error("文本消息发送失败");
            return; // 如果文本发送失败，可能不继续发送图片/文件
        }
      }

      // 3. 独立发送图片消息 (复用 sendImageMessage 逻辑)
      if (imagesToSend.length > 0) {
        console.log("[ChatBox] Sending image messages...");
        for (const imageFile of imagesToSend) {
            try {
                // 注意: sendImageMessage 内部会处理预览、上传和发送
                await this.sendImageMessage(imageFile);
            } catch (e) {
                console.error("图片消息发送失败:", imageFile.name, e);
                // 提示用户哪张图片发送失败
                this.$message.error(`图片 ${imageFile.name} 发送失败`);
            }
        }
      }

      // 4. 独立发送文件消息 (复用 sendFileMessage 逻辑)
      if (filesToSend.length > 0) {
        console.log("[ChatBox] Sending file messages...");
        for (const file of filesToSend) {
            try {
                // 注意: sendFileMessage 内部会处理预览、上传和发送
                await this.sendFileMessage(file);
            } catch (e) {
                console.error("文件消息发送失败:", file.name, e);
                // 提示用户哪个文件发送失败
                 this.$message.error(`文件 ${file.name} 发送失败`);
            }
        }
      }

      // 所有消息发送完成后，再清理输入框和草稿
      console.log("[ChatBox] All messages sent, now clearing input and draft");
      
      // 清空草稿
      this.$store.commit('saveDraft', {
        chatInfo: this.chat,
        text: ''
      });
      
      // 清空输入框内容（只在消息处理完成后执行）
      if (this.$refs.chatInput) {
        this.$refs.chatInput.clear();
      }

      // 保持输入框焦点
      if (this.$refs.chatInput) {
          this.$refs.chatInput.focus();
      }
      // 滚动到底部
      this.scrollToBottom();
      // 关闭录音窗口 (如果打开)
      this.showRecord = false;
      // 清理引用和回执标记 (再次确保，如果只有图片/文件发送)
      this.setQuoteMessage(null);
      this.isReceipt = false;
      this.refreshPlaceHolder();
    },
    sendImageMessage(file) {
      return new Promise((resolve, reject) => {
        this.onImageBefore(file);
        let formData = new FormData()
        formData.append('file', file)
        this.$http.post("/image/upload", formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then((data) => {
          this.onImageSuccess(data, file);
          resolve();
        }).catch((res) => {
          this.onImageFail(res, file);
          reject();
        })
        this.$nextTick(() => this.$refs.chatInput.focus());
        this.scrollToBottom();
      });
    },
    sendTextMessage(sendText, atUserIds) {
      return new Promise((resolve, reject) => {
        if (!sendText.trim()) {
          reject();
        }
        let msgInfo = {
          content: sendText,
          type: 0
        }
        // 填充对方id
        this.fillTargetId(msgInfo, this.chat.targetId);
        // 被@人员列表
        if (this.chat.type == "GROUP") {
          msgInfo.atUserIds = atUserIds;
          msgInfo.receipt = this.isReceipt;
        }
        // 引用消息
        if (this.quoteMessage) {
          msgInfo.quoteMessageId = this.quoteMessage.id
        }
        this.lockMessage = true;
        this.sendMessageRequest(msgInfo).then((m) => {
          m.selfSend = true;
          this.$store.commit("insertMessage", [m, this.chat]);
        }).finally(() => {
          // 解除锁定
          this.scrollToBottom();
          this.isReceipt = false;
          this.quoteMessage = null;
          resolve();
        });
      });
    },
    sendFileMessage(file) {
      return new Promise((resolve, reject) => {
        let check = this.$refs.fileUpload.beforeUpload(file);
        if (check) {
          this.$refs.fileUpload.onFileUpload({ file });
        }
      })
    },
    onDeleteMessage(msgInfo) {
      this.$confirm('确认删除消息?', '删除消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.commit("deleteMessage", [msgInfo, this.chat]);
      });
    },
    onRecallMessage(msgInfo) {
      this.$confirm('确认撤回消息?', '撤回消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let url = `/message/${this.chat.type.toLowerCase()}/recall/${msgInfo.id}`;
        this.$http({
          url: url,
          method: 'delete'
        }).then(() => {
          // 保存原始消息内容用于重新编辑
          const recalledMsg = JSON.parse(JSON.stringify(msgInfo));

          // 对不同类型的消息进行处理
          if (msgInfo.type === this.$enums.MESSAGE_TYPE.TEXT) {
            // 文本消息直接保存原文本内容
            recalledMsg.originalContent = msgInfo.content;
          } else if (msgInfo.type === this.$enums.MESSAGE_TYPE.IMAGE ||
              msgInfo.type === this.$enums.MESSAGE_TYPE.FILE ||
              msgInfo.type === this.$enums.MESSAGE_TYPE.VIDEO ||
              msgInfo.type === this.$enums.MESSAGE_TYPE.AUDIO) {
            // 对于多媒体消息，保存内容但需要提示用户
            recalledMsg.originalContent = "非文本消息已撤回，无法恢复原始内容，请重新发送。";
          }

          recalledMsg.originalType = msgInfo.type;  // 保存原始类型
          recalledMsg.type = this.$enums.MESSAGE_TYPE.RECALL;  // 撤回消息类型
          recalledMsg.content = '你撤回了一条消息';  // 撤回提示文本
          recalledMsg.status = this.$enums.MESSAGE_STATUS.RECALL;

          // 将撤回后的消息插入到聊天记录中
          this.$store.commit("insertMessage", [recalledMsg, this.chat]);
          this.$message.success("消息已撤回");
        });
      });
    },
    onQuoteMessage(msgInfo) {
      this.quoteMessage = msgInfo;

    },
    onLocateQuoteMessage(msgInfo) {
      // 寻找消息位置
      const idx = this.findMessageIdx(msgInfo.quoteMessage);
      if (idx < 0) {
        this.$message.error("无法定位原消息");
        return;
      }
      // 要定位到消息，首先要渲染这条消息
      this.showMinIdx = Math.min(this.showMinIdx, Math.max(idx - 10, 0));
      this.$nextTick(() => {
        document.getElementById(msgInfo.quoteMessage.id).scrollIntoView({ behavior: 'smooth' });
        this.activeMessageIdx = idx;
      })
      this.$refs.chatHistory.close();
    },

    locateMessage(msgInfo) {
			// 寻找消息位置
			const idx = this.findMessageIdx(msgInfo);
			if (idx < 0) {
				this.$message.error("无法定位原消息");
				return;
			}
			// 要定位到消息，首先要渲染这条消息
			if (this.showMinIdx > idx) {
				this.showMinIdx = Math.min(this.showMinIdx, Math.max(idx - 10, 0));
				this.showMaxIdx = this.showMinIdx + 20;
			}
			this.$nextTick(() => {
				document.getElementById(msgInfo.id).scrollIntoView({ behavior: 'smooth' });
				this.activeMessageIdx = idx;
			})
			this.$refs.chatHistory.close();
		},

    findMessageIdx(msgInfo) {
      for (let idx in this.chat.messages) {
        const message = this.chat.messages[idx];
        // 通过id判断
        if (msgInfo.id && message.id && message.id == msgInfo.id) {
          return idx;
        }
        // 正在发送中的消息可能没有id,只有tmpId
        if (msgInfo.tmpId && message.tmpId && message.tmpId == msgInfo.tmpId) {
          return idx;
        }
      }
      return -1;
    },
    readedMessage() {
      if (this.chat.unreadCount == 0) {
        return;
      }
      this.$store.commit("resetUnreadCount", this.chat)
      if (this.chat.type == "GROUP") {
        var url = `/message/group/readed?groupId=${this.chat.targetId}`
      } else {
        url = `/message/private/readed?friendId=${this.chat.targetId}`
      }
      this.$http({
        url: url,
        method: 'put'
      }).then(() => { })
    },
    loadReaded(fId) {
      this.$http({
        url: `/message/private/maxReadedId?friendId=${fId}`,
        method: 'get'
      }).then((id) => {
        this.$store.commit("readedMessage", {
          friendId: fId,
          maxId: id
        });
      });
    },
    loadGroup(groupId) {
      this.$http({
        url: `/group/find/${groupId}`,
        method: 'get'
      }).then((group) => {
        this.group = group;
        this.$store.commit("updateChatFromGroup", group);
        this.$store.commit("updateGroup", group);
      });
      this.$http({
        url: `/group/members/${groupId}`,
        method: 'get'
      }).then((groupMembers) => {
        this.groupMembers = groupMembers;
      });
    },
    updateFriendInfo() {
      if (this.isFriend) {
        // store的数据不能直接修改，深拷贝一份store的数据
        let friend = JSON.parse(JSON.stringify(this.friend));
        friend.headImage = this.userInfo.headImageThumb;
        friend.nickName = this.userInfo.nickName;
        friend.showNickName = friend.remarkNickName ? friend.remarkNickName : friend.nickName;
        this.$store.commit("updateChatFromFriend", friend);
        this.$store.commit("updateFriend", friend);
      }
    },
    loadFriend(friendId) {
      // 获取好友信息
      this.$http({
        url: `/user/find/${friendId}`,
        method: 'GET'
      }).then((userInfo) => {
        this.userInfo = userInfo;
        this.updateFriendInfo();
      })
    },
    showName(msgInfo) {
      if (!msgInfo)
        return '';
      if (this.chat.type == 'GROUP') {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.showNickName : "";
      } else {
        return msgInfo.selfSend ? this.mine.nickName : this.chat.showName      }
    },
    headImage(msgInfo) {
      if (this.chat.type == 'GROUP') {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.headImage : "";
      } else {
				return msgInfo.selfSend ? this.mine.headImageThumb : this.chat.headImage
      }
    },
    resetEditor() {
      this.$nextTick(() => {
        this.$refs.chatInput.clear();
        this.$refs.chatInput.focus();
      });
    },
    scrollToBottom() {
      this.$nextTick(() => {
        let div = document.getElementById("chatScrollBox");
        div.scrollTop = div.scrollHeight;
      });
    },
    refreshPlaceHolder() {
      if (this.isReceipt) {
        this.placeholder = "【回执消息】"
      } else if (this.$refs.editBox && this.$refs.editBox.innerHTML) {
        this.placeholder = ""
      } else {
        this.placeholder = "聊点什么吧~";
      }
    },
    sendMessageRequest(msgInfo) {
      return new Promise((resolve, reject) => {
        // 请求入队列，防止请求"后发先至"，导致消息错序
        this.reqQueue.push({ msgInfo, resolve, reject });
        this.processReqQueue();
      })
    },
    processReqQueue() {
      if (this.reqQueue.length && !this.isSending) {
        this.isSending = true;
        const reqData = this.reqQueue.shift();
        this.$http({
          url: this.messageAction,
          method: 'post',
          data: reqData.msgInfo
        }).then((res) => {
          reqData.resolve(res)
        }).catch((e) => {
          reqData.reject(e)
        }).finally(() => {
          this.isSending = false;
          // 发送下一条请求
          this.processReqQueue();
        })
      }
    },
    showBannedTip() {
      let msgInfo = {
        tmpId: this.generateId(),
        sendId: this.mine.id,
        sendTime: new Date().getTime(),
        type: this.$enums.MESSAGE_TYPE.TIP_TEXT
      }
      if (this.chat.type == "PRIVATE") {
        msgInfo.recvId = this.mine.id
        msgInfo.content = "该用户已被管理员封禁,原因:" + this.userInfo.reason
      } else {
        msgInfo.groupId = this.group.id;
        msgInfo.content = "本群聊已被管理员封禁,原因:" + this.group.reason
      }
      this.$store.commit("insertMessage", [msgInfo, this.chat]);
    },
    generateId() {
      // 生成临时id
      return String(new Date().getTime()) + String(Math.floor(Math.random() * 1000));
    },
    onCopyMessage(msgInfo) {
      // 直接调用子组件的复制方法
      // 这里我们不需要实现具体的复制功能，因为子组件已经实现了
      // 此方法主要是为了处理从子组件传递过来的copy事件
      console.log('复制消息:', msgInfo.id);
    },
    // 将fullList中的内容转换为可发送的消息对象
    createTextMessage(fullList) {
      console.log("createTextMessage 被调用，fullList:", fullList);

      if (!fullList || fullList.length === 0) {
        console.error("fullList为空或不存在");
        return null;
      }

      // 创建基本消息对象
      let msgInfo = {
        type: 0, // 默认为文本消息
        content: "",
        atUserIds: []
      };

      // 加入回执标记
      if (this.isReceipt && this.chat.type === "GROUP") {
        msgInfo.receipt = this.isReceipt;
      }

      // 处理引用消息
      if (this.quoteMessage) {
        msgInfo.quoteMessageId = this.quoteMessage.id;
      }

      // 处理fullList中的每一项
      for (let i = 0; i < fullList.length; i++) {
        let item = fullList[i];
        console.log("处理fullList项:", item);

        if (item.type === 'text') {
          msgInfo.content += item.content;
          // 合并@用户ID列表
          if (item.atUserIds && item.atUserIds.length > 0) {
            msgInfo.atUserIds = msgInfo.atUserIds.concat(item.atUserIds);
          }
        } else if (item.type === 'image') {
          // 图片消息特殊处理，单独发送
          let imgMsgInfo = {
            content: JSON.stringify(item.content),
            type: this.$enums.MESSAGE_TYPE.IMAGE
          };
          console.log("创建图片消息:", imgMsgInfo);
          return imgMsgInfo;
        } else if (item.type === 'file') {
          // 文件消息特殊处理，单独发送
          let fileMsgInfo = {
            content: JSON.stringify(item.content),
            type: this.$enums.MESSAGE_TYPE.FILE
          };
          console.log("创建文件消息:", fileMsgInfo);
          return fileMsgInfo;
        }
      }

      // 检查内容是否为空
      if (!msgInfo.content || msgInfo.content.trim() === '') {
        if (!msgInfo.atUserIds || msgInfo.atUserIds.length === 0) {
          console.error("消息内容为空且没有@用户");
          return null;
        }
      }

      console.log("创建的最终消息对象:", msgInfo);
      return msgInfo;
    },
    // API请求方法
    apiRequest(url, method, data) {
      console.log(`执行API请求: ${method.toUpperCase()} ${url}`, data);
      return new Promise((resolve, reject) => {
        this.$http({
          url: url,
          method: method,
          data: data
        }).then(res => {
          console.log("API请求成功:", res);
          resolve(res);
        }).catch(err => {
          console.error("API请求失败:", err);
          reject(err);
        });
      });
    },
    // 设置引用消息
    setQuoteMessage(message) {
      console.log("设置引用消息:", message);
      this.quoteMessage = message;
    },
    showClearDataConfirm() {
      this.$confirm('此操作将清除所有本地数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除 IndexedDB
        const request = indexedDB.deleteDatabase('im');
        request.onsuccess = () => {
          this.$message.success('本地数据已清除');
          // 刷新页面
          window.location.reload();
        };
        request.onerror = () => {
          this.$message.error('清除失败');
        };
      }).catch(() => {
        this.$message.info('已取消清除');
      });
    },
  },
  computed: {
    mine() {
      return this.$store.state.userStore.userInfo;
    },
    isFriend() {
      let friends = this.$store.state.friendStore.friends;
      return friends.some((f) => f.id == this.userInfo.id)
    },
    friend() {
      let friends = this.$store.state.friendStore.friends;
      return friends.find((f) => f.id == this.userInfo.id);
    },
    title() {
      let title = this.chat.showName;
      if (this.chat.type == "GROUP") {
        let size = this.groupMembers.filter(m => !m.quit).length;
        title += `(${size})`;
      }
      return title;
    },
    messageAction() {
      return `/message/${this.chat.type.toLowerCase()}/send`;
    },
    unreadCount() {
      return this.chat.unreadCount;
    },
    messageSize() {
      if (!this.chat || !this.chat.messages) {
        return 0;
      }
      return this.chat.messages.length;
    },
    isBanned() {
      return (this.chat.type == "PRIVATE" && this.userInfo.isBanned) ||
          (this.chat.type == "GROUP" && this.group.isBanned)
    },

    //当前匹配的是第几条：currentMatchIndex
    //只要当前不是第一条（索引大于 0），就说明还有"上一条"
    //返回 true 表示"上一条"按钮可以点击
    hasPreviousMatch() {
      return this.currentMatchIndex > 0;
    },

    //匹配的所有消息索引：matchedMessageIndices
    //当前匹配的索引 < 最后一条索引，说明还可以向后
    hasNextMatch() {
      return this.currentMatchIndex < this.matchedMessageIndices.length - 1;
    }
  },
  watch: {
    chat: {
      handler(newChat, oldChat) {
        if (newChat.targetId > 0 && (!oldChat || newChat.type != oldChat.type ||
            newChat.targetId != oldChat.targetId)) {
          if (this.chat.type == "GROUP") {
            this.loadGroup(this.chat.targetId);
          } else {
            this.loadFriend(this.chat.targetId);
            // 加载已读状态
            this.loadReaded(this.chat.targetId)
          }
          // 滚到底部
          this.scrollToBottom();
          this.showSide = false;
          // 消息已读
          this.readedMessage()
          // 初始状态只显示30条消息
          let size = this.chat.messages.length;
          this.showMinIdx = size > 30 ? size - 30 : 0;
          // 重置输入框
          this.resetEditor();
          // 复位回执消息
          this.isReceipt = false;
          // 清空引用消息
          this.quoteMessage = null;
          // 更新placeholder
          this.refreshPlaceHolder();
        }
      },
      immediate: true
    },
    messageSize: {
      handler(newSize, oldSize) {
        if (newSize > oldSize) {
          // 拉至底部
          this.scrollToBottom();
        }
      }
    }
  },
  mounted() {
    // Comment out the non-existent function call
    // this.startLoopTask();

    // 恢复草稿内容
    // this.restoreDraft();

    // 处理快捷键
    document.addEventListener('keydown', this.handleKeyDown);

    let div = document.getElementById("chatScrollBox");
    div.addEventListener('scroll', this.onScroll)
  }
}
</script>

<style lang="scss">
.chat-box {
  position: relative;
  width: 100%;
  background: #fff;

  .el-header {
    display: flex;
    justify-content: space-between;
    padding: 0 12px;
    line-height: 50px;
    font-size: var(--im-font-size-larger);
    border-bottom: var(--im-border);


    .btn-side {
      position: absolute;
      right: 20px;
      line-height: 50px;
      font-size: 20px;
      cursor: pointer;
      color: var(--im-text-color-light);
    }
  }

  .im-chat-main {
    padding: 0 10px;
    background-color: #fff;

    .im-chat-box {
      >ul {
        padding: 0 20px;

        li {
          list-style-type: none;
        }
      }
    }
  }

  .im-chat-footer {
    display: flex;
    flex-direction: column;
    padding: 0;

    .chat-tool-bar {
      display: flex;
      position: relative;
      width: 100%;
      height: 36px;
      text-align: left;
      box-sizing: border-box;
      border-top: var(--im-border);
      padding: 4px 2px 2px 8px;

      >div {
        font-size: 22px;
        cursor: pointer;
        line-height: 30px;
        width: 30px;
        height: 30px;
        text-align: center;
        border-radius: 2px;
        margin-right: 8px;
        color: #999;
        transition: 0.3s;

        &.chat-tool-active {
          font-weight: 600;
          color: var(--im-color-primary);
          background-color: #ddd;
        }
      }

      >div:hover {
        color: #333;
      }
    }

    .send-content-area {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      background-color: white !important;

      .send-text-area {
        box-sizing: border-box;
        padding: 5px;
        width: 100%;
        flex: 1;
        resize: none;
        font-size: 16px;
        outline: none;

        text-align: left;
        line-height: 30px;

        &:before {
          content: attr(placeholder);
          color: gray;
        }

        .at {
          color: blue;
          font-weight: 600;
        }

        .receipt {
          color: darkblue;
          font-size: 15px;
          font-weight: 600;
        }

        .emo {
          width: 30px;
          height: 30px;
          vertical-align: bottom;
        }
      }

      .send-image-area {
        text-align: left;
        border: #53a0e7 solid 1px;

        .send-image-box {
          position: relative;
          display: inline-block;

          .send-image {
            max-height: 180px;
            border: 1px solid #ccc;
            border-radius: 2%;
            margin: 2px;
          }

          .send-image-close {
            position: absolute;
            padding: 3px;
            right: 7px;
            top: 7px;
            color: white;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            background-color: #aaa;
            border-radius: 50%;
            border: 1px solid #ccc;
          }
        }
      }

      .quote-message {
        position: absolute;
        bottom: 10px;
        left: 10px;
        font-size: 14px;
        max-width: 80%;
        border-radius: 5px;

        &:hover .quote-remove {
          display: block;
        }

        .quote-remove {
          display: none;
          position: absolute;
          top: -8px;
          right: -8px;
          width: 20px;
          height: 20px;
          line-height: 20px;
          font-size: 14px;
          color: white;
          border-radius: 50%;
          background: #aaa;
          cursor: pointer;

          &:hover {
            background: #888;
          }

        }
      }

      .send-btn-area {
        padding: 10px;
        position: absolute;
        bottom: 4px;
        right: 6px;
      }
    }
  }

  .chat-group-side-box {
    border-left: var(--im-border);
  }

  .search-navigation {
    position: absolute;
    right: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .search-count {
      color: var(--im-text-color-light);
      font-size: 12px;
    }
  }
}
</style>