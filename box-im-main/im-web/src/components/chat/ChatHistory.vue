<template>
	<el-drawer title="聊天记录" size="700px" :visible.sync="isShow" direction="rtl" :before-close="close">
		<div class="chat-history">
			<div class="search-bar">
				<el-input class="search-text" size="small" placeholder="搜索聊天记录" v-model="searchText"
					@input="onSearchTextChange">
					<i class="el-icon-search el-input__icon" slot="prefix"> </i>
				</el-input>
			</div>
			<div class="chat-tabs" ref="chatTabs">
				<el-tabs v-model="tabName" @tab-click="onTabClick">
					<el-tab-pane label="全部" name="all">
						<el-scrollbar v-if="messageSize > 0" ref="allScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-for="(msgInfo, idx) in messages" :key="idx">
								<chat-history-item v-if="idx >= showMinIdx" @click.native="onClickItem(idx)"
									@contextmenu.prevent.native="onRclickItem($event, idx)"
									@dblclick.native="onDblclickItem(idx)" :active="activeIdx == idx"
									:headImage="headImage(msgInfo)" :showName="showName(msgInfo)"
									:quoteShowName="showName(msgInfo.quoteMessage)" :msgInfo="msgInfo">
								</chat-history-item>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
					<el-tab-pane label="文字" name="text">
						<el-scrollbar v-if="messageSize > 0" ref="textScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-for="(msgInfo, idx) in messages" :key="idx">
								<chat-history-item v-if="idx >= showMinIdx" @click.native="onClickItem(idx)"
									@contextmenu.prevent.native="onRclickItem($event, idx)"
									@dblclick.native="onDblclickItem(idx)" :active="activeIdx == idx"
									:headImage="headImage(msgInfo)" :showName="showName(msgInfo)"
									:quoteShowName="showName(msgInfo.quoteMessage)" :msgInfo="msgInfo">
								</chat-history-item>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
					<el-tab-pane label="图片" name="image">
						<el-scrollbar v-if="messageSize > 0" ref="imageScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-if="tabName == 'image'" class="chat-image-video-list">
								<div v-for="(msgInfo, idx) in messages" :key="idx">
									<div class="chat-image-video" :class="activeIdx == idx ? 'active' : ''"
										@contextmenu.prevent="onRclickItem($event, idx)" @dblclick="onDblclickItem(idx)"
										@click="onClickItem(idx)">
										<img class="image" :src="JSON.parse(msgInfo.content).thumbUrl" loading="lazy"
											@click="showFullImageBox(msgInfo)" />
										<span class="upload-text">{{ showName(msgInfo) }}上传于 {{
											$date.toTimeText(msgInfo.sendTime, true) }} </span>
									</div>
								</div>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
					<el-tab-pane label="文件" name="file">
						<el-scrollbar v-if="messageSize > 0" ref="fileScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-for="(msgInfo, idx) in messages" :key="idx">
								<chat-history-item v-if="idx >= showMinIdx" @click.native="onClickItem(idx)"
									@contextmenu.prevent.native="onRclickItem($event, idx)"
									@dblclick.native="onDblclickItem(idx)" :active="activeIdx == idx"
									:headImage="headImage(msgInfo)" :showName="showName(msgInfo)"
									:quoteShowName="showName(msgInfo.quoteMessage)" :msgInfo="msgInfo">
								</chat-history-item>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
					<el-tab-pane label="语音" name="voice">
						<el-scrollbar v-if="messageSize > 0" ref="voiceScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-for="(msgInfo, idx) in messages" :key="idx">
								<chat-history-item v-if="idx >= showMinIdx" @click.native="onClickItem(idx)"
									@contextmenu.prevent.native="onRclickItem($event, idx)"
									@dblclick.native="onDblclickItem(idx)" :active="activeIdx == idx"
									:headImage="headImage(msgInfo)" :showName="showName(msgInfo)"
									:quoteShowName="showName(msgInfo.quoteMessage)" :msgInfo="msgInfo">
								</chat-history-item>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
					<el-tab-pane label="视频" name="video">
						<el-scrollbar v-if="messageSize > 0" ref="videoScrollBox"
							:style="{ 'height': scrollBoxHeight + 'px' }">
							<div v-if="tabName == 'video'" class="chat-image-video-list">
								<div v-for="(msgInfo, idx) in messages" :key="idx">
									<div class="chat-image-video" :class="activeIdx == idx ? 'active' : ''"
										@contextmenu.prevent="onRclickItem($event, idx)" @dblclick="onDblclickItem(idx)"
										@click="onClickItem(idx)">
										<video class="video" controls preload="none"
											:poster="JSON.parse(msgInfo.content).coverUrl"
											:src="JSON.parse(msgInfo.content).videoUrl" />
										<span class="upload-text">{{ showName(msgInfo) }}上传于 {{
											$date.toTimeText(msgInfo.sendTime, true) }} </span>
									</div>
								</div>
							</div>
						</el-scrollbar>
						<no-data-tip v-else></no-data-tip>
					</el-tab-pane>
				</el-tabs>
			</div>

		</div>
		<right-menu v-show="rightMenu.show" :pos="rightMenu.pos" :items="menuItems" @close="rightMenu.show = false"
			@select="onSelectMenu"></right-menu>
	</el-drawer>
</template>

<script>
import ChatMessageItem from './ChatMessageItem.vue';
import ChatHistoryItem from './ChatHistoryItem.vue';
import RightMenu from '../common/RightMenu.vue';
import NoDataTip from '../common/NoDataTip.vue';
export default {
	name: 'chatHistory',
	components: {
		ChatMessageItem,
		ChatHistoryItem,
		RightMenu,
		NoDataTip
	},
	props: {
		chat: {
			type: Object
		},
		friend: {
			type: Object
		},
		group: {
			type: Object
		},
		groupMembers: {
			type: Array,
		}
	},
	data() {
		return {
			isShow: false,
			tabName: "all",
			initEventTabs: [],
			showMinIdx: 0,
			activeIdx: -1,
			searchText: '',
			scrollBoxHeight: 500,
			menuItems: [{
				key: 'LOCATE_QUOTE',
				name: '在聊天中定位'
			}],
			rightMenu: {
				show: false,
				pos: {
					x: 0,
					y: 0
				}
			}
		}
	},
	methods: {
		open() {
			this.isShow = true;
			this.searchText = '';
			this.tabName = 'all';
			this.resetShowMinIdx();
			this.initEvent();
			this.scrollToBottom();
			// 计算滚动条高度
			this.$nextTick(() => {
				this.scrollBoxHeight = this.$refs.chatTabs.offsetHeight - 80;
			})
		},
		close() {
			this.isShow = false
		},
		onTabClick() {
			this.resetShowMinIdx();
			this.initEvent();
			this.scrollToBottom();
		},
		onScroll(e) {
			let scrollElement = e.target
			let scrollTop = scrollElement.scrollTop
			if (scrollTop < 30) {
				// 多展示20条信息
				this.showMinIdx = this.showMinIdx > 20 ? this.showMinIdx - 20 : 0;
			}
		},
		onSearchTextChange() {
			this.resetShowMinIdx();
		},
		onClickItem(idx) {
			this.activeIdx = idx;
		},
		onDblclickItem(idx) {
			this.activeIdx = idx;
			this.onSelectMenu(this.menuItems[0]);
		},
		onRclickItem(e, idx) {
			this.activeIdx = idx;
			this.showMenu(e)
		},
		onSelectMenu(item) {
			// 菜单id转驼峰作为事件key
			let eventKey = item.key.toLowerCase().replace(/_([a-z])/g, (g) => g[1].toUpperCase());
			this.$emit(eventKey, this.messages[this.activeIdx]);
		},
		resetShowMinIdx() {
			this.showMinIdx = this.messageSize > 30 ? this.messageSize - 30 : 0;
		},
		initEvent() {
			if (this.initEventTabs.indexOf(this.tabName) >= 0) {
				return;
			}
			this.$nextTick(() => {
				let scrollBoxName = this.tabName + "ScrollBox";
				let scrollWrap = this.$refs[scrollBoxName].$el.querySelector('.el-scrollbar__wrap');
				scrollWrap.addEventListener('scroll', this.onScroll);
				this.initEventTabs.push(this.tabName);
			})
		},
		showName(msgInfo) {
			if (!msgInfo)
				return '';
			if (this.isGroup) {
				let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
				return member ? member.showNickName : "";
			} else {
				return msgInfo.selfSend ? this.mine.nickName : this.chat.showName
			}
		},
		headImage(msgInfo) {
			if (this.isGroup) {
				let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
				return member ? member.headImage : "";
			} else {
				return msgInfo.selfSend ? this.mine.headImageThumb : this.chat.headImage
			}
		},
		showMenu(e) {
			this.rightMenu.pos = {
				x: e.x,
				y: e.y
			};
			this.rightMenu.show = "true";
		},
		showFullImageBox(msgInfo) {
			let imageUrl = JSON.parse(msgInfo.content).originUrl;
			if (imageUrl) {
				this.$store.commit('showFullImageBox', imageUrl);
			}
		},
		scrollToBottom() {
			this.$nextTick(() => {
				let scrollBoxName = this.tabName + "ScrollBox";
				let scrollWrap = this.$refs[scrollBoxName].$el.querySelector('.el-scrollbar__wrap');
				scrollWrap.scrollTop = scrollWrap.scrollHeight;
			});
		}
	},
	computed: {
		mine() {
			return this.$store.state.userStore.userInfo;
		},
		isGroup() {
			return this.chat.type == 'GROUP';
		},
		allMessage() {
			return this.chat.messages.filter(m => this.$msgType.isNormal(m.type));
		},
		imageMessage() {
			let type = this.$enums.MESSAGE_TYPE.IMAGE;
			return this.chat.messages.filter(m => m.type == type);
		},
		fileMessage() {
			let type = this.$enums.MESSAGE_TYPE.FILE;
			return this.chat.messages.filter(m => m.type == type);
		},
		videoMessage() {
			let type = this.$enums.MESSAGE_TYPE.VIDEO;
			return this.chat.messages.filter(m => m.type == type);
		},
		voiceMessage() {
			let type = this.$enums.MESSAGE_TYPE.AUDIO;
			return this.chat.messages.filter(m => m.type == type);
		},
		textMessage() {
			let type = this.$enums.MESSAGE_TYPE.TEXT;
			return this.chat.messages.filter(m => m.type == type);
		},
		messages() {
			return this[this.tabName + 'Message'].filter(m => {
				if(!this.searchText){
					return true;
				}
				// 只有文字和文件支持检索
				if (this.tabName != 'all' && this.tabName != 'text' && this.tabName != 'file') {
					return true;
				}
				if (this.$enums.MESSAGE_TYPE.TEXT == m.type) {
					return m.content.toLowerCase().includes(this.searchText.toLowerCase())
				} else if (this.$enums.MESSAGE_TYPE.FILE == m.type) {
					return JSON.parse(m.content).name.toLowerCase().includes(this.searchText.toLowerCase());
				}
				return false;
			});
		},
		messageSize() {
			return this.messages.length;
		}
	}
}
</script>

<style lang="scss">
.chat-history {
	display: flex;
	height: 100%;
	padding: 0 25px;
	flex-direction: column;

	.search-bar {
		margin-bottom: 10px;
	}

	.chat-tabs {
		flex: 1;

		.chat-image-video-list {
			display: flex;
			flex-wrap: wrap;

			.chat-image-video {
				display: flex;
				flex-direction: column;
				padding: 10px;
				width: 140px;
				border-radius: 5px;
				cursor: pointer;

				.image {
					width: 140px;
					height: 140px;
					border-radius: 5px;
					object-fit: cover;
				}

				.video {
					width: 140px;
					height: 140px;
					border-radius: 5px;
					object-fit: cover;
				}

				.upload-text {
					color: var(--im-text-color-light);
					font-size: var(--im-font-size-small);
					margin-top: 5px;
					word-break: break-all;
				}

				&:hover {
					background: #f4f4f4;
				}

				&.active {
					background: #E1EAF7;
				}

			}

		}
	}
}
</style>
