<template>
	<div class="chat-group-side">
		<div v-show="!group.quit" class="group-side-search">
			<el-input placeholder="搜索群成员" v-model="searchText" size="small">
				<i class="el-icon-search el-input__icon" slot="prefix"> </i>
			</el-input>
		</div>
		<div class="group-side-scrollbar">
			<div v-show="!group.quit" class="group-side-member-list">
				<div class="member-tools">
					<div class="tool-btn" title="邀请好友进群聊" @click="showAddGroupMember = true">
						<i class="el-icon-plus"></i>
					</div>
					<div class="tool-text">邀请</div>
					<add-group-member :visible="showAddGroupMember" :groupId="group.id" :members="groupMembers"
						@reload="$emit('reload')" @close="showAddGroupMember = false"></add-group-member>
				</div>
				<div class="member-tools" v-if="isOwner">
					<div class="tool-btn" title="禁言" @click="onMuted()">
						<span class="icon iconfont icon-chat-muted" style="font-size: 16px"></span>
					</div>
					<div class="tool-text">禁言</div>
					<group-member-selector ref="mutedSelector" title="选择成员进行禁言" :groupId="group.id"
						@complete="onMutedComplete"></group-member-selector>
				</div>
				<div class="member-tools" v-if="isOwner">
					<div class="tool-btn" title="解除禁言" @click="onUnmuted()">
						<span class="icon iconfont icon-chat-unmuted" style="font-size: 16px"></span>
					</div>
					<div class="tool-text">解除禁言</div>
					<group-member-selector ref="unmutedSelector" title="选择成员进行解除禁言" :groupId="group.id"
						@complete="onUnmutedComplete"></group-member-selector>
				</div>
				<div v-for="(member) in groupMembers" :key="member.id">
					<group-member class="group-side-member"
						v-show="!member.quit && member.showNickName.includes(searchText)" :member="member"
						:showDel="false"></group-member>
				</div>
			</div>
			<el-divider v-if="!group.quit" content-position="center"></el-divider>
			<el-form labelPosition="top" class="group-side-form" :model="group" size="small">
				<el-form-item label="全员禁言" title="开启全员禁言后，仅群主可以发言">
					<el-switch :disabled="!isOwner || !editing" v-model="group.isMuted"> </el-switch>
				</el-form-item>
				<el-form-item label="群聊名称">
					<el-input v-model="group.name" :disabled="!isOwner || !editing" maxlength="20"></el-input>
				</el-form-item>
				<el-form-item label="群主">
					<el-input :value="ownerName" disabled></el-input>
				</el-form-item>
				<el-form-item label="群公告">
					<el-input v-model="group.notice" :disabled="!isOwner || !editing" type="textarea"
						maxlength="1024"></el-input>
				</el-form-item>
				<el-form-item label="备注">
					<el-input v-model="group.remarkGroupName" :disabled="!editing" maxlength="20"></el-input>
				</el-form-item>
				<el-form-item label="我在本群的昵称">
					<el-input v-model="group.remarkNickName" :disabled="!editing" maxlength="20"></el-input>
				</el-form-item>
				<div v-show="!group.quit" class="btn-group">
					<el-button v-if="editing" type="success" @click="onSaveGroup()">保存</el-button>
					<el-button v-if="!editing" type="primary" @click="editing = !editing">编辑</el-button>
					<el-button type="danger" v-show="!isOwner" @click="onQuit()">退出群聊</el-button>
				</div>
			</el-form>
		</div>
	</div>
</template>

<script>
import AddGroupMember from '../group/AddGroupMember.vue';
import GroupMember from '../group/GroupMember.vue';
import GroupMemberSelector from '../group/GroupMemberSelector.vue';
export default {
	name: "chatGroupSide",
	components: {
		AddGroupMember,
		GroupMember,
		GroupMemberSelector
	},
	data() {
		return {
			searchText: "",
			editing: false,
			showAddGroupMember: false
		}
	},
	props: {
		group: {
			type: Object
		},
		groupMembers: {
			type: Array
		}
	},
	methods: {
		onClose() {
			this.$emit('close');
		},
		onMuted() {
			// 群主不显示
			let hideIds = [this.group.ownerId]
			let checkedIds = this.groupMembers.filter(m => m.isMuted).map(m => m.userId);
			this.$refs.mutedSelector.open(-1, checkedIds, checkedIds, hideIds);
		},
		onMutedComplete(members) {
			let userIds = members.map(m => m.userId);
			let data = {
				groupId: this.group.id,
				userIds: userIds,
				isMuted: true
			}
			let tip = `您对${userIds.length}位成员进行了禁言`;
			this.sendMemberMuted(data, tip);
		},
		onUnmuted() {
			// 过滤掉未禁言的用户
			const hideIds = this.groupMembers.filter(m => !m.isMuted).map(m => m.userId)
			this.$refs.unmutedSelector.open(-1, [], [], hideIds);
		},
		onUnmutedComplete(members) {
			let userIds = members.map(m => m.userId);
			let data = {
				groupId: this.group.id,
				userIds: userIds,
				isMuted: false
			}
			let tip = `您解除了${userIds.length}位成员的禁言`;
			this.sendMemberMuted(data, tip);
		},
		onSaveGroup() {
			let vo = this.group;
			this.$http({
				url: "/group/modify",
				method: "put",
				data: vo
			}).then((group) => {
				this.editing = !this.editing
				this.$store.commit("updateGroup", group);
				this.$emit('reload');
				this.$message.success("修改成功");
			})
		},
		onQuit() {
			this.$confirm('退出群聊后将不再接受群里的消息，确认退出吗？', '确认退出?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.$http({
					url: `/group/quit/${this.group.id}`,
					method: 'delete'
				}).then(() => {
					this.$store.commit("removeGroup", this.group.id);
					this.$store.commit("activeGroup", -1);
					this.$store.commit("removeGroupChat", this.group.id);
				});
			})
		},
		sendMemberMuted(data, tip) {
			this.$http({
				url: "/group/members/muted",
				method: "put",
				data: data
			}).then(() => {
				this.$emit('reload');
				this.$message.success(tip)
			})
		}
	},
	computed: {
		ownerName() {
			let member = this.groupMembers.find((m) => m.userId == this.group.ownerId);
			return member && member.showNickName;
		},
		isOwner() {
			return this.group.ownerId == this.$store.state.userStore.userInfo.id;
		}

	}
}
</script>

<style lang="scss">
.chat-group-side {
	position: relative;

	.group-side-search {
		padding: 10px;
	}

	.group-side-scrollbar {
		overflow: auto;
	}

	.el-divider--horizontal {
		margin: 0;
	}

	.el-form-item {
		margin-bottom: 0px !important;
	}

	.group-side-member-list {
		padding: 10px;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		font-size: 14px;
		text-align: center;

		.group-side-member {
			margin-left: 5px;
		}

		.member-tools {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 50px;
			margin-left: 5px;

			.tool-btn {
				width: 38px;
				height: 38px;
				line-height: 38px;
				border: var(--im-border);
				font-size: 14px;
				cursor: pointer;
				box-sizing: border-box;

				&:hover {
					border: #aaaaaa solid 1px;
				}
			}

			.tool-text {
				font-size: 12px;
				text-align: center;
				width: 100%;
				height: 30px;
				line-height: 30px;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden
			}
		}
	}

	.group-side-form {
		text-align: left;
		padding: 10px;
		height: 30%;

		.el-form-item {
			margin-bottom: 12px;

			.el-form-item__label {
				padding: 0;
				line-height: 30px;
			}

			.el-textarea__inner {
				min-height: 100px !important;
			}
		}

		.el-input__inner,
		.el-textarea__inner {
			color: var(--im-text-color) !important;
		}


		.btn-group {
			text-align: center;
			margin-top: 12px;
		}
	}

}
</style>