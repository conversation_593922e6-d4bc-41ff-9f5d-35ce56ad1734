<template>
  <div class="chat-input-area">
    <div :class="['edit-chat-container', isEmpty ? '' : 'not-empty']" contenteditable="true" @paste.prevent="onPaste"
         @keydown="onKeydown" @compositionstart="compositionFlag = true" @compositionend="onCompositionEnd"
         @input="onEditorInput" @mousedown="onMousedown" ref="content" @blur="onBlur">
    </div>
    <div v-if="hasDraft" class="draft-indicator">
      <span>草稿</span>
    </div>
    <chat-at-box @select="onAtSelect" :search-text="atSearchText" ref="atBox" :ownerId="ownerId"
                 :members="groupMembers"></chat-at-box>
  </div>
</template>

<script>
import ChatAtBox from "./ChatAtBox";

export default {

  name: "ChatInput",
  components: { ChatAtBox },
  props: {
    ownerId: {
      type: Number,
    },
    groupMembers: {
      type: Array,
    },

  },
  data() {
    return {
      imageList: {},
      fileList: {},
      currentId: 0,
      atSearchText: null,
      compositionFlag: false,
      atIng: false,
      isEmpty: true,
      changeStored: true,
      blurRange: null,
      hasDraft: false,
      draftTimer: null
    }
  },
  
  created() {
    // 在组件创建时开始定时保存草稿
    this.startDraftTimer();
    
    // 监听页面关闭事件，确保草稿保存
    window.addEventListener('beforeunload', this.saveDraftContent);
  },
  
  beforeDestroy() {
    // 在组件销毁前清除定时器
    this.clearDraftTimer();
    
    // 保存最后的草稿
    this.saveDraftContent();
    
    // 移除页面关闭事件监听
    window.removeEventListener('beforeunload', this.saveDraftContent);
  },
  
  watch: {
    // 仅当主动进入聊天时加载草稿，而非初始化界面时
    '$store.state.chatStore.activeChat': {
      handler: function(newChat, oldChat) {
        // 如果是从无聊天切换到有聊天，才加载草稿，避免初始化时加载
        if (newChat && (!oldChat || newChat.targetId !== oldChat.targetId || newChat.type !== oldChat.type)) {
          this.loadDraft(newChat);
        }
      },
      immediate: false // 这里改为false，避免组件创建时立即执行
    }
  },

  methods: {
    // 开始定时保存草稿
    startDraftTimer() {
      this.clearDraftTimer();
      // 每5秒自动保存一次草稿（比微信更频繁保存）
      this.draftTimer = setInterval(() => {
        this.saveDraftContent();
      }, 5000);
    },
    
    // 清除定时器
    clearDraftTimer() {
      if (this.draftTimer) {
        clearInterval(this.draftTimer);
        this.draftTimer = null;
      }
    },
    
    getDomPath(node) {
      const path = [];
      while (node && node !== this.$refs.content) {
        const index = Array.prototype.indexOf.call(node.parentNode?.childNodes || [], node);
        path.unshift(index);
        node = node.parentNode;
      }
      return path;
    },

    getNodeByPath(path) {
      let node = this.$refs.content;
      for (let i = 0; i < path.length; i++) {
        node = node.childNodes[path[i]];
        if (!node) return null;
      }
      return node;
    },

    // 保存草稿内容
    saveDraftContent() {
      if (!this.$refs.content) return;
      
      // 获取编辑器内容
      const content = this.$refs.content.innerHTML;
      const range = window.getSelection()?.getRangeAt(0);
      
      // 更严格地检测内容是否为空
      const isNonEmpty = content && 
                         content.trim() !== "" && 
                         content !== "<br>" && 
                         content !== "&nbsp;" && 
                         content !== "<div>&nbsp;</div>" &&
                         content !== "<div>\u00A0</div>";
      
      // 更新hasDraft状态
      this.hasDraft = isNonEmpty;
      
      // 保存草稿时，确保表情包代码被正确保存
      let draftContent = content;
      if (isNonEmpty) {
        // 将表情图片转换为表情代码
        const emojiElements = this.$refs.content.getElementsByClassName('chat-emoji');
        for (let emoji of emojiElements) {
          const emojiCode = emoji.dataset.emojiCode;
          if (emojiCode) {
            // 将表情图片替换为表情代码，不包含#和;符号
            emoji.outerHTML = emojiCode;
          }
        }
        draftContent = this.$refs.content.innerHTML;
      }
      
      this.$store.commit("saveDraft", {
        chatInfo: this.$store.state.chatStore.activeChat,
        text: isNonEmpty ? draftContent : "",
        range: isNonEmpty && range ? {
          startOffset: range.startOffset,
          endOffset: range.endOffset,
          containerPath: this.getDomPath(range.startContainer)
        } : null
      });
    },
    
    restoreRange(rangeData) {
      const container = this.getNodeByPath(rangeData.containerPath);
      if (!container) return;
      const range = document.createRange();
      const sel = window.getSelection();
      range.setStart(container, rangeData.startOffset);
      range.setEnd(container, rangeData.endOffset);
      sel.removeAllRanges();
      sel.addRange(range);
    },
    
    loadDraft(chatInfo) {
      if (!chatInfo) return;
      const draftData = this.$store.getters.getDraft(chatInfo);
      
      this.$nextTick(() => {
        if (draftData.text) {
          this.hasDraft = true;
          
          // 创建临时div来解析草稿内容
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = draftData.text;
          
          // 将表情代码转换为表情图片
          const textContent = tempDiv.textContent;
          const emojiRegex = /#([\u4E00-\u9FA5]{1,3});/g;
          let match;
          let lastIndex = 0;
          let result = '';
          
          while ((match = emojiRegex.exec(textContent)) !== null) {
            // 添加表情前的文本
            result += textContent.slice(lastIndex, match.index);
            
            // 创建表情图片
            const emojiCode = match[1];
            const emojiElement = document.createElement('img');
            emojiElement.className = 'chat-emoji no-text';
            emojiElement.dataset.emojiCode = emojiCode;
            emojiElement.src = this.$emo.textToUrl(emojiCode);
            
            // 将表情图片转换为HTML字符串
            result += emojiElement.outerHTML;
            
            lastIndex = match.index + match[0].length;
          }
          
          // 添加剩余的文本
          result += textContent.slice(lastIndex);
          
          // 设置内容
          this.setContent(result);
          
          // 尝试恢复光标位置
          if (draftData.range) {
            this.$nextTick(() => {
              this.restoreRange(draftData.range);
            });
          }
        } else {
          this.hasDraft = false;
          // 清空输入框
          if (this.$refs.content) {
            this.clear();
          }
        }
      });
    },

    setContent(content) {
      //没有内容直接返回
      if (!content) return;

      // 获取编辑器容器
      const editorContainer = this.$refs.content;
      if (!editorContainer) {
        console.error('编辑器容器未找到');
        return;
      }

      // 清理旧内容
      this.clear();

      // 如果是纯文本，直接插入文本节点
      if (content.indexOf('<') === -1 && content.indexOf('>') === -1) {
        const textNode = document.createTextNode(content);
        editorContainer.appendChild(textNode);
      } else {
        // 包含HTML，需要解析
        editorContainer.innerHTML = content;
      }

      this.isEmpty = false;

      // 聚焦并将光标移动到末尾
      this.$nextTick(() => {
        this.focus();
        const range = document.createRange();
        const selection = window.getSelection();

        range.selectNodeContents(editorContainer);
        range.collapse(false); // 将范围折叠到末尾

        selection.removeAllRanges();
        selection.addRange(range);
      });
    },

    onPaste(e) {
      this.isEmpty = false;
      
      // 检查localStorage中是否有截图数据
      const screenshotData = localStorage.getItem('screenshot');
      if (screenshotData) {
        try {
          // 从dataURL创建图片对象
          const img = new Image();
          img.src = screenshotData;
          
          img.onload = () => {
            // 创建canvas来转换图片格式
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            
            // 将canvas转换为blob
            canvas.toBlob(blob => {
              // 创建File对象
              const file = new File([blob], `截图_${new Date().getTime()}.png`, { type: 'image/png' });
              
              // 使用现有逻辑处理图片
              let imagePush = {
                fileId: this.generateId(),
                file: file,
                url: URL.createObjectURL(file)
              };
              
              this.imageList[imagePush.fileId] = (imagePush);
              let line = this.newLine();
              let imageElement = document.createElement('img');
              imageElement.className = 'chat-image no-text';
              imageElement.src = imagePush.url;
              imageElement.dataset.imgId = imagePush.fileId;
              line.appendChild(imageElement);
              let after = document.createTextNode('\u00A0');
              line.appendChild(after);
              this.selectElement(after, 1);
              
              // 清除localStorage中的截图数据，避免重复粘贴
              localStorage.removeItem('screenshot');
            }, 'image/png');
          };
          
          // 如果加载有问题，继续处理普通粘贴
          img.onerror = () => {
            this.handleRegularPaste(e);
          };
          
          return;
        } catch (err) {
          console.error('处理localStorage截图失败:', err);
          // 出错时继续处理普通粘贴
        }
      }
      
      this.handleRegularPaste(e);
    },
    
    // 处理常规粘贴操作
    handleRegularPaste(e) {
      let txt = e.clipboardData.getData('Text')
      let range = window.getSelection().getRangeAt(0)
      if (range.startContainer !== range.endContainer || range.startOffset !== range.endOffset) {
        range.deleteContents();
      }
      
      // 粘贴文本内容
      if (txt && typeof (txt) == 'string') {
        let textNode = document.createTextNode(txt);
        range.insertNode(textNode)
        range.collapse();
        return;
      }
      
      let items = (e.clipboardData || window.clipboardData).items
      if (items.length) {
        for (let i = 0; i < items.length; i++) {
          if (items[i].type.indexOf('image') !== -1) {
            let file = items[i].getAsFile();
            if (!file) continue;
            
            let fileId = this.generateId();
            let imagePush = {
              fileId: fileId,
              file: file,
              url: URL.createObjectURL(file)
            };
            
            console.log('保存图片数据:', fileId, imagePush);
            // 使用数组索引作为键，避免对象键问题
            this.imageList[fileId] = imagePush;
            
            let line = this.newLine();
            let imageElement = document.createElement('img');
            imageElement.className = 'chat-image no-text';
            imageElement.src = imagePush.url;
            imageElement.dataset.imgId = fileId;
            line.appendChild(imageElement);
            let after = document.createTextNode('\u00A0');
            line.appendChild(after);
            this.selectElement(after, 1);
          } else {
            let asFile = items[i].getAsFile();
            if (!asFile) {
              continue;
            }
            let fileId = this.generateId();
            let filePush = { fileId: fileId, file: asFile };
            
            console.log('保存文件数据:', fileId, filePush);
            // 使用数组索引作为键，避免对象键问题
            this.fileList[fileId] = filePush;
            
            let line = this.newLine();
            let fileElement = this.createFile(filePush);
            line.appendChild(fileElement);
            let after = document.createTextNode('\u00A0');
            line.appendChild(after);
            this.selectElement(after, 1);
          }
        }
      }
      range.collapse();
    },

    selectElement(element, endOffset) {
      let selection = window.getSelection();
      // 插入元素可能不是立即执行的，vue可能会在插入元素后再更新dom
      this.$nextTick(() => {
        let t1 = document.createRange();
        t1.setStart(element, 0);
        t1.setEnd(element, endOffset || 0);
        if (element.firstChild) {
          t1.selectNodeContents(element.firstChild);
        }
        t1.collapse();
        selection.removeAllRanges();
        selection.addRange(t1);
        // 需要时自动聚焦
        if (element.focus) {
          element.focus();
        }
      })
    },
    onCompositionEnd(e) {
      this.compositionFlag = false;
      this.onEditorInput(e);
    },
    onKeydown(e) {
      if (e.keyCode === 13) {
        e.preventDefault();
        e.stopPropagation();
        if (this.atIng) {
          console.log('选中at的人')
          this.$refs.atBox.select();
          return;
        }
        if (e.ctrlKey) {
          // 使用真实的换行符
          let line = this.newLine();
          // 创建一个包含换行符的文本节点
          let textNode = document.createTextNode('\n');
          // 将换行符插入到不可见空格之前
          line.insertBefore(textNode, line.firstChild);
          // 将光标移动到新行的开始位置
          this.selectElement(line, 0);
        } else {
          // 中文输入标记
          if (this.compositionFlag) {
            return;
          }
          this.submit();
        }
        return;
      }
      // 删除键
      if (e.keyCode === 8) {
        console.log("delete")
        // 等待dom更新
        setTimeout(() => {
          let s = this.$refs.content.innerHTML.trim();
          // 空dom时，需要刷新dom
          console.log(s);
          if (s === '' || s === '<br>' || s === '<div>&nbsp;</div>') {
            // 拼接随机长度的空格，以刷新dom
            this.empty();
            this.isEmpty = true;
            this.selectElement(this.$refs.content);
          } else {
            this.isEmpty = false;
          }
        })
      }
      // at框打开时，上下键移动特殊处理
      if (this.atIng) {
        if (e.keyCode === 38) {
          e.preventDefault();
          e.stopPropagation();
          this.$refs.atBox.moveUp();
        }
        if (e.keyCode === 40) {
          e.preventDefault();
          e.stopPropagation();
          this.$refs.atBox.moveDown();
        }
      }

    },
    onAtSelect(member) {
      this.atIng = false;
      // 选中输入的 @xx 符
      let blurRange = this.blurRange;
      let endContainer = blurRange.endContainer;
      let startOffset = endContainer.data.indexOf("@" + this.atSearchText);
      let endOffset = startOffset + this.atSearchText.length + 1;
      blurRange.setStart(blurRange.endContainer, startOffset);
      blurRange.setEnd(blurRange.endContainer, endOffset);
      blurRange.deleteContents();
      blurRange.collapse();
      console.log("onAtSelect");
      this.focus();
      // 创建元素节点
      let element = document.createElement('SPAN');
      element.className = "chat-at-user";
      element.dataset.id = member.userId;
      element.contentEditable = 'false';
      element.innerText = `@${member.showNickName}`;
      blurRange.insertNode(element);
      // 光标移动到末尾
      blurRange.collapse();

      // 插入空格
      let textNode = document.createTextNode('\u00A0');
      blurRange.insertNode(textNode);

      blurRange.collapse();
      this.atSearchText = "";
      this.selectElement(textNode, 1);
    },
    onEditorInput(e) {
      this.isEmpty = false;
      this.changeStored = false;
      
      // 输入时立即保存草稿，实现实时保存
      this.saveDraftContent();
      
      if (this.$props.groupMembers && !this.compositionFlag) {
        let selection = window.getSelection()
        let range = selection.getRangeAt(0);
        // 截取@后面的名称作为过滤条件，并以空格结束
        let endContainer = range.endContainer;
        let endOffset = range.endOffset;
        let textContent = endContainer.textContent;
        let startIndex = -1;
        for (let i = endOffset; i >= 0; i--) {
          if (textContent[i] === '@') {
            startIndex = i;
            break;
          }
        }
        // 没有at符号，则关闭弹窗
        if (startIndex === -1) {
          this.$refs.atBox.close();
          return;
        }

        let endIndex = endOffset;
        for (let i = endOffset; i < textContent.length; i++) {
          if (textContent[i] === ' ') {
            endIndex = i;
            break;
          }
        }
        this.atSearchText = textContent.substring(startIndex + 1, endIndex).trim();
        // 打开选择弹窗
        if (this.atSearchText == '') {
          this.showAtBox(e)
        }
      }

    },
    // onBlur() {
    //   this.blurRange = window.getSelection().getRangeAt(0);
    //   this.saveDraftContent();
    // },
    
		onBlur() {
			if(!this.atIng){
				this.updateRange();
			}
		},
    onMousedown() {
      if (this.atIng) {
        this.$refs.atBox.close();
        this.atIng = false;
      }
    },
    insertEmoji(emojiText) {
      let emojiElement = document.createElement('img');
      emojiElement.className = 'chat-emoji no-text';
      emojiElement.dataset.emojiCode = emojiText;
      emojiElement.src = this.$emo.textToUrl(emojiText);

      let blurRange = this.blurRange;
      if (!blurRange) {
        this.focus();
        this.updateRange();
        blurRange = this.blurRange;
      }
      if (blurRange.startContainer !== blurRange.endContainer || blurRange.startOffset !== blurRange.endOffset) {
        blurRange.deleteContents();
      }
      blurRange.insertNode(emojiElement);
      blurRange.collapse()

      let textNode = document.createTextNode('\u00A0');
      blurRange.insertNode(textNode)
      blurRange.collapse()

      this.selectElement(textNode);
      this.updateRange();
      this.isEmpty = false;
    },
    generateId() {
      return this.currentId++;
    },
    createFile(filePush) {
      let file = filePush.file;
      let fileId = filePush.fileId;
      let container = document.createElement('div');
      container.className = 'chat-file-container no-text';
      container.contentEditable = 'false';
      container.dataset.fileId = fileId;

      let left = document.createElement('div');
      left.className = 'file-position-left';
      container.appendChild(left);

      let icon = document.createElement('div');
      icon.className = 'el-icon-document';
      left.appendChild(icon);

      let right = document.createElement('div');
      right.className = 'file-position-right';
      container.appendChild(right);

      let fileName = document.createElement('div');
      fileName.className = 'file-name';
      fileName.innerText = file.name;

      let fileSize = document.createElement('div');
      fileSize.className = 'file-size';
      fileSize.innerText = this.sizeConvert(file.size);

      right.appendChild(fileName);
      right.appendChild(fileSize);

      return container;
    },
    sizeConvert(len) {
      if (len < 1024) {
        return len + 'B';
      } else if (len < 1024 * 1024) {
        return (len / 1024).toFixed(2) + 'KB';
      } else if (len < 1024 * 1024 * 1024) {
        return (len / 1024 / 1024).toFixed(2) + 'MB';
      } else {
        return (len / 1024 / 1024 / 1024).toFixed(2) + 'GB';
      }
    },
    updateRange() {
			
			let selection = window.getSelection();
			this.blurRange = selection.getRangeAt(0);
			console.log("updateRange:",this.blurRange )
		},
    newLine() {
      let selection = window.getSelection();
      let range = selection.getRangeAt(0);
      let divElement = document.createElement('div');
      let endContainer = range.endContainer;
      let parentElement = endContainer.parentElement;
      if (parentElement.parentElement === this.$refs.content) {
        divElement.innerHTML = endContainer.textContent.substring(range.endOffset).trim();
        endContainer.textContent = endContainer.textContent.substring(0, range.endOffset);
        // 插入到当前div（当前行）后面
        parentElement.insertAdjacentElement('afterend', divElement);
      } else {
        divElement.innerHTML = '';
        this.$refs.content.append(divElement);
      }
      // 确保新行有一个不可见的空格，以保持光标位置
      let spaceNode = document.createTextNode('\u00A0');
      divElement.appendChild(spaceNode);
      return divElement;
    },
    clear() {
      this.empty();
      this.imageList = {};
      this.fileList = {};
    },
    empty() {
      this.$refs.content.innerHTML = "";
      let line = this.newLine();
      // 不添加默认空格，让输入框保持真正的空状态
      this.$nextTick(() => this.selectElement(line));
    },
    showAtBox(e) {
      this.atIng = true;
      // show之后会自动更新当前搜索的text
      // this.atSearchText = "";
      let selection = window.getSelection()
      let range = selection.getRangeAt(0)
      // 光标所在坐标
      let pos = range.getBoundingClientRect();
      this.$refs.atBox.open({
        x: pos.x,
        y: pos.y
      })
      // 记录光标所在位置
      this.updateRange();
    },
    html2Escape(strHtml) {
      return strHtml.replace(/[<>&"]/g, function (c) {
        return {
          '<': '&lt;',
          '>': '&gt;',
          '&': '&amp;',
          '"': '&quot;'
        }[c];
      });
    },
    submit() {
      // 检查内容是否为空
      if (!this.$refs.content || !this.$refs.content.childNodes || this.$refs.content.childNodes.length === 0) {
        return;
      }
      
      console.log("开始提交消息");
      
      let nodes = this.$refs.content.childNodes;
      let fullList = [];
      let tempText = '';
      let atUserIds = [];

      const each = (nodes) => {
        for (let node of nodes) {
          if (node.nodeType === 1) { // 元素节点
            if (node.className === 'chat-at-user') {
              // 处理@用户
              if (tempText) {
                fullList.push({
                  type: 'text',
                  content: tempText,
                  atUserIds: []
                });
                tempText = '';
              }
              // 将@用户文本添加到消息中
              tempText += node.innerText;
              atUserIds.push(parseInt(node.dataset.id));
            } else if (node.className === 'chat-emoji') {
              // 处理表情
              if (tempText) {
                fullList.push({
                  type: 'text',
                  content: tempText,
                  atUserIds: atUserIds
                });
                tempText = '';
                atUserIds = [];
              }
              // 将表情代码添加到消息中，不包含#和;符号
              tempText += node.dataset.emojiCode;
            } else if (node.className && node.className.indexOf('chat-image') !== -1) {
              // 处理图片
              if (tempText) {
                fullList.push({
                  type: 'text',
                  content: tempText,
                  atUserIds: atUserIds
                });
                tempText = '';
                atUserIds = [];
              }
              
              // 从imageList获取文件数据
              const imgId = node.dataset.imgId;
              console.log('获取图片数据:', imgId, this.imageList);
              
              if (imgId && this.imageList[imgId] && this.imageList[imgId].file) {
                fullList.push({
                  type: 'image',
                  content: {
                    file: this.imageList[imgId].file
                  }
                });
                console.log('图片数据已添加到发送列表', this.imageList[imgId].file.name);
              } else {
                console.error('未找到图片数据:', imgId);
              }
            } else if (node.className && node.className.indexOf('chat-file-container') !== -1) {
              // 处理文件
              if (tempText) {
                fullList.push({
                  type: 'text',
                  content: tempText,
                  atUserIds: atUserIds
                });
                tempText = '';
                atUserIds = [];
              }
              
              // 从fileList获取文件数据
              const fileId = node.dataset.fileId;
              console.log('获取文件数据:', fileId, this.fileList);
              
              if (fileId && this.fileList[fileId] && this.fileList[fileId].file) {
                fullList.push({
                  type: 'file',
                  content: {
                    file: this.fileList[fileId].file
                  }
                });
                console.log('文件数据已添加到发送列表', this.fileList[fileId].file.name);
              } else {
                console.error('未找到文件数据:', fileId);
              }
            } else {
              each(node.childNodes);
            }
          } else if (node.nodeType === 3) { // 文本节点
            tempText += node.textContent || '';
          }
        }
      };
      each(nodes);

      let text = tempText.trim();
      if (text) {
        fullList.push({
          type: 'text',
          content: text,
          atUserIds: atUserIds
        });
      }

      // 检查是否有内容可发送
      if (fullList.length === 0) {
        return;
      }
      
      console.log('准备发送消息:', fullList);
      
      // 重要：先发送消息，再清除状态和内容
      // 1. 发送消息
      this.$emit("submit", fullList);
      
      // 2. 清空草稿状态
      this.hasDraft = false;
      
      // 3. 清空草稿内容
      this.$store.commit("saveDraft", {
        chatInfo: this.$store.state.chatStore.activeChat,
        text: "",
        range: null
      });
      
      // 4. 最后清空输入框
      this.$nextTick(() => {
        this.clear();
      });
    },
    focus() {
      this.$refs.content.focus();
    }

  }
}
</script>

<style lang="scss" scoped>
.chat-input-area {
  position: relative;
  flex: 1;
  
  .draft-indicator {
    position: absolute;
    top: -20px;
    left: 10px;
    background-color: #ffefd5;
    color: #ff8c00;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    z-index: 1;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    
    span {
      font-weight: bold;
    }
  }

  .edit-chat-container {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    outline: none;
    padding: 5px;
    line-height: 1.5;
    font-size: var(--im-font-size);
    text-align: left;
    overflow-y: auto;

    // 单独一行时，无法在前面输入的bug
    >div:before {
      content: "\00a0";
      font-size: 14px;
      position: absolute;
      top: 0;
      left: 0;
    }

    .chat-image {
      display: block;
      max-width: 200px;
      max-height: 100px;
      border: 1px solid #e6e6e6;
      cursor: pointer;
    }

    .chat-emoji {
      width: 30px;
      height: 30px;
      vertical-align: top;
      cursor: pointer;
    }

    .chat-file-container {
      max-width: 65%;
      padding: 10px;
      border: 2px solid #587ff0;
      display: flex;
      background: #eeeC;
      border-radius: 10px;

      .file-position-left {
        display: flex;
        width: 80px;
        justify-content: center;
        align-items: center;

        .el-icon-document {
          font-size: 40px;
          text-align: center;
          color: #d42e07;
        }
      }

      .file-position-right {
        flex: 1;

        .file-name {
          font-size: 16px;
          font-weight: 600;
          color: #66b1ff;
        }

        .file-size {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }

    .chat-at-user {
      color: #00f;
      border-radius: 3px;
    }
  }

  .edit-chat-container>div:nth-of-type(1):after {
    content: '请输入消息（按Ctrl+Enter键换行）';
    color: gray;
  }

  .edit-chat-container.not-empty>div:nth-of-type(1):after {
    content: none;
  }
}
</style>
