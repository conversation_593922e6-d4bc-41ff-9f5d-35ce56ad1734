<template>
  <div class="chat-item" :class="[active ? 'active' : '', chat.isTop ? 'chat-item-top' : '']" @contextmenu.prevent="showRightMenu($event)">
    <div class="chat-left">
      <head-image :url="chat.headImage" :name="chat.showName" :size="42"
                  :id="chat.type == 'PRIVATE' ? chat.targetId : 0" :isShowUserInfo="false"></head-image>
      <div v-show="chat.unreadCount > 0" class="unread-text">{{ chat.unreadCount }}</div>
    </div>
    <div class="chat-right">
      <div class="chat-name">
        <div class="chat-name-text">
          <div>{{ chat.showName }}</div>
          <el-tag v-if="chat.type == 'GROUP'" size="mini" effect="dark">群</el-tag>
          <el-tag v-if="chat.type == 'SYSTEM'" size="mini" color="#de1c1c" effect="dark">官方</el-tag>
          <i v-if="chat.isTop" class="el-icon-top chat-top-icon" title="已置顶"></i>
        </div>
        <div class="chat-time-text">{{ showTime }}</div>
      </div>
      <div class="chat-content">
        <div class="chat-at-text">{{ atText }}</div>
        <div v-if="hasDraft" class="chat-draft-text">[草稿]</div>
        <div class="chat-send-name" v-show="isShowSendName">{{ chat.sendNickName + ':&nbsp;' }}</div>
        <div class="chat-content-text" v-html="$emo.transform(hasDraft ? getDraftPreview(chat.draftText) : chat.lastContent)"></div>
      </div>
    </div>
    <slot></slot>
    <right-menu v-show="rightMenu.show" :pos="rightMenu.pos" :items="rightMenu.items" @close="rightMenu.show = false"
                @select="onSelectMenu"></right-menu>
  </div>
</template>

<script>
import HeadImage from '../common/HeadImage.vue';
import RightMenu from '../common/RightMenu.vue';

export default {
  name: "chatItem",
  components: {
    HeadImage,
    RightMenu
  },
  data() {
    return {
      rightMenu: {
        show: false,
        pos: {
          x: 0,
          y: 0
        },
        items: []
      }
    }
  },
  props: {
    chat: {
      type: Object
    },
    active: {
      type: Boolean
    },
    index: {
      type: Number
    }
  },
  methods: {
    showRightMenu(e) {
      // 动态设置菜单项
      this.rightMenu.items = [{
        key: 'TOP',
        name: this.chat.isTop ? '取消置顶' : '置顶',
        icon: this.chat.isTop ? 'el-icon-bottom' : 'el-icon-top'
      }, {
        key: 'DELETE',
        name: '删除',
        icon: 'el-icon-delete'
      }];

      this.rightMenu.pos = {
        x: e.x,
        y: e.y
      };
      this.rightMenu.show = "true";
    },
    onSelectMenu(item) {
      this.$emit(item.key.toLowerCase(), this.msgInfo);
    },
    
    // 提取草稿预览文本
    getDraftPreview(htmlContent) {
      // 创建临时元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      
      // 获取纯文本内容
      let text = tempDiv.textContent || tempDiv.innerText || '';
      
      // 限制文本长度
      if (text.length > 20) {
        text = text.substring(0, 20) + '...';
      }
      
      return text;
    }
  },
  computed: {
    // 判断是否有有效的草稿内容
    hasDraft() {
      return this.chat.draftText && 
             this.chat.draftText.trim() !== "" && 
             this.chat.draftText !== "<br>" && 
             this.chat.draftText !== "&nbsp;";
    },
    
    isShowSendName() {
      // 如果有草稿，不显示发送者名称
      if (this.hasDraft) {
        return false;
      }
      
      if (!this.chat.sendNickName) {
        return false;
      }
      let size = this.chat.messages.length;
      if (size == 0) {
        return false;
      }
      // 只有群聊的普通消息需要显示名称
      let lastMsg = this.chat.messages[size - 1];
      return this.$msgType.isNormal(lastMsg.type)
    },
    showTime() {
      return this.$date.toTimeText(this.chat.lastSendTime, true)
    },
    atText() {
      if (this.chat.atMe) {
        return "[有人@我]"
      } else if (this.chat.atAll) {
        return "[@全体成员]"
      }
      return "";
    }
  }
}
</script>

<style lang="scss">
.chat-item {
  height: 50px;
  display: flex;
  position: relative;
  padding: 5px 10px;
  align-items: center;
  background-color: var(--im-background);
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    background-color: var(--im-background-active);
  }

  &.active {
    background-color: var(--im-background-active-dark);
  }

  &.chat-item-top {
    background-color: #f0f7ff;
  }

  .chat-left {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .unread-text {
      position: absolute;
      background-color: #f56c6c;
      right: -4px;
      top: -8px;
      color: white;
      border-radius: 30px;
      padding: 1px 5px;
      font-size: 10px;
      text-align: center;
      white-space: nowrap;
      border: 1px solid #f1e5e5;
    }
  }


  .chat-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 10px;
    text-align: left;
    overflow: hidden;

    .chat-name {
      display: flex;
      line-height: 20px;
      height: 20px;

      .chat-name-text {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: var(--im-font-size);
        white-space: nowrap;
        overflow: hidden;

        .el-tag {
          min-width: 22px;
          text-align: center;
          background-color: #2830d3;
          border-radius: 10px;
          border: 0;
          height: 16px;
          line-height: 16px;
          font-size: 10px;
          margin-left: 2px;
          opacity: 0.8;
        }

        .chat-top-icon {
          margin-left: 5px;
          color: var(--im-color-primary);
          font-size: 14px;
        }
      }

      .chat-time-text {
        font-size: 11px;
        color: var(--im-text-color-light);
        opacity: 0.8;
      }
    }

    .chat-content {
      display: flex;
      align-items: center;
      font-size: 11px;
      width: 100%;
      height: 20px;
      overflow: hidden;
      color: var(--im-text-color-light);
      text-overflow: ellipsis;

      .chat-at-text {
        color: var(--im-color-primary);
        font-weight: 600;
        white-space: nowrap;
      }
      
      .chat-draft-text {
        color: #ff8c00;
        font-weight: 600;
        white-space: nowrap;
        margin-right: 4px;
      }

      .chat-content-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      .chat-send-name {
        white-space: nowrap;
      }
    }
  }
}
</style>
