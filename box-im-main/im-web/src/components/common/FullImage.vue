<template>
  <div class="full-image" v-show="visible" :before-close="onClose" :modal="true">
    <div class="mask"></div>
    <div class="image-box" @wheel="handleZoom">
      <img
          ref="image"
          :src="url"
          :style="{
					transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotate}deg)`,
					transition: 'transform 0.2s ease',
				}"
      />
    </div>
    <div class="close" @click="onClose"><i class="el-icon-close"></i></div>

    <div class="controls">
      <button class="control-btn" @click="rotateImage(-90)">↺ 左转</button>
      <button class="control-btn" @click="rotateImage(90)">↻ 右转</button>
    </div>
  </div>
</template>

<script>
export default {
  name: "fullImage",
  data() {
    return {
      scale: 1, // 初始缩放比例
      minScale: 0.5, // 最小缩放比例
      maxScale: 3, // 最大缩放比例
      rotate: 0, // 初始旋转角度
    };
  },
  methods: {
    onClose() {
      this.$emit("close");
    },

    // 处理滚轮事件（放大/缩小）
    handleZoom(event) {
      event.preventDefault(); // 阻止默认滚动行为

      if (event.deltaY < 0) {
        // 向上滚动，放大
        this.scale = Math.min(this.scale + 0.1, this.maxScale);
      } else {
        // 向下滚动，缩小
        this.scale = Math.max(this.scale - 0.1, this.minScale);
      }
    },

    // 旋转图片
    rotateImage(degrees) {
      this.rotate += degrees; // 更新旋转角度
    },
  },
  props: {
    visible: {
      type: Boolean,
    },
    url: {
      type: String,
    },
  },
};

</script>

<style lang="scss">
.full-image {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 9999;

  .mask {
    position: fixed;
    width: 100%;
    height: 100%;
    background: black;
    opacity: 0.5;
  }

  .image-box {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden; /* 防止图片超出容器 */

    img {
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: center center; /* 缩放和旋转中心点 */
      transform: translate(-50%, -50%) scale(1) rotate(0deg); /* 初始状态 */
      max-height: 100%;
      max-width: 100%;
      transition: transform 0.2s ease; /* 平滑过渡效果 */
    }
  }

  .close {
    position: fixed;
    top: 10px;
    right: 10px;
    color: white;
    font-size: 25px;
    cursor: pointer;
  }

  .controls {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;

    .control-btn {
      padding: 10px 20px;
      font-size: 16px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.9);
      }
    }
  }
}
</style>
