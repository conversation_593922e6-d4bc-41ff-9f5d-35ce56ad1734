import http from '../api/httpRequest.js'

export default {
	state: {
		registration: {
			mode: []
		},
		webrtc: {}
	},
	mutations: {
		setConfig(state, config) {
			state.webrtc = config.webrtc;
			state.registration = config.registration;
		},
		clear(state) {
			state.webrtc = {};
			state.registration = {};
		}
	},
	actions: {
		loadConfig(context) {
			return new Promise((resolve, reject) => {
				http({
					url: '/system/config',
					method: 'GET'
				}).then((config) => {
					console.log("系统配置", config)
					context.commit("setConfig", config);
					resolve();
				}).catch((res) => {
					reject(res);
				});
			})
		}
	}

}