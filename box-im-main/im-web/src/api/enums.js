const MESSAGE_TYPE = {
	TEXT: 0,
	IMAGE: 1,
	FILE: 2,
	AUDIO: 3,
	VIDEO: 4,
	RECALL: 10,
	READED: 11,
	RECEIPT: 12,
	TIP_TIME: 20,
	TIP_TEXT: 21,
	LOADING: 30,
	ACT_RT_VOICE: 40,
	ACT_RT_VIDEO: 41,
	USER_BANNED: 50,
	SYSTEM_MESSAGE: 53,
	USER_UNREG: 54,
	RTC_SETUP_VOICE: 100,
	RTC_SETUP_VIDEO: 101,
	RTC_ACCEPT: 102,
	RTC_REJECT: 103,
	RTC_CANCEL: 104,
	RTC_FAILED: 105,
	RTC_HANDUP: 106,
	RTC_OFFER: 107,
	RTC_ANSWER: 108,
	RTC_CANDIDATE: 109,
	RTC_GROUP_SETUP: 200,
	RTC_GROUP_ACCEPT: 201,
	RTC_GROUP_REJECT: 202,
	RTC_GROUP_FAILED: 203,
	RTC_GROUP_CANCEL: 204,
	RTC_GROUP_QUIT: 205,
	RTC_GROUP_INVITE: 206,
	RTC_<PERSON><PERSON><PERSON>_JOIN: 207,
	RTC_GROUP_OFFER: 208,
	RTC_GROUP_ANSWER: 209,
	RTC_GROUP_CANDIDATE: 210,
	RTC_GROUP_DEVICE: 211
}

const RTC_STATE = {
	FREE: 0, //空闲，可以被呼叫
	WAIT_CALL: 1, // 呼叫后等待
	WAIT_ACCEPT: 2, // 被呼叫后等待
	ACCEPTED: 3, // 已接受聊天，等待建立连接
	CHATING: 4 // 聊天中
}

const TERMINAL_TYPE = {
	WEB: 0,
	APP: 1
}

const MESSAGE_STATUS = {
	UNSEND: 0,
	SENDED: 1,
	RECALL: 2,
	READED: 3
}


export {
	MESSAGE_TYPE,
	RTC_STATE,
	TERMINAL_TYPE,
	MESSAGE_STATUS
}