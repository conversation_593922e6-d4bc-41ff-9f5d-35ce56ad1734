// API服务文件 - 处理与后端控制器的交互
import axios, { AxiosResponse } from 'axios';

// 基础URL配置
const BASE_URL = 'http://localhost:8888';
// 在实际部署时可以替换为生产环境地址: http://*************:8888

// 创建axios实例
const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // 支持跨域cookie
  withCredentials: true,
});

// ----------------- 接口定义 -----------------

// 通用响应结构
export interface Result<T> {
  code: number;
  msg: string;
  data: T;
  success: boolean;
}

// API响应DTO
export interface ApiResponseDTO<T> {
  code: number;
  message: string;
  timestamp: string;
  data: T;
}

// 知识库请求DTO
export interface KnowledgeBaseRequestDTO {
  user_name: string;
  instruction: string;
  input: string;
  output: string;
}

// 知识库响应DTO
export interface KnowledgeBaseResponseDTO {
  id: string;
  success: boolean;
  type: string;
  userName: string;
  input: string;
  saveTime: string;
}

// 工作日志请求DTO
export interface WorkLogRequestDTO {
  date: string;
  employeeName: string;
  taskDescription: string;
  startTime: string;
  endTime: string;
  duration?: string;
  remarks?: string;
}

// 工作日志响应DTO
export interface WorkLogResponseDTO {
  id: string;
  success: boolean;
  employeeName: string;
  date: string;
  taskDescription: string;
  duration: string;
  submitTime: string;
}

// ----------------- 知识库API -----------------

/**
 * 保存知识到外部知识库
 * @param data 知识库数据
 * @returns Promise<ApiResponseDTO<KnowledgeBaseResponseDTO>>
 */
export const saveToExternalKnowledgeBase = async (
  data: KnowledgeBaseRequestDTO
): Promise<ApiResponseDTO<KnowledgeBaseResponseDTO>> => {
  try {
    const response: AxiosResponse<Result<ApiResponseDTO<KnowledgeBaseResponseDTO>>> = 
      await apiClient.post('/knowledge/waibu', data);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.msg || '保存到外部知识库失败');
  } catch (error) {
    console.error('保存到外部知识库出错:', error);
    throw error;
  }
};

/**
 * 保存知识到内部知识库
 * @param data 知识库数据
 * @returns Promise<ApiResponseDTO<KnowledgeBaseResponseDTO>>
 */
export const saveToInternalKnowledgeBase = async (
  data: KnowledgeBaseRequestDTO
): Promise<ApiResponseDTO<KnowledgeBaseResponseDTO>> => {
  try {
    const response: AxiosResponse<Result<ApiResponseDTO<KnowledgeBaseResponseDTO>>> = 
      await apiClient.post('/knowledge/neibu', data);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.msg || '保存到内部知识库失败');
  } catch (error) {
    console.error('保存到内部知识库出错:', error);
    throw error;
  }
};

// ----------------- 工作日志API -----------------

/**
 * 提交工作日志
 * @param data 工作日志数据
 * @returns Promise<ApiResponseDTO<WorkLogResponseDTO>>
 */
export const submitWorkLog = async (
  data: WorkLogRequestDTO
): Promise<ApiResponseDTO<WorkLogResponseDTO>> => {
  try {
    const response: AxiosResponse<Result<ApiResponseDTO<WorkLogResponseDTO>>> = 
      await apiClient.post('/api/submit', data);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.msg || '提交工作日志失败');
  } catch (error) {
    console.error('提交工作日志出错:', error);
    throw error;
  }
};

/**
 * 提交工作日志(备用接口)
 * @param data 工作日志数据
 * @returns Promise<ApiResponseDTO<WorkLogResponseDTO>>
 */
export const submitWorkLogAlternative = async (
  data: WorkLogRequestDTO
): Promise<ApiResponseDTO<WorkLogResponseDTO>> => {
  try {
    const response: AxiosResponse<Result<ApiResponseDTO<WorkLogResponseDTO>>> = 
      await apiClient.post('/api/logs', data);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.msg || '提交工作日志(备用)失败');
  } catch (error) {
    console.error('提交工作日志(备用)出错:', error);
    throw error;
  }
};

// ----------------- 辅助函数 -----------------

/**
 * 计算工作时长
 * 与后端保持一致的时长计算逻辑
 */
export const calculateDuration = (startTime: string, endTime: string): string => {
  if (!startTime || !endTime) {
    return '未计算';
  }
  
  try {
    // 解析时间格式 HH:mm:ss
    const startParts = startTime.split(':');
    const endParts = endTime.split(':');
    
    if (startParts.length < 2 || endParts.length < 2) {
      return '未计算';
    }
    
    const startHour = parseInt(startParts[0]);
    const startMin = parseInt(startParts[1]);
    const endHour = parseInt(endParts[0]);
    const endMin = parseInt(endParts[1]);
    
    // 计算总分钟数
    const startTotalMinutes = startHour * 60 + startMin;
    const endTotalMinutes = endHour * 60 + endMin;
    
    // 处理跨天情况
    let totalMinutes = endTotalMinutes - startTotalMinutes;
    if (totalMinutes < 0) {
      totalMinutes += 24 * 60; // 加一天的分钟数
    }
    
    // 计算差值（小时）
    const durationHours = totalMinutes / 60.0;
    
    // 格式化为一位小数
    return `${durationHours.toFixed(1)}小时`;
  } catch (error) {
    console.error('计算工作时长出错', error);
    return '计算错误';
  }
};

export default {
  saveToExternalKnowledgeBase,
  saveToInternalKnowledgeBase,
  submitWorkLog,
  submitWorkLogAlternative,
  calculateDuration
}; 