let replaceURLWithHTMLLinks = (content, color) => {
	// 使用正则表达式匹配更广泛的URL格式(此正则由deepseek生成)
	const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|]|\bwww\.[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
	return content.replace(urlRegex, (url) => {
	    // 如果URL不以http(s)://开头，则添加http://前缀
	    let fullUrl = url;
	    if (!url.startsWith("http")) {
	        fullUrl = "http://" + url;
	    }
	    
	    // 创建更加紧凑的链接样式，完全解决链接换行问题
	    return `<a href="${fullUrl}" target="_blank" style="color: ${color};text-decoration: underline;display: inline;word-break: break-word;white-space: normal;vertical-align: baseline;line-height: inherit;">${url}</a>`;
	});
}

export default {
	replaceURLWithHTMLLinks
}