<template>
  <div class="new-page-container">
    <div class="page-header">
      <el-button type="primary" icon="el-icon-back" size="large" @click="goBack">返回项目列表</el-button>
      <h2>项目新增</h2>
    </div>
    <el-form :model="formData" ref="formRef" label-width="100px" class="project-form">
      <el-form-item label="项目名称" prop="projectName" :rules="[{ required: true, message: '请输入项目名称', trigger: 'blur' }]">
        <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="项目描述" prop="projectDesc">
        <el-input
          type="textarea"
          v-model="formData.projectDesc"
          :autosize="{ minRows: 3, maxRows: 20 }"
          placeholder="请输入项目描述"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startDate" :rules="[{ required: true, message: '请选择开始时间', trigger: 'change' }]">
        <el-date-picker v-model="formData.startDate" type="date" placeholder="选择日期" style="width:100%" />
      </el-form-item>
      <el-form-item label="结束时间" prop="endDate">
        <el-date-picker v-model="formData.endDate" type="date" placeholder="选择日期" style="width:100%" :picker-options="{ disabledDate: d => formData.startDate && d < new Date(formData.startDate) }" />
      </el-form-item>
      <el-form-item label="总价值" prop="totalValue">
        <el-input v-model="formData.totalValue" placeholder="请输入项目总价值" type="number" />
      </el-form-item>
      <el-form-item label="总成本" prop="totalCost">
        <el-input v-model="formData.totalCost" placeholder="请输入项目总成本" type="number" />
      </el-form-item>
      <el-form-item label="当前进度" prop="currentProgress" :rules="[{ required: true, message: '请输入当前进度', trigger: 'blur' }]">
        <el-input v-model="formData.currentProgress" placeholder="请输入当前进度" />
      </el-form-item>
      <el-divider>管理员选择</el-divider>
      <el-form-item label="选择部门" prop="departmentId" :rules="[{ required: true, message: '请选择部门', trigger: 'change' }]">
        <el-select v-model="formData.departmentId" placeholder="请选择部门" @change="handleDepartmentChange" filterable :loading="departmentsLoading">
          <el-option v-for="item in departmentsList" :key="item.id" :label="item.departmentName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择管理员" prop="managerId" :rules="[{ required: true, message: '请选择管理员', trigger: 'change' }]">
        <el-select v-model="formData.managerId" placeholder="请选择管理员" :disabled="!formData.departmentId || employeesLoading" filterable :loading="employeesLoading" @change="handleEmployeeChange">
          <el-option v-for="item in employeesList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.managerId">
        <el-tag type="success">已选管理员: {{ formData.managerName }} ({{ formData.managerWechat || '无微信' }})</el-tag>
      </el-form-item>
      <el-form-item>
        <div class="form-actions">
          <el-button size="large" @click="cancel">取消</el-button>
          <el-button type="primary" size="large" :loading="isSubmitting" @click="submitForm">提交</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'NewPage',
  data() {
    return {
      formData: {
        projectName: '',
        projectDesc: '',
        startDate: '',
        endDate: '',
        totalValue: '',
        totalCost: '',
        currentProgress: '',
        departmentId: '',
        managerId: '',
        managerName: '',
        managerWechat: ''
      },
      departmentsList: [],
      employeesList: [],
      departmentsLoading: false,
      employeesLoading: false,
      isSubmitting: false
    }
  },
  mounted() {
    this.fetchDepartments();
  },
  methods: {
    async fetchDepartments() {
      this.departmentsLoading = true;
      try {
        const res = await axios.get('http://projectmanagement.jenasi.ai:99/departments/selectAll');
        this.departmentsList = res.data.data || [];
      } catch (e) {
        this.$message.error('获取部门列表失败');
      } finally {
        this.departmentsLoading = false;
      }
    },
    async handleDepartmentChange(val) {
      this.formData.managerId = '';
      this.formData.managerName = '';
      this.formData.managerWechat = '';
      this.employeesList = [];
      if (!val) return;
      this.employeesLoading = true;
      try {
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/employees/selectAllByDepartmentId/${val}`);
        this.employeesList = res.data.data || [];
      } catch (e) {
        this.$message.error('获取员工列表失败');
      } finally {
        this.employeesLoading = false;
      }
    },
    handleEmployeeChange(val) {
      const emp = this.employeesList.find(e => e.id === val);
      if (emp) {
        this.formData.managerName = emp.name;
        this.formData.managerWechat = emp.wechatName || '';
      } else {
        this.formData.managerName = '';
        this.formData.managerWechat = '';
      }
    },
    async submitForm() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) {
          console.log('表单验证失败');
          return;
        }
        if (this.formData.startDate && this.formData.endDate && this.formData.startDate > this.formData.endDate) {
          this.$message.error('结束日期不能早于开始日期');
          return;
        }
        this.isSubmitting = true;
        try {
          const payload = { ...this.formData };
          console.log('提交的数据:', payload);
          const res = await axios.post('http://projectmanagement.jenasi.ai:99/project/add', payload);
          console.log('服务器响应:', res.data);
          if (parseInt(res.data.code) !== 200) {
            this.$message.error(res.data.message || '提交失败');
            return;
          }
          this.$message.success('项目创建成功');
          // debugger
          this.$router.push('/home/<USER>/list');
        } catch (e) {
          console.error('提交失败:', e);
          this.$message.error(e.message || '项目创建失败，请重试');
        } finally {
          this.isSubmitting = false;
        }
      });
    },
    cancel() {
      this.$router.push('/home/<USER>/list');
    },
    goBack() {
      this.$router.push('/home/<USER>/list');
    }
  }
}
</script>

<style scoped>
.new-page-container {
  height: 100vh;
  width: 100%;
  margin: 0;
  background: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 24px;
}

.project-form {
  flex: 1;
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  overflow-y: auto;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding-bottom: 20px;
}

.form-actions .el-button {
  min-width: 150px;
  padding: 15px 40px;
  font-size: 16px;
}

/* 自定义滚动条样式 */
.project-form::-webkit-scrollbar {
  width: 8px;
}

.project-form::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.project-form::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.project-form::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .new-page-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }
  
  .project-form {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .form-actions .el-button {
    width: 100%;
  }
}
</style> 