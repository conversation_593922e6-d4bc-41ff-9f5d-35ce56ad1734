<template>
  <div class="project-detail-container">
    <div class="header">
      <el-button type="primary" icon="el-icon-back" @click="goBack">返回项目列表</el-button>
      <h2>{{ projectDetail.projectName }}</h2>
      <el-tag :type="getStatusType(projectDetail.status)">{{ projectDetail.status }}</el-tag>
    </div>

    <div class="detail-wrapper">
      <div class="detail-content">
        <!-- 头部信息区 -->
        <el-card class="header-card">
          <div class="project-title-row">
            <h3 class="project-name">{{ projectDetail.projectName || '未知项目' }}</h3>
            <el-button type="text" icon="el-icon-edit" @click="editProject(projectDetail.projectId)">编辑</el-button>
          </div>
          <div class="project-status-row">
            <el-tag :type="getStatusType(projectDetail.progressStatus)">
              {{ projectDetail.progressStatus || '未知状态' }}
            </el-tag>
          </div>
          <p class="project-desc">{{ projectDetail.projectDesc || '暂无项目描述' }}</p>
        </el-card>

        <!-- 关键信息区 -->
        <el-card class="info-card">
          <div slot="header">
            <span>关键信息</span>
          </div>
          <div class="info-row">
            <i class="el-icon-date"></i>
            <span class="label">时间范围:</span>
            <span class="value">{{ formatDate(projectDetail.startDate,'YYYY-MM-DD') }} 至 {{ formatDate(projectDetail.endDate,'YYYY-MM-DD') }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-user"></i>
            <span class="label">管理员:</span>
            <span class="value">{{ projectDetail.managerName || '未知' }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-chat-dot-round"></i>
            <span class="label">管理员微信:</span>
            <span class="value">{{ projectDetail.managerWechat || '未提供' }}</span>
            <el-button v-if="projectDetail.managerWechat" size="mini" type="primary" @click="copyWechat(projectDetail.managerWechat)">复制</el-button>
          </div>
        </el-card>

        <!-- 项目进度信息区 -->
        <el-card class="progress-card">
          <div slot="header">
            <span>项目进度</span>
          </div>
          <div class="progress-item">
            <span class="label">当前进度:</span>
            <span class="value progress-percentage">{{ projectDetail.currentProgress || '0' }}%</span>
          </div>
          <el-progress 
            :percentage="parseFloat(projectDetail.currentProgress) || 0"
            :status="getProgressStatus(projectDetail.progressStatus)"
            :stroke-width="6"
          />
          <div class="info-row progress-info-row">
            <i class="el-icon-date"></i>
            <span class="label">计划进度:</span>
            <span class="value">{{ projectDetail.plannedProgress || '0' }}%</span>
          </div>
          <el-divider></el-divider>
          <div class="info-row progress-info-row">
            <i class="el-icon-bell"></i>
            <span class="label">进度说明:</span>
            <span class="value">{{ projectDetail.progressDesc || '暂无说明' }}</span>
          </div>
          <el-divider></el-divider>
          <div class="info-row timestamp-row">
            <i class="el-icon-refresh"></i>
            <span class="label">最后更新:</span>
            <span class="value timestamp">{{ formatDate(projectDetail.lastUpdateProgress) }}</span>
          </div>
        </el-card>

        <!-- 计划任务列表区 -->
        <el-card class="tasks-card">
          <div slot="header" class="task-header-row">
            <span>计划任务列表</span>
            <el-button type="text" icon="el-icon-plus" @click="addTask">添加任务</el-button>
          </div>
          <div v-if="taskList && taskList.length > 0">
            <el-card v-for="task in taskList" 
                    :key="task.taskId" 
                    class="task-item"
                    shadow="hover"
                    @click.native="viewTaskDetail(task.taskId)">
              <div class="task-header">
                <i class="el-icon-circle-check" :style="{ color: getStatusColor(task.status) }"></i>
                <span class="task-name">{{ task.taskName || '未知任务' }}</span>
                <div class="task-actions">
                  <el-tag :type="getStatusType(task.status)" size="small">
                    {{ task.status || '未知状态' }}
                  </el-tag>
                  <el-button 
                    type="danger" 
                    size="mini" 
                    icon="el-icon-delete"
                    @click.stop="handleDeleteTask(task.taskId)">
                    删除
                  </el-button>
                </div>
              </div>
              <div class="task-info-grid">
                <div class="task-info-item">
                  <i class="el-icon-user"></i>
                  <span class="task-label">负责人:</span>
                  <span class="task-value">{{ task.ownerName || '未知' }}</span>
                </div>
                <div class="task-info-item">
                  <i class="el-icon-date"></i>
                  <span class="task-label">计划:</span>
                  <span class="task-value">{{ formatDate(task.plannedStartDate,'YYYY-MM-DD') }} 至 {{ formatDate(task.plannedEndDate,'YYYY-MM-DD') }}</span>
                </div>
                <div class="task-info-item">
                  <i class="el-icon-circle"></i>
                  <span class="task-label">进度:</span>
                  <span class="task-value task-percentage">{{ task.progress || '0' }}%</span>
                </div>
              </div>
              <el-progress 
                :percentage="parseFloat(task.progress) || 0"
                :status="getProgressStatus(task.status)"
                :stroke-width="6"
              />
            </el-card>
          </div>
          <el-empty v-else description="暂无计划任务"></el-empty>
        </el-card>

        <!-- 财务信息区 -->
        <el-card class="finance-card">
          <div slot="header">
            <span>财务概览</span>
          </div>
          <div class="info-row">
            <i class="el-icon-wallet"></i>
            <span class="label">总价值:</span>
            <span class="value value-currency">{{ formatMoney(projectDetail.totalValue) || 'N/A' }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-money"></i>
            <span class="label">总成本:</span>
            <span class="value value-currency">{{ formatMoney(projectDetail.totalCost) || 'N/A' }}</span>
          </div>
        </el-card>

        <!-- 团队信息区 -->
        <el-card class="team-card">
          <div slot="header">
            <span>团队成员</span>
          </div>
          <div class="participants-list">
            <el-empty v-if="fetchParticipantsList.length === 0" description="暂无参与人员"></el-empty>
            <div v-else>
              <div v-for="(participant, index) in fetchParticipantsList" 
                   :key="index" 
                   class="participant-item">
                <i class="el-icon-user"></i>
                <span class="participant-name">{{ participant.name || '未知' }}</span>
                <span v-if="participant.wechat" class="participant-wechat">
                  (微信: {{ participant.wechat }})
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 其他信息区 -->
        <el-card class="audit-card">
          <div slot="header">
            <span>其他信息</span>
          </div>
          <div class="info-row">
            <i class="el-icon-info"></i>
            <span class="label">项目ID:</span>
            <span class="value">{{ projectDetail.projectId }}</span>
          </div>
          <div class="info-row timestamp-row">
            <i class="el-icon-plus"></i>
            <span class="label">创建于:</span>
            <span class="value timestamp">{{ formatDate(projectDetail.createdAt) }}</span>
          </div>
        </el-card>

        <!-- 在最底部添加一个回到顶部按钮 -->
        <!-- <div class="back-to-top">
          <p>这是页面底部</p>
          <el-button type="danger" size="small" @click="scrollToTop">
            <i class="el-icon-top"></i> 回到顶部
          </el-button>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ProjectDetail',
  data() {
    return {
      loading: true,
      projectDetail: null,
      taskList: [],
      fetchParticipantsList: []
    }
  },
  created() {
    this.fetchProjectDetail();
  },
  methods: {
    formatDate(dateString, format = 'YYYY-MM-DD HH:mm') {
      if (!dateString) return '待定';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '无效日期';

        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);

        if (format === 'YYYY-MM-DD') {
          return `${year}-${month}-${day}`;
        } else if (format === 'YYYY-MM-DD HH:mm') {
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
        return dateString;
      } catch (e) {
        console.error("格式化出错:", e);
        return '格式化失败';
      }
    },
    formatMoney(value) {
      if (!value) return 'N/A';
      return `¥${parseFloat(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    },
    getStatusType(status) {
      switch (status) {
        case '进行中':
        case '正常':
          return 'success';
        case '计划延迟':
        case '延期':
        case '异常':
          return 'danger';
        case '已完成':
        case '关闭':
          return 'info';
        case '未开始':
          return 'warning';
        default:
          return '';
      }
    },
    getProgressStatus(status) {
      switch (status) {
        case '进行中':
        case '正常':
          return 'success';
        case '计划延迟':
        case '延期':
        case '异常':
          return 'exception';
        case '已完成':
        case '关闭':
          return 'success';
        case '未开始':
          return '';
        default:
          return '';
      }
    },
    getStatusColor(status) {
      switch (status) {
        case '进行中':
        case '正常':
          return '#67C23A';
        case '计划延迟':
        case '延期':
        case '异常':
          return '#F56C6C';
        case '已完成':
        case '关闭':
          return '#909399';
        case '未开始':
          return '#E6A23C';
        default:
          return '#909399';
      }
    },
    async fetchProjectDetail() {
      try {
        // 获取路由参数中的id
        const projectId = this.$route.params.id;
        console.log('获取项目详情，项目ID:', projectId);
        
        // 如果没有项目ID，显示错误信息
        if (!projectId) {
          this.$message.error('无法获取项目ID');
          this.loading = false;
          return;
        }
        
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/project/selectById/${projectId}`);
        console.log('项目详情API响应:', res.data);
        
        // 修改判断条件，同时支持数字和字符串类型的200
        if (res.data.code === 200 || res.data.code === "200") {
          this.projectDetail = res.data.data;
          // 获取任务列表
          await this.fetchTaskList(projectId);
          // 获取参与者列表
          await this.fetchParticipants(projectId);
        } else {
          this.$message.error(res.data.message || '获取项目详情失败');
        }
      } catch (error) {
        console.error('获取项目详情失败:', error);
        this.$message.error('网络请求失败');
      } finally {
        this.loading = false;
      }
    },
    async fetchTaskList(projectId) {
      try {
        console.log('获取任务列表，项目ID:', projectId);
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/task/selectByProjectId/${projectId}`);
        console.log('任务列表API响应:', res.data);
        
        if (res.data.code === 200 || res.data.code === "200") {
          this.taskList = res.data.data;
        } else {
          console.warn('获取任务列表失败:', res.data.message);
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
      }
    },
    async fetchParticipants(projectId) {
      try {
        console.log('获取参与者列表，项目ID:', projectId);
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/participant/selectByProjectId/${projectId}`);
        console.log('参与者列表API响应:', res.data);
        
        if (res.data.code === 200 || res.data.code === "200") {
          this.fetchParticipantsList = res.data.data;
        } else {
          console.warn('获取参与者列表失败:', res.data.message);
        }
      } catch (error) {
        console.error('获取参与者列表失败:', error);
      }
    },
    editProject(projectId) {
      this.$router.push(`/home/<USER>/update/${projectId}`);
    },
    addTask() {
      this.$router.push(`/home/<USER>/task/add/${this.$route.params.id}`);
    },
    viewTaskDetail(taskId) {
      this.$router.push(`/home/<USER>/task/detail/${taskId}`);
    },
    copyWechat(wechat) {
      navigator.clipboard.writeText(wechat).then(() => {
        this.$message.success('微信已复制到剪贴板');
      }).catch(() => {
        this.$message.error('复制失败，请手动复制');
      });
    },
    // 添加滚动到顶部的方法
    scrollToTop() {
      if (this.$refs.detailWrapper) {
        this.$refs.detailWrapper.scrollTop = 0;
      }
    },
    goBack() {
      this.$router.push('/home/<USER>/list')
    },
    async handleDeleteTask(taskId) {
      try {
        await this.$confirm('确认删除该任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const response = await axios.get(`http://projectmanagement.jenasi.ai:99/task/delete/${taskId}`);
        if (response.data.code === 200 || response.data.code === "200") {
          this.$message.success('删除成功');
          // 重新获取任务列表
          await this.fetchTaskList(this.$route.params.id);
        } else {
          this.$message.error(response.data.message || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除任务失败:', error);
          this.$message.error('删除失败');
        }
      }
    }
  }
}
</script>

<style scoped>
.project-detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px 0;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.header h2 {
  margin: 0;
  flex: 1;
}

.detail-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 40px;
}

/* 自定义滚动条样式 */
.detail-wrapper::-webkit-scrollbar {
  width: 8px;
}

.detail-wrapper::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.detail-wrapper::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.detail-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .detail-content {
    padding: 0 10px;
  }
}

.header-card,
.info-card,
.progress-card,
.tasks-card,
.finance-card,
.team-card,
.audit-card {
  margin-bottom: 20px;
}

.project-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.project-name {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.project-status-row {
  margin-bottom: 15px;
}

.project-desc {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #606266;
}

.info-row i {
  margin-right: 8px;
  font-size: 16px;
}

.label {
  margin-right: 8px;
  color: #909399;
}

.value {
  color: #303133;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-percentage {
  font-weight: bold;
  color: #409EFF;
}

.task-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-item {
  margin-bottom: 15px;
  cursor: pointer;
}

.task-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.task-header i {
  margin-right: 8px;
  font-size: 16px;
}

.task-name {
  flex: 1;
  margin-right: 12px;
  font-weight: bold;
  color: #303133;
}

.task-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.task-info-item {
  display: flex;
  align-items: center;
}

.task-info-item i {
  margin-right: 8px;
  font-size: 14px;
}

.task-label {
  margin-right: 8px;
  color: #909399;
}

.task-value {
  color: #606266;
}

.task-percentage {
  font-weight: bold;
  color: #409EFF;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.participants-list {
  padding: 10px 0;
}

.participant-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.participant-item i {
  margin-right: 8px;
  color: #409EFF;
}

.participant-name {
  color: #303133;
  margin-right: 8px;
}

.participant-wechat {
  color: #909399;
}

.value-currency {
  font-family: monospace;
  font-weight: bold;
}

.timestamp {
  color: #909399;
  font-size: 13px;
}

/* 添加回到顶部按钮样式 */
.back-to-top {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 40px;
  padding: 15px;
  background-color: #f0f9eb;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
}

.back-to-top p {
  margin-bottom: 10px;
  font-size: 14px;
  color: #67c23a;
}
</style> 