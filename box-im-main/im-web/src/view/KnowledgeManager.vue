<template>
  <div class="knowledge-manager">
    <div class="tabs">
      <el-tabs v-model="activeTab" type="card" class="full-height-tabs">
       <el-tab-pane label="工作日志" name="workLog">
          <work-log />
        </el-tab-pane>
        <el-tab-pane label="外部知识记录" name="knowledgeBase">
          <knowledge-base />
        </el-tab-pane>
        <el-tab-pane label="内部知识记录" name="internalKnowledgeBase">
          <internal-knowledge-base />
        </el-tab-pane>
        <el-tab-pane label="AI知识库" name="aiKnowledgeBase">
          <ai-knowledge-base />
        </el-tab-pane>
        <el-tab-pane label="备忘录" name="memo">
          <memo />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import KnowledgeBase from '@/components/knowledge/KnowledgeBase.vue';
import InternalKnowledgeBase from '@/components/knowledge/InternalKnowledgeBase.vue';
import WorkLog from '@/components/knowledge/WorkLog.vue';
import AIKnowledgeBase from '@/components/knowledge/AIKnowledgeBase.vue';
import Memo from '@/components/knowledge/memoBase.vue';

export default {
  name: 'KnowledgeManager',
  components: {
    'knowledge-base': KnowledgeBase,
    'internal-knowledge-base': InternalKnowledgeBase,
    'work-log': WorkLog,
    'ai-knowledge-base': AIKnowledgeBase,
    'memo': Memo
  },
  data() {
    return {
      activeTab: 'workLog'
    };
  }
};
</script>

<style lang="scss" scoped>
.knowledge-manager {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f7fa;
  
  .tabs {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    height: calc(100% - 40px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    :deep(.full-height-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;

      .el-tabs__content {
        flex: 1;
        height: calc(100% - 40px);
        overflow: hidden;
      }

      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style> 