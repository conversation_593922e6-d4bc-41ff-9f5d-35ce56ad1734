<template>
  <el-container class="chat-page">
    <el-aside width="260px" class="chat-list-box">
      <div class="chat-list-header">
        <el-input class="search-text" size="small" placeholder="搜索" v-model="searchText">
          <i class="el-icon-search el-input__icon" slot="prefix"> </i>
        </el-input>
      </div>
      <div class="chat-list-loading" v-if="loading" v-loading="true" element-loading-text="消息接收中..."
           element-loading-spinner="el-icon-loading" element-loading-background="#F9F9F9" element-loading-size="24">
      </div>
      <el-scrollbar class="chat-list-items" v-else>
        <div v-for="(chat, index) in chatStore.chats" :key="index">
          <chat-item v-show="!chat.delete && (chat.showName.includes(searchText) || hasMatchingMessages(chat))"
                     :chat="chat" :index="index"
                     @click.native="onActiveItem(index)" @delete="onDelItem(index)" @top="onTop(index)"
                     :active="chat === chatStore.activeChat">
            <div v-if="searchText && hasMatchingMessages(chat)" class="search-result-hint">
              找到 {{ getMatchingMessagesCount(chat) }} 条相关消息
              <el-button size="mini" type="text" @click.stop="showSearchDetail(chat, index)">查看</el-button>
            </div>
          </chat-item>
          <div v-if="shouldShowDivider(index)" class="pinned-divider">
            <span>置顶会话</span>
          </div>
        </div>
      </el-scrollbar>
    </el-aside>
    <el-container class="chat-box">
      <chat-box v-if="activeChat && activeChat.type != 'SYSTEM'" :chat="activeChat"></chat-box>
      <chat-system-box v-if="activeChat && activeChat.type == 'SYSTEM'" :chat="activeChat"></chat-system-box>
    </el-container>
  </el-container>
</template>

<script>
import ChatItem from "../components/chat/ChatItem.vue";
import ChatBox from "../components/chat/ChatBox.vue";
import ChatSystemBox from "../components/chat/ChatSystemBox.vue";
export default {
  name: "chat",
  components: {
    ChatItem,
    ChatBox,
    ChatSystemBox
  },
  data() {
    return {
      searchText: "",
      messageContent: "",
      group: {},
      groupMembers: []
    }
  },
  methods: {
    onActiveItem(index) {
      this.$store.commit("activeChat", index);
    },
    onDelItem(index) {
      this.$store.commit("removeChat", index);
    },
    onTop(chatIdx) {
      this.$store.commit("moveTop", chatIdx);
    },
    shouldShowDivider(index) {
      const chats = this.chatStore.chats;
      return index < chats.length - 1 &&
          chats[index].isTop &&
          !chats[index + 1].isTop &&
          !chats[index].delete &&
          (chats[index].showName.includes(this.searchText) || this.hasMatchingMessages(chats[index])) &&
          !chats[index + 1].delete &&
          (chats[index + 1].showName.includes(this.searchText) || this.hasMatchingMessages(chats[index + 1]));
    },
    hasMatchingMessages(chat) {
      if (!this.searchText) return false;

      if (chat.showName && typeof chat.showName === 'string' && chat.showName.includes(this.searchText)) {
        return true;
      }

      return this.getMatchingMessages(chat).length > 0;
    },
    getMatchingMessages(chat) {
      if (!this.searchText || !chat.messages) return [];

      const searchText = this.searchText.toLowerCase();

      return chat.messages.filter(message => {
        if (message.type === this.$msgType.TIP_TIME) {
          return false;
        }

        if (message.content && typeof message.content === 'string') {
          if (message.content.toLowerCase().includes(searchText)) {
            return true;
          }
        }

        if (message.title && typeof message.title === 'string') {
          if (message.title.toLowerCase().includes(searchText)) {
            return true;
          }
        }

        if (message.type === this.$msgType.IMAGE && message.fileName) {
          if (message.fileName.toLowerCase().includes(searchText)) {
            return true;
          }
        }

        if (message.type === this.$msgType.FILE && message.fileName) {
          if (message.fileName.toLowerCase().includes(searchText)) {
            return true;
          }
        }

        if (message.type === this.$msgType.VIDEO && message.fileName) {
          if (message.fileName.toLowerCase().includes(searchText)) {
            return true;
          }
        }

        return false;
      });
    },
    getMatchingMessagesCount(chat) {
      return this.getMatchingMessages(chat).length;
    },
    showSearchDetail(chat, index) {
      this.$store.commit("activeChat", index);
      
      this.$nextTick(() => {
        const matchedMessages = this.getMatchingMessages(chat);
        if (matchedMessages.length > 0) {
          this.$store.commit('setSearchDetail', {
            searchText: this.searchText,
            messages: matchedMessages
          });
        }
      });
    }
  },
  computed: {
    chatStore() {
      return this.$store.state.chatStore;
    },
    activeChat() {
      return this.chatStore.activeChat;
    },
    loading() {
      return this.chatStore.loadingGroupMsg || this.chatStore.loadingPrivateMsg
    }
  }
}
</script>

<style lang="scss">
.chat-page {
  .chat-list-box {
    display: flex;
    flex-direction: column;
    background: var(--im-background);

    .chat-list-header {
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 8px;
    }

    .chat-list-loading {
      height: 50px;
      background-color: #eee;

      .el-icon-loading {
        font-size: 24px;
        color: var(--im-text-color-light);
      }

      .el-loading-text {
        color: var(--im-text-color-light);
      }

      .chat-loading-box {
        height: 100%;
      }
    }

    .chat-list-items {
      flex: 1;

      .pinned-divider {
        position: relative;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        color: var(--im-text-color-light);
        background-color: #f7f7f7;
        border-top: 1px solid #e8e8e8;
        border-bottom: 1px solid #e8e8e8;

        span {
          display: inline-block;
          padding: 0 10px;
        }
      }

      .search-result-hint {
        font-size: 12px;
        color: var(--im-color-primary);
        padding: 0 0 5px 52px;
        margin-top: -5px;
        display: flex;
        align-items: center;

        .el-button {
          margin-left: 5px;
          padding: 0 5px;
        }
      }
    }
  }
}
</style>
