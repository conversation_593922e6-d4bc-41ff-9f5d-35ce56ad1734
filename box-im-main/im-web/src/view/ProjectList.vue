<template>
  <div class="project-list-container">
    <div class="header">
      <h2>项目列表</h2>
      <div class="header-buttons">
        <el-button 
          type="info" 
          icon="el-icon-user"
          @click="goToEmployeeView">
          员工计划查看
        </el-button>
        <el-button 
          type="primary" 
          icon="el-icon-menu"
          @click="goToPlanList">
          计划列表中心
        </el-button>
      </div>
    </div>

    <div class="project-list">
      <el-empty v-if="filteredProjectList.length === 0" description="暂无项目数据" />
      
      <el-card v-for="project in filteredProjectList" 
               :key="project.projectId" 
               class="project-card">
        <div class="card-header">
          <span class="project-name">{{ project.projectName }}</span>
          <div class="header-actions">
            <el-tag :type="getStatusType(project.progressStatus)" size="small">
              {{ project.progressStatus || '未知状态' }}
            </el-tag>
            <el-button 
              type="danger" 
              size="mini" 
              icon="el-icon-delete"
              @click="handleDelete(project.projectId)"
              class="delete-btn">
              删除
            </el-button>
          </div>
        </div>
        
        <div class="card-body">
          <p class="project-desc">{{ project.projectDesc }}</p>
          <div class="info-row">
            <span>开始: {{ formatDate(project.startDate, 'YYYY-MM-DD') }}</span>
            <span>结束: {{ formatDate(project.endDate, 'YYYY-MM-DD') }}</span>
          </div>
          <div class="info-row">
            <span>进度: {{ project.currentProgress || '0%' }}</span>
            <span>负责人: {{ project.managerName || '未知' }}</span>
          </div>
          <el-progress 
            :percentage="parseFloat(project.currentProgress)" 
            :status="getProgressStatus(project.progressStatus)"
            :stroke-width="6"
          />
          <div class="button-container">
            <el-button 
              type="primary" 
              size="small" 
              @click="goToProjectDetail(project.projectId)">
              查看详情
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <el-button 
      type="primary" 
      class="add-button" 
      circle 
      icon="el-icon-plus"
      @click="goToAddProject"
    />
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ProjectList',
  data() {
    return {
      projectList: []
    }
  },
  computed: {
    filteredProjectList() {
      return this.projectList.filter(project => {
        const progress = parseFloat(project.currentProgress) || 0;
        return progress < 100;
      });
    }
  },
  mounted() {
    this.fetchProjectList();
  },
  watch: {
    // 监听路由变化
    '$route': {
      handler: 'fetchProjectList',
      immediate: true
    }
  },
  methods: {
    formatDate(dateString, format = 'YYYY-MM-DD HH:mm') {
      if (!dateString) return '待定';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '无效日期';

        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);

        if (format === 'YYYY-MM-DD') {
          return `${year}-${month}-${day}`;
        } else if (format === 'YYYY-MM-DD HH:mm') {
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
        return dateString;
      } catch (e) {
        console.error("格式化出错:", e);
        return '格式化失败';
      }
    },
    getStatusType(status) {
      switch (status) {
        case '进行中':
        case '正常':
          return 'success';
        case '计划延迟':
        case '延期':
        case '异常':
          return 'danger';
        case '已完成':
        case '关闭':
          return 'info';
        case '未开始':
          return 'warning';
        default:
          return '';
      }
    },
    getProgressStatus(status) {
      switch (status) {
        case '进行中':
        case '正常':
          return 'success';
        case '计划延迟':
        case '延期':
        case '异常':
          return 'exception';
        case '已完成':
        case '关闭':
          return 'success';
        case '未开始':
          return '';
        default:
          return '';
      }
    },
    goToProjectDetail(projectId) {
      this.$router.push(`/home/<USER>/detail/${projectId}`);
    },
    goToAddProject() {
      this.$router.push('/home/<USER>');
    },
    goToPlanList() {
      this.$router.push('/home/<USER>/list');
    },
    goToEmployeeView() {
      this.$router.push('/home/<USER>/employee');
    },
    async handleDelete(projectId) {
      try {
        await this.$confirm('确认删除该项目吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const response = await axios.get(`http://projectmanagement.jenasi.ai:99/project/delete/${projectId}`);
        if (response.data.code === "200") {
          this.$message.success('删除成功');
          this.fetchProjectList(); // 重新加载项目列表
        } else {
          this.$message.error(response.data.message || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除项目失败:', error);
          this.$message.error('删除失败');
        }
      }
    },
    async fetchProjectList() {
      try {
        const res = await axios.get('http://projectmanagement.jenasi.ai:99/project/selectAll');
        if (res.data.code === "200") {
          this.projectList = res.data.data;
        } else {
          this.$message.error(res.data.message || '获取项目列表失败');
          this.projectList = [];
        }
      } catch (error) {
        console.error('请求项目列表失败:', error);
        this.$message.error('网络请求失败');
        this.projectList = [];
      }
    }
  }
}
</script>

<style scoped>
.project-list-container {
  height: 100vh;
  width: 100%;
  margin: 0;
  background: transparent;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 24px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.project-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  padding: 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.project-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.project-name {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  flex: 1;
  margin-right: 20px;
  word-break: break-word;
}

.card-body {
  color: #606266;
}

.project-desc {
  margin-bottom: 15px;
  color: #606266;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #909399;
}

.info-row span {
  width: 49%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 56px;
  height: 56px;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

.delete-btn {
  margin-left: 10px;
}
</style> 