<template>
  <div class="plan-detail-container">
    <div class="page-header">
      <div class="header-buttons">
        <el-button type="primary" icon="el-icon-back" @click="goBack">返回项目详情</el-button>
        <el-button type="info" icon="el-icon-menu" @click="goToPlanList">返回计划列表中心</el-button>
        <el-button type="success" icon="el-icon-user" @click="goToEmployeePlanList">返回员工计划列表</el-button>
      </div>
      <h2>计划详情</h2>
    </div>

    <div class="detail-wrapper" v-loading="loading">
      <div v-if="taskData" class="detail-content">
        <!-- 任务概览卡片 -->
        <el-card class="task-overview-card">
          <div class="project-name">所属项目: {{ taskData.projectName || '未知项目' }}</div>
          <div class="info-row">
            <i class="el-icon-document"></i>
            <span class="label">计划名称:</span>
            <span class="value task-title">{{ taskData.taskName || '无标题任务' }}</span>
            <el-tag :type="getPriorityType(taskData.priority)" size="medium">
              {{ taskData.priority || '未设置' }}
            </el-tag>
            <el-button 
              type="text" 
              icon="el-icon-edit" 
              @click="editTask(taskData.taskId)"
            />
          </div>
          <div class="info-row" v-if="taskData.taskDesc">
            <i class="el-icon-notebook-2"></i>
            <span class="label">计划描述:</span>
            <span class="value">{{ taskData.taskDesc }}</span>
          </div>
        </el-card>

        <!-- 时间与成本卡片 -->
        <el-card class="time-cost-card">
          <div slot="header">
            <span class="card-title">时间与成本</span>
          </div>
          <div class="info-row">
            <i class="el-icon-date"></i>
            <span class="label">计划开始:</span>
            <span class="value">{{ formatDate(taskData.plannedStartDate, 'YYYY-MM-DD') || '未设置' }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-date"></i>
            <span class="label">计划结束:</span>
            <span class="value">{{ formatDate(taskData.plannedEndDate, 'YYYY-MM-DD') || '未设置' }}</span>
          </div>
          <el-divider></el-divider>
          <div class="info-row">
            <i class="el-icon-time"></i>
            <span class="label">实际开始:</span>
            <span class="value">{{ formatDate(taskData.actualStartDate) || '未开始' }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-time"></i>
            <span class="label">实际结束:</span>
            <span class="value">{{ formatDate(taskData.actualEndDate) || '未结束' }}</span>
          </div>
          <el-divider></el-divider>
          <div class="info-row">
            <i class="el-icon-money"></i>
            <span class="label">预估价值:</span>
            <span class="value value-currency">{{ formatMoney(taskData.estimatedValue) }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-money"></i>
            <span class="label">预计成本:</span>
            <span class="value value-currency">{{ formatMoney(taskData.estimatedCost) }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-money"></i>
            <span class="label">实际成本:</span>
            <span class="value value-currency">{{ formatMoney(taskData.actualCost) }}</span>
          </div>
        </el-card>

        <!-- 进度与状态卡片 -->
        <el-card class="progress-status-card">
          <div slot="header">
            <span class="card-title">进度与状态</span>
          </div>
          <div class="progress-item">
            <span class="label">当前进度:</span>
            <el-progress 
              :percentage="taskData.progress || 0" 
              :status="getProgressStatus(taskData.status)"
              :stroke-width="18"
            />
          </div>
          <el-divider></el-divider>
          <div class="info-row">
            <i class="el-icon-info"></i>
            <span class="label">状态:</span>
            <span class="value" :style="{ color: getStatusColor(taskData.status) }">
              {{ taskData.status || '未知' }}
            </span>
          </div>
          <el-divider></el-divider>
          <div class="info-row timestamp-row">
            <i class="el-icon-plus"></i>
            <span class="label">创建于:</span>
            <span class="value timestamp">{{ formatDate(taskData.createdAt) || '未知' }}</span>
          </div>
          <div class="info-row timestamp-row">
            <i class="el-icon-refresh"></i>
            <span class="label">更新于:</span>
            <span class="value timestamp">{{ formatDate(taskData.updatedAt) || '未知' }}</span>
          </div>
        </el-card>

        <!-- 负责人信息卡片 -->
        <el-card class="responsible-card">
          <div slot="header">
            <span class="card-title">负责人信息</span>
          </div>
          <div class="info-row">
            <i class="el-icon-user"></i>
            <span class="label">负责人:</span>
            <span class="value">{{ taskData.ownerName || '未设置' }}</span>
          </div>
          <div class="info-row">
            <i class="el-icon-chat-dot-round"></i>
            <span class="label">负责人微信:</span>
            <span class="value">{{ taskData.ownerWechat || '未提供' }}</span>
            <el-button 
              v-if="taskData.ownerWechat" 
              size="mini" 
              type="primary"
              @click="copyWechat(taskData.ownerWechat)"
            >复制</el-button>
          </div>
        </el-card>

        <!-- 参与人员卡片 -->
        <el-card class="participants-card">
          <div slot="header">
            <span class="card-title">参与人员</span>
          </div>
          <div v-if="taskData.participants && taskData.participants.length > 0" class="participant-list">
            <div v-for="member in taskData.participants" 
                 :key="member.id || member.name" 
                 class="participant-item">
              <i class="el-icon-user"></i>
              <span class="member-name">{{ member.name || '未知' }}</span>
              <span v-if="member.wechat" class="member-wechat">(微信: {{ member.wechat }})</span>
            </div>
          </div>
          <el-empty v-else description="暂无参与人员" />
        </el-card>

        <!-- 每日复盘记录卡片 -->
        <el-card class="reviews-card">
          <div slot="header">
            <span class="card-title">每日复盘记录</span>
          </div>
          <div v-if="taskData.dailyReviewSummary && taskData.dailyReviewSummary.length > 0" class="review-list">
            <el-card v-for="(review, index) in taskData.dailyReviewSummary" 
                     :key="index" 
                     class="review-item-card"
                     shadow="hover">
              <div class="review-header">
                <i class="el-icon-date"></i>
                <span class="review-date">{{ formatDate(review.date) || '未知日期' }}</span>
                <span class="review-hours">({{ review.hours || 'N/A' }} 小时)</span>
                <span class="review-day-progress">当日进度: {{ review.progress || 'N/A' }}%</span>
              </div>
              <div class="review-participants">
                <i class="el-icon-user"></i>
                <span>参与: {{ formatReviewParticipants(review.participants) || '无' }}</span>
              </div>
              <div v-if="review.content" class="review-content">
                <span>内容: {{ review.content }}</span>
              </div>
              <div v-if="review.summary" class="review-summary">
                <span>总结: {{ review.summary }}</span>
              </div>
              <div v-if="!review.content && !review.summary" class="review-empty-content">
                <span>无内容记录</span>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无复盘记录" />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'PlanDetail',
  data() {
    return {
      loading: false,
      taskData: null
    }
  },
  mounted() {
    this.fetchTaskDetails(this.$route.params.id);
  },
  methods: {
    goBack() {
      if (this.taskData && this.taskData.projectId) {
        this.$router.push(`/home/<USER>/detail/${this.taskData.projectId}`);
      } else {
        this.$router.push('/home/<USER>/list');
      }
    },
    goToPlanList() {
      this.$router.push('/home/<USER>/list');
    },
    goToEmployeePlanList() {
      this.$router.push('/home/<USER>/employee');
    },
    formatMoney(amount) {
      if (isNaN(amount)) {
        return '无效金额';
      }
      
      if(!amount){
        return 0;
      }

      const parts = amount.toString().split('.');
      const integerPart = parts[0];
      const decimalPart = parts[1] ? '.' + parts[1] : '';

      const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

      return formattedInteger + decimalPart;
    },
    formatDate(dateString, format = 'YYYY-MM-DD HH:mm') {
      if (!dateString) return '待定';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '无效日期';

        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);

        if (format === 'YYYY-MM-DD') {
          return `${year}-${month}-${day}`;
        } else if (format === 'YYYY-MM-DD HH:mm') {
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
        return dateString;
      } catch (e) {
        console.error("格式化出错:", e);
        return '格式化失败';
      }
    },
    getPriorityType(priority) {
      switch (priority?.toLowerCase()) {
        case '高':
          return 'danger';
        case '中':
          return 'warning';
        case '低':
          return 'info';
        default:
          return 'info';
      }
    },
    getStatusColor(status) {
      switch (status) {
        case '草稿':
          return '#c0c4cc';
        case '待处理':
          return '#a9b7c7';
        case '进行中':
          return '#1890ff';
        case '暂停/挂起':
          return '#faad14';
        case '已完成':
          return '#52c41a';
        case '失败':
          return '#f5222d';
        case '已取消':
          return '#909399';
        case '已拒绝':
          return '#f5222d';
        case '已归档':
          return '#dcdfe6';
        default:
          return '#666666';
      }
    },
    getProgressStatus(status) {
      switch (status) {
        case '已完成':
          return 'success';
        case '失败':
        case '已取消':
        case '已拒绝':
          return 'exception';
        default:
          return '';
      }
    },
    formatReviewParticipants(participantsArray) {
      if (!participantsArray || participantsArray.length === 0) {
        return '无';
      }
      return participantsArray.join(', ');
    },
    copyWechat(wechatId) {
      if (!wechatId) return;
      navigator.clipboard.writeText(wechatId).then(() => {
        this.$message.success('微信已复制');
      }).catch(() => {
        this.$message.error('复制失败');
      });
    },
    editTask(taskId) {
      if (!taskId) {
        this.$message.warning('计划ID无效');
        return;
      }
      this.$router.push(`/home/<USER>/task/update/${taskId}`);
    },
    async fetchTaskDetails(taskId) {
      this.loading = true;
      try {
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/task/selectById/${taskId}`);
        if (res.data.code === "200") {
          this.taskData = res.data.data;
          
          // 如果任务数据中包含项目ID，则获取项目信息
          if (this.taskData.projectId) {
            try {
              const projectRes = await axios.get(`http://projectmanagement.jenasi.ai:99/project/selectById/${this.taskData.projectId}`);
              if (projectRes.data.code === "200" && projectRes.data.data) {
                // 将项目名称添加到任务数据中
                this.taskData.projectName = projectRes.data.data.projectName;
              }
            } catch (error) {
              console.error('获取项目信息失败:', error);
            }
          }
        } else {
          this.$message.error(res.data.message || '获取计划详情失败');
        }
      } catch (error) {
        console.error('获取计划详情失败:', error);
        this.$message.error('网络请求失败');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.plan-detail-container {
  height: 100vh;
  width: 100%;
  margin: 0;
  background: #f5f7fa;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 24px;
}

.detail-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 40px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-left: 4px solid #1890ff;
  padding-left: 10px;
  line-height: 1;
}

.task-overview-card {
  margin-bottom: 20px;
}

.project-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.6;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row i {
  margin-right: 10px;
  color: #666;
  margin-top: 3px;
}

.label {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #303133;
  flex: 1;
}

.value-currency {
  font-weight: 500;
  color: #ff5722;
}

.timestamp-row {
  font-size: 13px;
  color: #999;
}

.timestamp-row .label {
  min-width: 80px;
}

.progress-item {
  margin-bottom: 20px;
}

.participant-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.participant-item:last-child {
  border-bottom: none;
}

.participant-item i {
  margin-right: 10px;
  color: #409eff;
}

.member-name {
  font-weight: 500;
  margin-right: 10px;
}

.member-wechat {
  color: #888;
  font-size: 13px;
}

.review-item-card {
  margin-bottom: 15px;
}

.review-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 500;
}

.review-header i {
  margin-right: 5px;
  color: #666;
}

.review-date {
  font-size: 14px;
}

.review-hours {
  font-size: 13px;
  color: #888;
}

.review-day-progress {
  margin-left: auto;
  font-size: 13px;
  color: #1890ff;
}

.review-participants {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #666;
  font-size: 13px;
}

.review-participants i {
  margin-right: 5px;
}

.review-content,
.review-summary {
  line-height: 1.6;
  color: #444;
  padding: 8px 0;
  border-top: 1px dashed #eee;
  margin-top: 8px;
}

.review-empty-content {
  color: #999;
  text-align: center;
  padding: 10px 0;
}

/* 自定义滚动条样式 */
.detail-wrapper::-webkit-scrollbar {
  width: 8px;
}

.detail-wrapper::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.detail-wrapper::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.detail-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .plan-detail-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }
  
  .detail-content {
    padding: 0 10px;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .label {
    margin-bottom: 5px;
  }
}
</style> 