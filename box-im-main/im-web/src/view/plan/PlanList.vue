<template>
  <div class="plan-list-container">
    <div class="page-header">
      <div class="header-content">
        <el-button 
          icon="el-icon-back" 
          @click="goBack"
          class="back-button">
          返回项目列表
        </el-button>
        <h2>计划列表中心</h2>
      </div>
    </div>

    <div class="content-container">
      <el-card class="plan-tree-card">
        <div v-loading="loading">
          <el-tree
            :data="planTreeData"
            :props="defaultProps"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <template v-if="data.projectId">
                <!-- 项目节点 -->
                <div class="project-node">
                  <i class="el-icon-folder"></i>
                  <span class="project-name">{{ data.projectName }}</span>
                  <el-tag size="mini" :type="getProgressType(data.progressStatus)">
                    {{ data.progressStatus }}
                  </el-tag>
                </div>
              </template>
              <template v-else>
                <!-- 计划节点 -->
                <div class="task-node clickable" @click="goToPlanDetail(data)">
                  <i class="el-icon-document"></i>
                  <span class="task-name">{{ data.taskName }}</span>
                  <el-tag size="mini" :type="getStatusType(data.status)">
                    {{ data.status }}
                  </el-tag>
                  <span class="update-time">{{ formatDate(data.updatedAt) }}</span>
                </div>
              </template>
            </span>
          </el-tree>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'PlanList',
  data() {
    return {
      loading: false,
      planTreeData: [],
      defaultProps: {
        children: 'tacksTreeVoList',
        label: 'projectName'
      },
      API_BASE_URL: 'http://projectmanagement.jenasi.ai:99'
    };
  },
  created() {
    this.fetchPlanTree();
  },
  methods: {
    // 获取计划树数据
    async fetchPlanTree() {
      this.loading = true;
      try {
        const res = await axios.get(`${this.API_BASE_URL}/project/selectTaskTree`);
        if (res.data.code === "200") {
          this.planTreeData = res.data.data;
        } else {
          this.$message.error(res.data.message || '获取计划列表失败');
        }
      } catch (error) {
        console.error('获取计划列表失败:', error);
        this.$message.error('获取计划列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 获取进度状态对应的标签类型
    getProgressType(status) {
      const typeMap = {
        '正常': 'success',
        '未开始': 'info',
        '延迟': 'danger',
        '已完成': 'success'
      };
      return typeMap[status] || 'info';
    },

    // 获取计划状态对应的标签类型
    getStatusType(status) {
      const typeMap = {
        '进行中': 'primary',
        '已完成': 'success',
        '待处理': 'info',
        '暂停/挂起': 'warning',
        '已取消': 'danger'
      };
      return typeMap[status] || 'info';
    },

    // 返回项目列表
    goBack() {
      this.$router.push('/home/<USER>/list');
    },

    // 跳转到计划详情
    goToPlanDetail(data) {
      if (data.taskId) {
        this.$router.push({
          path: `/home/<USER>/task/detail/${data.taskId}`
        });
      } else {
        this.$message.warning('计划ID不存在');
      }
    }
  }
};
</script>

<style scoped>
.plan-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
}

.page-header h2 {
  flex: 1;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  padding: 15px 0;
}

.content-container {
  width: 100%;
  margin: 0 auto;
}

.plan-tree-card {
  border-radius: 8px;
  min-height: calc(100vh - 140px);
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  padding: 16px 12px;
  width: 100%;
}

.project-node {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.project-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.task-node {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.task-name {
  color: #409EFF;
  font-size: 15px;
}

.update-time {
  color: #909399;
  font-size: 13px;
  margin-left: auto;
  padding-left: 16px;
}

.el-tree-node__content {
  height: auto !important;
  padding: 20px 0;
  margin: 8px 0;
}

.el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.el-tree-node {
  margin: 8px 0;
}

/* 自定义滚动条样式 */
.plan-tree-card ::v-deep .el-card__body {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}

.plan-tree-card ::v-deep .el-card__body::-webkit-scrollbar {
  width: 8px;
}

.plan-tree-card ::v-deep .el-card__body::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.plan-tree-card ::v-deep .el-card__body::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.plan-tree-card ::v-deep .el-card__body::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

.task-node.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-node.clickable:hover {
  background-color: #f5f7fa;
}
</style> 