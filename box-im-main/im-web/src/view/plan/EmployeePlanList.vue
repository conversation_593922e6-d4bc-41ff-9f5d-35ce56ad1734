<template>
  <div class="employee-plan-container">
    <div class="page-header">
      <div class="header-content">
        <el-button 
          icon="el-icon-back" 
          @click="goBack"
          class="back-button">
          返回项目列表
        </el-button>
        <h2>项目与任务管理</h2>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="部门">
            <el-select 
              v-model="filterForm.departmentId" 
              placeholder="请选择部门"
              @change="handleDepartmentChange">
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.departmentName"
                :value="dept.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人员">
            <el-select 
              v-model="filterForm.employeeId" 
              placeholder="请选择人员"
              :disabled="!filterForm.departmentId"
              @change="handleEmployeeChange">
              <el-option
                v-for="emp in employees"
                :key="emp.id"
                :label="emp.name"
                :value="emp.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 数据加载/错误/无数据状态 -->
    <div v-if="isLoading" class="loading-indicator">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-if="error" class="error-message">
      <el-alert
        :title="error"
        type="error"
        show-icon>
        <el-button type="primary" size="small" @click="retryFetchProjects">
          点击重试
        </el-button>
      </el-alert>
    </div>

    <!-- 项目列表 -->
    <div class="project-list" v-if="!isLoading && !error && projects.length > 0">
      <el-collapse v-for="project in projects" 
                  :key="project.projectId"
                  v-model="project.open">
        <el-collapse-item>
          <template slot="title">
            <div class="project-title-content">
              <span class="project-name">{{ project.name }}</span>
              <div class="project-meta">
                <el-tag size="small" type="info">{{ project.tasks.length }} 任务</el-tag>
                <el-tag 
                  size="small" 
                  :type="getProjectCssClass(project.progressStatus)">
                  {{ project.progressStatus }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="task-list">
            <div v-if="project.tasks.length === 0" class="no-tasks">
              <el-empty description="暂无任务" />
            </div>
            <div v-for="task in project.tasks" 
                 :key="task.taskId" 
                 class="task-item"
                 @click="toTask(task.taskId)">
              <span class="task-name">{{ task.name }}</span>
              <div class="task-details">
                <el-tag 
                  size="small" 
                  :type="getTaskCssClass(task.status)">
                  {{ task.status }}
                </el-tag>
                <span class="task-due-date">{{ task.dueDate }}</span>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 无项目数据提示 -->
    <div v-if="!isLoading && !error && projects.length === 0 && filterForm.employeeId" class="no-data">
      <el-empty description="当前人员暂无项目数据" />
    </div>
    <div v-if="!isLoading && !error && projects.length === 0 && !filterForm.employeeId" class="no-data">
      <el-empty description="请选择人员以查看项目数据" />
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'EmployeePlanList',
  data() {
    return {
      isLoading: false,
      error: null,
      departments: [],
      employees: [],
      projects: [],
      filterForm: {
        departmentId: '',
        employeeId: ''
      },
      API_BASE_URL: 'http://projectmanagement.jenasi.ai:99'
    };
  },
  created() {
    this.fetchDepartments();
  },
  methods: {
    goBack() {
      this.$router.push('/home/<USER>/list');
    },
    async fetchDepartments() {
      try {
        const res = await axios.get(`${this.API_BASE_URL}/departments/selectAll`);
        if (res.data.code === "200") {
          this.departments = res.data.data;
          // 如果只有一个部门，则自动选中
          if (this.departments.length === 1) {
            this.handleDepartmentChange(this.departments[0].id);
          }
        } else {
          this.$message.error(res.data.message || '获取部门列表失败');
        }
      } catch (error) {
        console.error('获取部门列表失败:', error);
        this.$message.error('获取部门列表失败');
      }
    },
    async handleDepartmentChange(departmentId) {
      this.filterForm.employeeId = '';
      this.employees = [];
      this.projects = [];
      
      if (departmentId) {
        try {
          const res = await axios.get(`${this.API_BASE_URL}/employees/selectAllByDepartmentId/${departmentId}`);
          if (res.data.code === "200") {
            this.employees = res.data.data;
          } else {
            this.$message.error(res.data.message || '获取员工列表失败');
          }
        } catch (error) {
          console.error('获取员工列表失败:', error);
          this.$message.error('获取员工列表失败');
        }
      }
    },
    async handleEmployeeChange(employeeId) {
      if (employeeId) {
        await this.fetchEmployeePlans(employeeId);
      } else {
        this.projects = [];
      }
    },
    async fetchEmployeePlans(employeeId) {
      this.isLoading = true;
      this.error = null;
      try {
        const res = await axios.get(`${this.API_BASE_URL}/project/selectTaskTreeByEmployeeId/${employeeId}`);
        if (res.data.code === "200") {
          this.projects = this.mapApiDataToProjects(res.data.data);
        } else {
          this.error = res.data.message || '获取计划列表失败';
        }
      } catch (error) {
        console.error('获取计划列表失败:', error);
        this.error = '网络请求失败，请检查网络连接或服务器状态';
      } finally {
        this.isLoading = false;
      }
    },
    retryFetchProjects() {
      if (this.filterForm.employeeId) {
        this.fetchEmployeePlans(this.filterForm.employeeId);
      } else if (this.filterForm.departmentId) {
        this.handleDepartmentChange(this.filterForm.departmentId);
      } else {
        this.fetchDepartments();
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
        const parts = dateString.split('T')[0].split('-');
        if (parts.length === 3) {
          return `${parts[0]}-${parts[1]}-${parts[2]}`;
        }
        return dateString;
      } catch (e) {
        console.error("日期格式化失败:", e);
        return dateString;
      }
    },
    mapApiDataToProjects(apiData) {
      return apiData.map(project => ({
        projectId: project.projectId,
        name: project.projectName,
        progressStatus: project.progressStatus,
        open: false,
        tasks: project.tacksTreeVoList ? project.tacksTreeVoList.map(task => ({
          taskId: task.taskId,
          name: task.taskName,
          status: task.status,
          dueDate: this.formatDate(task.updatedAt),
        })) : [],
      }));
    },
    getProjectCssClass(status) {
      switch (status) {
        case '未开始':
          return 'warning';
        case '进行中':
        case '已暂停':
          return 'primary';
        case '延期':
          return 'danger';
        case '已完成':
        case '正常':
          return 'success';
        case '已取消':
          return 'info';
        default:
          return '';
      }
    },
    getTaskCssClass(status) {
      switch (status) {
        case '草稿':
        case '待处理':
          return 'warning';
        case '进行中':
          return 'primary';
        case '暂停/挂起':
          return 'info';
        case '已归档':
        case '已完成':
          return 'success';
        case '失败':
        case '已取消':
        case '已拒绝':
          return 'danger';
        default:
          return '';
      }
    },
    toTask(taskId) {
      this.$router.push(`/home/<USER>/task/detail/${taskId}`);
    }
  }
};
</script>

<style scoped>
.employee-plan-container {
  padding: 20px;
  background-color: transparent;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
}

.page-header h2 {
  flex: 1;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  padding: 15px 0;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: #fff;
  border-radius: 8px;
}

.filter-form {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.loading-indicator,
.error-message,
.no-data {
  margin: 20px 0;
  text-align: center;
}

.project-list {
  margin-top: 20px;
}

.project-title-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-list {
  padding: 10px 0;
}

.no-tasks {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px dashed #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:hover {
  background-color: #f5f7fa;
}

.task-name {
  font-size: 14px;
  color: #409EFF;
  flex: 1;
  margin-right: 20px;
}

.task-details {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-due-date {
  font-size: 13px;
  color: #909399;
}

/* 自定义 Element UI 组件样式 */
:deep(.el-collapse-item__header) {
  font-size: 16px;
  padding: 15px 20px;
}

:deep(.el-collapse-item__content) {
  padding: 0 20px 20px;
}

:deep(.el-select) {
  width: 200px;
}
</style> 