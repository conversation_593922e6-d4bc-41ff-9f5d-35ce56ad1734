<template>
  <div class="plan-add-container">
    <div class="page-header">
      <h2>新增计划</h2>
    </div>

    <div class="form-container">
      <div class="scrollable-content">
        <el-form :model="formData" :rules="rules" ref="planForm" label-width="120px" class="plan-form">
          <!-- 项目关联信息 -->
          <el-card class="form-card">
            <div slot="header">
              <span>项目信息</span>
            </div>
            
            <el-form-item v-if="formData.projectId">
              <div class="project-info">
                <i class="el-icon-folder"></i>
                <span class="label">所属项目：</span>
                <span class="info-text">{{ formData.projectName || '未知项目' }}</span>
              </div>
            </el-form-item>
            
            <el-form-item v-else label="所属项目ID" prop="projectId">
              <el-input v-model.number="formData.projectId" placeholder="请手动输入项目ID (临时)" type="number"></el-input>
            </el-form-item>
          </el-card>

          <!-- 计划基本信息 -->
          <el-card class="form-card">
            <div slot="header">
              <span>基本信息</span>
            </div>
            
            <el-form-item label="计划名称" prop="taskName">
              <el-input v-model="formData.taskName" placeholder="请输入计划名称"></el-input>
            </el-form-item>

            <el-form-item label="计划描述" prop="taskDesc">
              <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入计划描述" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
            </el-form-item>

            <el-form-item label="优先级" prop="priority">
              <el-input v-model="formData.priority" placeholder="请输入优先级说明 (例如：高，中)"></el-input>
            </el-form-item>
          </el-card>

          <!-- 计划时间信息 -->
          <el-card class="form-card">
            <div slot="header">
              <span>时间信息</span>
            </div>

            <el-form-item label="计划开始时间" prop="plannedStartDate">
              <el-date-picker
                v-model="formData.plannedStartDate"
                type="date"
                placeholder="选择开始日期"
                value-format="yyyy-MM-dd"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="计划结束时间" prop="plannedEndDate">
              <el-date-picker
                v-model="formData.plannedEndDate"
                type="date"
                placeholder="选择结束日期"
                value-format="yyyy-MM-dd"
                :picker-options="{ disabledDate: disabledEndDate }"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-card>

          <!-- 价值与成本 -->
          <el-card class="form-card">
            <div slot="header">
              <span>价值与成本</span>
            </div>

            <el-form-item label="预估价值" prop="estimatedValue">
              <el-input-number v-model="formData.estimatedValue" :precision="2" :step="100" :min="0" placeholder="请输入预估价值" style="width: 100%"></el-input-number>
            </el-form-item>

            <el-form-item label="预计成本" prop="estimatedCost">
              <el-input-number v-model="formData.estimatedCost" :precision="2" :step="100" :min="0" placeholder="请输入预计成本" style="width: 100%"></el-input-number>
            </el-form-item>

            <el-form-item label="实际成本" prop="actualCost">
              <el-input-number v-model="formData.actualCost" :precision="2" :step="100" :min="0" placeholder="请输入实际成本 (可选)" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-card>

          <!-- 进度与状态 -->
          <el-card class="form-card">
            <div slot="header">
              <span>进度与状态</span>
            </div>

            <el-form-item label="进度 (%)" prop="progress">
              <el-slider v-model="formData.progress" :min="0" :max="100" :step="1" show-input></el-slider>
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option
                  v-for="item in statusOptions"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </el-card>

          <!-- 负责人选择 -->
          <el-card class="form-card">
            <div slot="header">
              <span>负责人选择</span>
            </div>

            <el-form-item label="选择部门" prop="departmentId">
              <el-select 
                v-model="selectedDepartment" 
                placeholder="请选择部门" 
                @change="handleDepartmentChange"
                :loading="departmentsLoading"
                style="width: 100%">
                <el-option
                  v-for="item in departmentsList"
                  :key="item.id"
                  :label="item.departmentName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="选择负责人" prop="ownerId">
              <el-select 
                v-model="formData.ownerId" 
                placeholder="请选择负责人" 
                @change="handleEmployeeChange"
                :loading="employeesLoading"
                :disabled="!selectedDepartment || employeesList.length === 0"
                style="width: 100%">
                <el-option
                  v-for="item in employeesList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                  <span>{{ item.name }}</span>
                  <span v-if="item.wechatName" style="float: right; color: #8492a6; font-size: 13px">
                    微信: {{ item.wechatName }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 显示已选负责人信息 -->
            <div v-if="formData.ownerId" class="selected-manager-info">
              <i class="el-icon-user"></i>
              <span>已选负责人: {{ formData.ownerName }}</span>
              <span v-if="formData.wechatName">，微信：{{ formData.wechatName }}</span>
            </div>
          </el-card>

          <!-- 底部占位，确保表单底部内容不被固定按钮遮挡 -->
          <div class="bottom-spacer"></div>
        </el-form>
      </div>

      <!-- 表单提交按钮 - 固定在底部 -->
      <div class="fixed-bottom-actions">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting" :disabled="isSubmitting">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'PlanAdd',
  data() {
    return {
      formData: {
        projectId: null,    // 关联的项目ID
        projectName: '',    // 关联的项目名称
        taskName: '',       // 计划名称
        taskDesc: '',       // 计划描述
        priority: '',       // 优先级说明
        plannedStartDate: null,  // 计划开始日期
        plannedEndDate: null,    // 计划结束日期
        estimatedValue: '',      // 预估价值
        estimatedCost: '',       // 预计成本
        actualCost: '',          // 实际成本
        progress: 0,             // 进度百分比
        status: '',              // 状态
        ownerId: null,           // 负责人ID
        ownerName: '',           // 负责人姓名
        wechatName: ''           // 负责人微信
      },
      
      // 部门相关
      departmentsList: [],         // 部门列表
      selectedDepartment: null,    // 选中的部门ID
      departmentsLoading: false,   // 部门加载中
      
      // 员工相关
      employeesList: [],           // 员工列表
      employeesLoading: false,     // 员工加载中
      
      // 状态选项
      statusOptions: [
        '草稿',
        '待处理',
        '进行中',
        '暂停/挂起',
        '已完成',
        '失败',
        '已取消',
        '已拒绝',
        '已归档'
      ],
      
      // 表单状态
      isSubmitting: false,         // 是否正在提交
      
      // API基础URL
      API_BASE_URL: 'http://projectmanagement.jenasi.ai:99', // 后端接口地址
      
      // 表单验证规则
      rules: {
        taskName: [
          { required: true, message: '请输入计划名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        plannedStartDate: [
          { required: true, message: '请选择计划开始时间', trigger: 'change' }
        ],
        progress: [
          { required: true, message: '请设置进度', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        ownerId: [
          { required: true, message: '请选择负责人', trigger: 'change' }
        ]
      }
    };
  },
  
  created() {
    // 从路由获取项目ID和项目名称
    const { id, name } = this.$route.params;
    if (id) {
      this.formData.projectId = parseInt(id);
      this.formData.projectName = name || '';
      // 如果只有ID没有名称，则获取项目详情
      if (!name) {
        this.fetchProjectDetails(id);
      }
    }
    
    // 初始化加载部门列表
    this.fetchDepartments();
  },
  
  methods: {
    // 获取项目详情
    async fetchProjectDetails(projectId) {
      try {
        const res = await axios.get(`${this.API_BASE_URL}/project/selectById/${projectId}`);
        if (res.data.code === "200" && res.data.data) {
          this.formData.projectName = res.data.data.projectName;
        } else {
          this.$message.warning('获取项目信息失败');
        }
      } catch (error) {
        console.error('获取项目信息失败:', error);
        this.$message.warning('获取项目信息失败');
      }
    },

    // 禁用早于开始日期的结束日期选择
    disabledEndDate(date) {
      if (!this.formData.plannedStartDate) {
        return false;
      }
      return date.getTime() < new Date(this.formData.plannedStartDate).getTime();
    },
    
    // 获取部门列表
    async fetchDepartments() {
      this.departmentsLoading = true;
      try {
        const res = await axios.get(`${this.API_BASE_URL}/departments/selectAll`);
        if (res.data.code == 200) {
          this.departmentsList = res.data.data;
        } else {
          this.$message.error(res.data.message || '获取部门列表失败');
        }
      } catch (error) {
        console.error("获取部门列表异常:", error);
        this.$message.error('获取部门列表失败');
      } finally {
        this.departmentsLoading = false;
      }
    },
    
    // 根据部门ID获取员工列表
    async fetchEmployees(departmentId) {
      if (!departmentId) return;
      
      this.employeesLoading = true;
      this.employeesList = [];
      this.formData.ownerId = null;
      this.formData.ownerName = '';
      this.formData.wechatName = '';
      
      try {
        const res = await axios.get(`${this.API_BASE_URL}/employees/selectAllByDepartmentId/${departmentId}`);
        if (res.data.code == 200) {
          this.employeesList = res.data.data;
          if (this.employeesList.length === 0) {
            this.$message.warning('该部门暂无员工可选');
          }
        } else {
          this.$message.error(res.data.message || '获取员工列表失败');
        }
      } catch (error) {
        console.error("获取员工列表异常:", error);
        this.$message.error('获取员工列表失败');
      } finally {
        this.employeesLoading = false;
      }
    },
    
    // 部门选择变更
    handleDepartmentChange(departmentId) {
      this.fetchEmployees(departmentId);
    },
    
    // 员工选择变更
    handleEmployeeChange(employeeId) {
      const selectedEmployee = this.employeesList.find(emp => emp.id === employeeId);
      if (selectedEmployee) {
        this.formData.ownerName = selectedEmployee.name || '';
        this.formData.wechatName = selectedEmployee.wechatName || '';
      } else {
        this.formData.ownerName = '';
        this.formData.wechatName = '';
      }
    },
    
    // 表单提交
    submitForm() {
      this.$refs.planForm.validate(async (valid) => {
        if (!valid) {
          this.$message.error('请检查表单填写是否正确');
          return;
        }
        
        // 验证关键数据
        if (!this.formData.projectId) {
          this.$message.error('请指定所属项目ID');
          return;
        }
        
        // 验证计划日期逻辑
        if (this.formData.plannedStartDate && this.formData.plannedEndDate) {
          const startDate = new Date(this.formData.plannedStartDate);
          const endDate = new Date(this.formData.plannedEndDate);
          if (startDate > endDate) {
            this.$message.error('计划结束日期不能早于计划开始日期');
            return;
          }
        }
        
        this.isSubmitting = true;
        
        // 准备API提交数据
        const payload = {
          projectId: this.formData.projectId,
          projectName: this.formData.projectName,
          taskName: this.formData.taskName,
          taskDesc: this.formData.taskDesc,
          plannedStartDate: this.formData.plannedStartDate,
          plannedEndDate: this.formData.plannedEndDate,
          estimatedValue: this.formData.estimatedValue ? Number(this.formData.estimatedValue) : null,
          estimatedCost: this.formData.estimatedCost ? Number(this.formData.estimatedCost) : null,
          actualCost: this.formData.actualCost ? Number(this.formData.actualCost) : null,
          progress: String(this.formData.progress),
          status: this.formData.status,
          ownerId: this.formData.ownerId,
          ownerName: this.formData.ownerName,
          priority: this.formData.priority,
          ownerWechat: this.formData.wechatName
        };
        
        try {
          const res = await axios.post(`${this.API_BASE_URL}/task/add`, payload);
          
          if (res.data.code === "200") {
            // 先重置表单
            this.resetForm();
            // 显示成功提示
            this.$notify({
              title: '成功',
              message: res.data.message || '计划创建成功',
              type: 'success',
              duration: 2000
            });
            // 延迟返回上一页，确保用户看到成功提示
            setTimeout(() => {
              this.$router.go(-1);
            }, 1000);
          } else {
            this.$message.error(res.data.message || '计划创建失败，请重试');
          }
        } catch (error) {
          console.error("计划创建异常:", error);
          this.$message.error('计划创建失败，请重试');
        } finally {
          this.isSubmitting = false;
        }
      });
    },
    
    // 重置表单
    resetForm() {
      // 重置表单验证
      this.$refs.planForm.resetFields();
      
      // 重置表单数据
      Object.assign(this.formData, {
        taskName: '',
        taskDesc: '',
        priority: '',
        plannedStartDate: null,
        plannedEndDate: null,
        estimatedValue: '',
        estimatedCost: '',
        actualCost: '',
        progress: 0,
        status: '',
        ownerId: null,
        ownerName: '',
        wechatName: ''
      });
      
      // 重置部门选择
      this.selectedDepartment = null;
      this.employeesList = [];
    },
    
    // 取消操作
    cancel() {
      this.$router.go(-1); // 返回上一页
    }
  }
};
</script>

<style scoped>
.plan-add-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.page-header h2 {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  padding: 15px 0;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px; /* 为滚动条预留空间 */
  padding-bottom: 80px; /* 为底部按钮预留空间 */
}

.plan-form {
  max-width: 900px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 20px;
  border-radius: 6px;
}

.project-info {
  display: flex;
  align-items: center;
  line-height: 40px;
}

.project-info i {
  color: #5e8acb;
  margin-right: 10px;
}

.project-info .label {
  color: #606266;
  margin-right: 10px;
}

.project-info .info-text {
  color: #303133;
  font-weight: 600;
  font-size: 15px;
}

.selected-manager-info {
  background-color: #f0f9eb;
  color: #67c23a;
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.selected-manager-info i {
  margin-right: 10px;
}

.bottom-spacer {
  height: 80px; /* 与底部固定按钮区域高度一致 */
}

.fixed-bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: rgba(245, 247, 250, 0.9);
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.fixed-bottom-actions .el-button {
  padding: 12px 35px;
  margin: 0 15px;
}

/* 定制滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .plan-form {
    max-width: 100%;
  }
  
  .fixed-bottom-actions .el-button {
    padding: 10px 20px;
    margin: 0 10px;
  }
}
</style> 