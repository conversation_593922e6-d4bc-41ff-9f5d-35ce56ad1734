<template>
  <div class="plan-update-container">
    <div class="page-header-fixed">
      <div class="header-content">
        <el-button 
          icon="el-icon-back" 
          @click="goBackToDetail"
          class="back-button">
          返回计划详情
        </el-button>
        <h2>编辑计划</h2>
      </div>
    </div>

    <div class="scrollable-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="10" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="initialError" class="error-state">
        <i class="el-icon-warning-outline"></i>
        <p>{{ initialError }}</p>
        <el-button type="primary" size="small" @click="retryLoad">重试</el-button>
      </div>

      <!-- 表单内容 -->
      <el-form 
        v-else
        :model="formData" 
        :rules="rules" 
        ref="planForm" 
        label-width="120px" 
        class="plan-form">
        
        <!-- 基本信息 -->
        <el-card class="form-card">
          <div slot="header">
            <span>基本信息</span>
          </div>
          
          <el-form-item label="计划名称" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入计划名称"></el-input>
          </el-form-item>
  
          <el-form-item label="计划描述" prop="taskDesc">
            <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入计划描述" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </el-form-item>
  
          <el-form-item label="所属项目">
            <span>{{ formData.projectName || '未知项目' }}</span>
          </el-form-item>
  
          <el-form-item label="当前状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item label="当前进度" prop="progress">
            <el-slider v-model="formData.progress" :min="0" :max="100" :step="1" show-input></el-slider>
          </el-form-item>
        </el-card>
  
        <!-- 时间与成本 -->
        <el-card class="form-card">
          <div slot="header">
            <span>时间与成本</span>
          </div>
          
          <el-form-item label="计划开始日期" prop="plannedStartDate">
            <el-date-picker
              v-model="formData.plannedStartDate"
              type="date"
              placeholder="选择开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
  
          <el-form-item label="计划结束日期" prop="plannedEndDate">
            <el-date-picker
              v-model="formData.plannedEndDate"
              type="date"
              placeholder="选择结束日期"
              value-format="yyyy-MM-dd"
              :picker-options="{ disabledDate: disabledEndDate }"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
  
          <el-form-item label="实际开始日期" prop="actualStartDate">
            <el-date-picker
              v-model="formData.actualStartDate"
              type="date"
              placeholder="选择实际开始日期（可选）"
              value-format="yyyy-MM-dd"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
  
          <el-form-item label="实际结束日期" prop="actualEndDate">
            <el-date-picker
              v-model="formData.actualEndDate"
              type="date"
              placeholder="选择实际结束日期（可选）"
              value-format="yyyy-MM-dd"
              style="width: 100%">
            </el-date-picker>
          </el-form-item>
  
          <el-form-item label="预估价值" prop="estimatedValue">
            <el-input-number v-model="formData.estimatedValue" :precision="2" :step="100" :min="0" style="width: 100%"></el-input-number>
          </el-form-item>
  
          <el-form-item label="预计成本" prop="estimatedCost">
            <el-input-number v-model="formData.estimatedCost" :precision="2" :step="100" :min="0" style="width: 100%"></el-input-number>
          </el-form-item>
  
          <el-form-item label="实际成本" prop="actualCost">
            <el-input-number v-model="formData.actualCost" :precision="2" :step="100" :min="0" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-card>
  
        <!-- 负责人信息 -->
        <el-card class="form-card">
          <div slot="header">
            <span>负责人信息</span>
          </div>
          
          <el-form-item label="选择部门" prop="departmentId">
            <el-select 
              v-model="selectedDepartment" 
              placeholder="请选择部门" 
              @change="handleDepartmentChange"
              :loading="departmentsLoading"
              style="width: 100%">
              <el-option
                v-for="item in departmentsList"
                :key="item.id"
                :label="item.departmentName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item label="选择负责人" prop="ownerId">
            <el-select 
              v-model="formData.ownerId" 
              placeholder="请选择负责人" 
              @change="handleEmployeeChange"
              :loading="employeesLoading"
              :disabled="!selectedDepartment || employeesList.length === 0"
              style="width: 100%">
              <el-option
                v-for="item in employeesList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
                <span>{{ item.name }}</span>
                <span v-if="item.wechatName" style="float: right; color: #8492a6; font-size: 13px">
                  微信: {{ item.wechatName }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item label="负责人姓名">
            <span>{{ formData.ownerName || '未设置' }}</span>
          </el-form-item>
  
          <el-form-item label="负责人微信">
            <span>{{ formData.ownerWechat || '未提供' }}</span>
          </el-form-item>
        </el-card>
  
        <!-- 参与人员 -->
        <el-card class="form-card">
          <div slot="header">
            <span>参与人员</span>
          </div>
          
          <el-form-item label="选择参与人">
            <el-button 
              type="primary" 
              plain 
              icon="el-icon-plus" 
              @click="openParticipantsDialog">
              选择参与人员
            </el-button>
            <span v-if="participants.length" class="participant-count">
              已选择 {{ participants.length }} 人
            </span>
          </el-form-item>
  
          <el-form-item label="已选参与人">
            <div class="participant-list">
              <el-tag 
                v-for="item in participants" 
                :key="item.id"
                closable
                @close="removeParticipant(item.id)"
                class="participant-tag">
                {{ item.name }}
              </el-tag>
              <div v-if="!participants.length" class="empty-participants">暂无参与人</div>
            </div>
          </el-form-item>
        </el-card>
  
        <!-- 表单提交按钮 -->
        <div class="form-actions-fixed">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="saving" :disabled="saving">保存修改</el-button>
        </div>
      </el-form>
    </div>

    <!-- 参与人员选择对话框 -->
    <el-dialog
      title="选择参与人员"
      :visible.sync="participantsDialogVisible"
      width="60%">
      <div class="dialog-content">
        <!-- 部门过滤器 -->
        <div class="filter-section">
          <span class="filter-label">过滤部门:</span>
          <el-select 
            v-model="selectedFilterDepartment" 
            placeholder="全部部门"
            @change="handleFilterDepartmentChange"
            style="width: 200px">
            <el-option
              key="-1"
              label="全部部门"
              :value="-1">
            </el-option>
            <el-option
              v-for="item in departmentsList"
              :key="item.id"
              :label="item.departmentName"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
        
        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            placeholder="搜索人员姓名"
            prefix-icon="el-icon-search"
            v-model="searchQuery">
          </el-input>
        </div>
        
        <!-- 员工列表 -->
        <div class="employee-list" v-loading="loadingAllEmployees">
          <div v-if="filteredEmployees.length === 0" class="empty-list">
            <span v-if="loadingAllEmployees">加载员工列表中...</span>
            <span v-else-if="selectedFilterDepartment !== -1">该部门暂无人员</span>
            <span v-else-if="searchQuery">无匹配人员</span>
            <span v-else>暂无员工数据</span>
          </div>
          
          <el-checkbox-group v-model="selectedEmployeeIds">
            <div v-for="employee in filteredEmployees" 
                :key="employee.id" 
                class="employee-item">
              <el-checkbox :label="employee.id">
                <span>{{ employee.name }}</span>
                <span v-if="selectedFilterDepartment === -1 && employee.departmentName" 
                      class="employee-department">
                  ({{ employee.departmentName }})
                </span>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="participantsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmParticipants">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'PlanUpdate',
  data() {
    return {
      loading: true,
      saving: false,
      initialError: null,
      taskId: null,
      
      formData: {
        taskId: null,
        projectId: null,
        projectName: '',
        taskName: '',
        taskDesc: '',
        progress: 0,
        status: '',
        plannedStartDate: null,
        plannedEndDate: null,
        actualStartDate: null,
        actualEndDate: null,
        estimatedValue: null,
        estimatedCost: null,
        actualCost: null,
        ownerId: null,
        ownerName: '',
        ownerWechat: ''
      },
      
      // 验证规则
      rules: {
        taskName: [
          { required: true, message: '任务标题不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择任务状态', trigger: 'change' }
        ],
        progress: [
          { required: true, message: '当前进度不能为空', trigger: 'change' }
        ]
      },
      
      // 状态选项
      statusOptions: [
        { value: '草稿', text: '草稿' },
        { value: '待处理', text: '待处理' },
        { value: '进行中', text: '进行中' },
        { value: '暂停/挂起', text: '暂停/挂起' },
        { value: '已完成', text: '已完成' },
        { value: '失败', text: '失败' },
        { value: '已取消', text: '已取消' },
        { value: '已拒绝', text: '已拒绝' },
        { value: '已归档', text: '已归档' }
      ],
      
      // 部门相关
      departmentsList: [],
      selectedDepartment: null,
      departmentsLoading: false,
      
      // 员工相关
      employeesList: [],
      employeesLoading: false,
      
      // 所有员工数据
      allEmployees: [],
      loadingAllEmployees: false,
      
      // 参与人
      participants: [],
      participantsDialogVisible: false,
      selectedFilterDepartment: -1,
      searchQuery: '',
      selectedEmployeeIds: [],
      
      // API基础URL
      API_BASE_URL: 'http://projectmanagement.jenasi.ai:99'
    };
  },
  
  computed: {
    // 为参与人选择对话框过滤的员工列表
    filteredEmployees() {
      if (!this.allEmployees || this.allEmployees.length === 0) {
        return [];
      }
      
      // 先根据部门筛选
      let result = this.allEmployees;
      if (this.selectedFilterDepartment !== -1) {
        result = result.filter(emp => emp.departmentId === this.selectedFilterDepartment);
      }
      
      // 再根据搜索词筛选
      const query = this.searchQuery?.toLowerCase().trim();
      if (query) {
        result = result.filter(emp => 
          emp.name && emp.name.toLowerCase().includes(query)
        );
      }
      
      return result;
    }
  },
  
  created() {
    // 从路由获取任务ID
    const id = this.$route.params.id;
    if (id) {
      this.taskId = id;
      this.loadData();
    } else {
      this.initialError = '任务ID缺失，无法加载';
      this.loading = false;
    }
  },
  
  methods: {
    // 返回计划详情
    goBackToDetail() {
      this.$router.push(`/home/<USER>/task/detail/${this.taskId}`);
    },
    
    // 禁用早于开始日期的结束日期选择
    disabledEndDate(date) {
      if (!this.formData.plannedStartDate) {
        return false;
      }
      return date.getTime() < new Date(this.formData.plannedStartDate).getTime();
    },
    
    // 格式化日期用于表单
    formatDateForPicker(dateString) {
      if (!dateString) return null;
      
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return null;
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
      } catch (e) {
        console.error('日期格式化错误:', e);
        return null;
      }
    },
    
    // 初始化加载所有数据
    async loadData() {
      this.loading = true;
      this.initialError = null;
      
      try {
        // 1. 加载任务详情
        await this.fetchTaskDetails();
        
        // 2. 加载部门列表
        await this.fetchDepartments();
        
        // 3. 加载所有员工
        await this.fetchAllEmployees();
        
        // 4. 根据任务详情中的所有者，设置正确的部门和员工选项
        this.setupOwnerSelection();
        
      } catch (error) {
        console.error('加载数据失败:', error);
        this.initialError = typeof error === 'string' ? error : '加载数据失败，请重试';
      } finally {
        this.loading = false;
      }
    },
    
    // 重试加载
    retryLoad() {
      this.loadData();
    },
    
    // 获取任务详情
    async fetchTaskDetails() {
      try {
        const res = await axios.get(`${this.API_BASE_URL}/task/selectById/${this.taskId}`);
        
        if (String(res.data.code) !== "200") {
          throw new Error(res.data.message || '获取任务详情失败');
        }
        
        const data = res.data.data;
        this.formData = {
          taskId: data.taskId || this.taskId,
          projectId: data.projectId,
          projectName: data.projectName || '',
          taskName: data.taskName || '',
          taskDesc: data.taskDesc || '',
          progress: data.progress ? parseFloat(data.progress) : 0,
          status: data.status || '草稿',
          plannedStartDate: this.formatDateForPicker(data.plannedStartDate),
          plannedEndDate: this.formatDateForPicker(data.plannedEndDate),
          actualStartDate: this.formatDateForPicker(data.actualStartDate),
          actualEndDate: this.formatDateForPicker(data.actualEndDate),
          estimatedValue: data.estimatedValue != null ? parseFloat(data.estimatedValue) : null,
          estimatedCost: data.estimatedCost != null ? parseFloat(data.estimatedCost) : null,
          actualCost: data.actualCost != null ? parseFloat(data.actualCost) : null,
          ownerId: data.ownerId || null,
          ownerName: data.ownerName || '',
          ownerWechat: data.ownerWechat || ''
        };
        
        // 初始化参与人列表
        this.participants = data.participants || [];
        this.selectedEmployeeIds = this.participants.map(p => p.id);
        
        return data;
      } catch (error) {
        console.error('获取任务详情失败:', error);
        throw error;
      }
    },
    
    // 获取部门列表
    async fetchDepartments() {
      this.departmentsLoading = true;
      try {
        const res = await axios.get(`${this.API_BASE_URL}/departments/selectAll`);
        if (res.data.code === 200 || res.data.code === "200") {
          this.departmentsList = res.data.data || [];
          return this.departmentsList;
        } else {
          throw new Error(res.data.message || '获取部门列表失败');
        }
      } catch (error) {
        console.error('获取部门列表失败:', error);
        this.$message.error('获取部门列表失败');
        return [];
      } finally {
        this.departmentsLoading = false;
      }
    },
    
    // 获取所有员工
    async fetchAllEmployees() {
      this.loadingAllEmployees = true;
      try {
        const res = await axios.get(`${this.API_BASE_URL}/employees/selectAll`);
        if (res.data.code === 200 || res.data.code === "200") {
          this.allEmployees = res.data.data || [];
          return this.allEmployees;
        } else {
          throw new Error(res.data.message || '获取员工列表失败');
        }
      } catch (error) {
        console.error('获取所有员工失败:', error);
        this.$message.error('获取员工列表失败');
        return [];
      } finally {
        this.loadingAllEmployees = false;
      }
    },
    
    // 根据部门ID获取员工列表
    async fetchEmployees(departmentId) {
      if (!departmentId) return;
      
      this.employeesLoading = true;
      this.employeesList = [];
      
      try {
        const res = await axios.get(`${this.API_BASE_URL}/employees/selectAllByDepartmentId/${departmentId}`);
        if (res.data.code === 200 || res.data.code === "200") {
          this.employeesList = res.data.data || [];
          return this.employeesList;
        } else {
          throw new Error(res.data.message || '获取员工列表失败');
        }
      } catch (error) {
        console.error('获取员工列表失败:', error);
        this.$message.error('获取员工列表失败');
        return [];
      } finally {
        this.employeesLoading = false;
      }
    },
    
    // 根据初始加载的负责人设置选择
    setupOwnerSelection() {
      if (!this.formData.ownerId || !this.allEmployees || this.allEmployees.length === 0) {
        return;
      }
      
      // 查找负责人对应的员工和部门
      const owner = this.allEmployees.find(emp => emp.id === this.formData.ownerId);
      if (owner && owner.departmentId) {
        // 设置选中的部门
        this.selectedDepartment = owner.departmentId;
        
        // 获取该部门的员工列表
        this.fetchEmployees(owner.departmentId).then(() => {
          // 不需要重新设置 ownerId，因为它已经在 formData 中
          // 但确保 ownerName 和 ownerWechat 是最新的
          const employee = this.employeesList.find(emp => emp.id === this.formData.ownerId);
          if (employee) {
            this.formData.ownerName = employee.name || '';
            this.formData.ownerWechat = employee.wechatName || '';
          }
        });
      }
    },
    
    // 部门选择变更
    handleDepartmentChange(departmentId) {
      if (departmentId !== this.selectedDepartment) {
        this.fetchEmployees(departmentId);
        // 重置负责人选择
        this.formData.ownerId = null;
        this.formData.ownerName = '';
        this.formData.ownerWechat = '';
      }
    },
    
    // 员工选择变更
    handleEmployeeChange(employeeId) {
      const selectedEmployee = this.employeesList.find(emp => emp.id === employeeId);
      if (selectedEmployee) {
        this.formData.ownerName = selectedEmployee.name || '';
        this.formData.ownerWechat = selectedEmployee.wechatName || '';
      } else {
        this.formData.ownerName = '';
        this.formData.ownerWechat = '';
      }
    },
    
    // 打开参与人员选择对话框
    openParticipantsDialog() {
      // 重置为当前已选参与人
      this.selectedEmployeeIds = this.participants.map(p => p.id);
      this.searchQuery = '';
      this.selectedFilterDepartment = -1;
      this.participantsDialogVisible = true;
    },
    
    // 参与人部门过滤变更
    handleFilterDepartmentChange() {
      // 不需要重置搜索，只是重新计算过滤结果
    },
    
    // 确认参与人选择
    confirmParticipants() {
      // 更新参与人列表
      this.participants = this.selectedEmployeeIds.map(id => {
        const employee = this.allEmployees.find(emp => emp.id === id);
        return employee ? {
          id: employee.id,
          name: employee.name || '',
          wechat: employee.wechatName || ''
        } : null;
      }).filter(Boolean); // 过滤掉无效值
      
      this.participantsDialogVisible = false;
    },
    
    // 移除参与人
    removeParticipant(id) {
      this.participants = this.participants.filter(p => p.id !== id);
      const idIndex = this.selectedEmployeeIds.indexOf(id);
      if (idIndex !== -1) {
        this.selectedEmployeeIds.splice(idIndex, 1);
      }
    },
    
    // 表单提交
    submitForm() {
      this.$refs.planForm.validate(async (valid) => {
        if (!valid) {
          this.$message.error('请检查表单填写是否正确');
          return;
        }
        
        this.saving = true;
        
        try {
          // 准备主任务数据
          const payload = {
            taskId: this.formData.taskId,
            projectId: this.formData.projectId,
            projectName: this.formData.projectName,
            taskName: this.formData.taskName,
            taskDesc: this.formData.taskDesc,
            plannedStartDate: this.formData.plannedStartDate,
            plannedEndDate: this.formData.plannedEndDate,
            actualStartDate: this.formData.actualStartDate,
            actualEndDate: this.formData.actualEndDate,
            estimatedValue: this.formData.estimatedValue,
            estimatedCost: this.formData.estimatedCost,
            actualCost: this.formData.actualCost,
            progress: String(this.formData.progress),
            status: this.formData.status,
            ownerId: this.formData.ownerId,
            ownerName: this.formData.ownerName,
            ownerWechat: this.formData.ownerWechat
          };
          
          // 更新主任务数据
          const taskRes = await axios.post(`${this.API_BASE_URL}/task/update`, payload);
          
          if (taskRes.data.code !== 200) {
            throw new Error(taskRes.data.message || '更新任务失败');
          }
          
          // 更新参与人数据
          const participantsPayload = this.participants.map(p => ({
            id: p.id,
            name: p.name || '',
            wechat: p.wechat || ''
          }));
          
          try {
            const participantsRes = await axios.post(
              `${this.API_BASE_URL}/task/updateParticipantsById/${this.taskId}`,
              participantsPayload
            );
            
            if (participantsRes.data.code !== 200) {
              console.warn('参与人更新失败:', participantsRes.data.message);
              this.$message.warning('任务已更新，但参与人保存失败');
            }
          } catch (participantsError) {
            console.error('参与人更新异常:', participantsError);
            this.$message.warning('任务已更新，但参与人保存失败');
          }
          
          this.$message.success('任务更新成功');
          this.$router.go(-1); // 返回上一页
          
        } catch (error) {
          console.error('更新任务失败:', error);
          this.$message.error(error.message || '更新任务失败，请重试');
        } finally {
          this.saving = false;
        }
      });
    },
    
    // 取消编辑
    cancel() {
      this.$router.go(-1); // 返回上一页
    }
  }
};
</script>

<style scoped>
.plan-update-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
  position: relative;
}

.page-header-fixed {
  padding: 15px 20px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 999;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
}

.page-header-fixed h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  text-align: center;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

.form-actions-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 15px 0;
  box-shadow: 0 -2px 12px 0 rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  z-index: 999;
}

.form-actions-fixed .el-button {
  margin: 0 15px;
  padding: 12px 35px;
}

.plan-form {
  max-width: 900px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.loading-state,
.error-state {
  max-width: 900px;
  margin: 20px auto;
  padding: 20px;
  border-radius: 6px;
  background-color: #fff;
}

.error-state {
  text-align: center;
  color: #f56c6c;
  padding: 40px 20px;
}

.error-state i {
  font-size: 40px;
  margin-bottom: 15px;
}

.participant-count {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.participant-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.participant-tag {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-participants {
  color: #909399;
  padding: 5px 0;
}

.bottom-spacer {
  height: 100px; /* 为底部按钮预留空间 */
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background-color: #909399;
}

/* 参与人选择对话框样式 */
.dialog-content {
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.filter-section,
.search-section {
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
}

.filter-label {
  margin-right: 10px;
  color: #606266;
}

.employee-list {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  max-height: 350px;
  overflow-y: auto;
  padding: 0 10px;
}

.empty-list {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.employee-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.employee-item:last-child {
  border-bottom: none;
}

.employee-department {
  margin-left: 8px;
  color: #909399;
  font-size: 13px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .page-header-fixed h2 {
    font-size: 18px;
  }
  
  .scrollable-content {
    padding: 15px;
  }
  
  .plan-form {
    max-width: 100%;
  }
  
  .form-actions-fixed .el-button {
    padding: 10px 20px;
    margin: 0 8px;
  }
}
</style> 