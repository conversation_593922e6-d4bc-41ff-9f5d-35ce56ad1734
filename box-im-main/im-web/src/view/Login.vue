<template>
	<div class="login-container">
		<div class="login-box">
			<div class="login-title">登录</div>
			<el-form ref="formRef" :model="loginForm" :rules="rules" label-width="0" @keyup.enter.native="submitForm">
				<el-form-item>
					<el-radio-group v-model="loginMode" @change="handleModeChange">
						<el-radio-button label="username">用户名登录</el-radio-button>
						<el-radio-button label="phone">手机号登录</el-radio-button>
						<el-radio-button label="email">邮箱登录</el-radio-button>
					</el-radio-group>
				</el-form-item>

				<!-- 用户名登录 -->
				<template v-if="loginMode === 'username'">
					<el-form-item prop="userName">
						<el-input v-model="loginForm.userName" placeholder="请输入用户名" />
					</el-form-item>
					<el-form-item prop="password">
						<el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password />
					</el-form-item>
				</template>

				<!-- 手机号登录 -->
				<template v-if="loginMode === 'phone'">
					<el-form-item prop="phone">
						<el-input v-model="loginForm.phone" placeholder="请输入手机号" />
					</el-form-item>
					<el-form-item v-if="loginForm.loginType === 'password'" prop="password">
						<el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password />
					</el-form-item>
					<el-form-item v-else prop="code">
						<div class="code-input">
							<el-input v-model="loginForm.code" placeholder="请输入验证码" @keyup.enter.native="submitForm" />
							<el-button type="primary" :disabled="countdown > 0" @click="getCode('phone')">
								{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
							</el-button>
						</div>
					</el-form-item>
				</template>

				<!-- 邮箱登录 -->
				<template v-if="loginMode === 'email'">
					<el-form-item prop="email">
						<el-input v-model="loginForm.email" placeholder="请输入邮箱" />
					</el-form-item>
					<el-form-item v-if="loginForm.loginType === 'password'" prop="password">
						<el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password />
					</el-form-item>
					<el-form-item v-else prop="code">
						<div class="code-input">
							<el-input v-model="loginForm.code" placeholder="请输入验证码" @keyup.enter.native="submitForm" />
							<el-button type="primary" :disabled="countdown > 0" @click="getCode('email')">
								{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
							</el-button>
						</div>
					</el-form-item>
				</template>

				<el-form-item>
					<div class="login-options">
						<el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
						<el-radio-group v-if="loginMode !== 'username'" v-model="loginForm.loginType" size="small">
							<el-radio label="password">密码登录</el-radio>
							<el-radio label="code">验证码登录</el-radio>
						</el-radio-group>
					</div>
				</el-form-item>

				<el-form-item>
					<el-button type="primary" :loading="loading" @click="submitForm">登录</el-button>
					<el-button @click="resetForm">重置</el-button>
					<router-link :to="`/register?mode=${loginMode}`" class="register-link">没有账号？前往注册</router-link>
				</el-form-item>
			</el-form>
		</div>
		<captcha-image ref="captchaRef"></captcha-image>
	</div>
</template>

<script>
import Icp from '../components/common/Icp.vue'
import CaptchaImage from '../components/common/CaptchaImage.vue';

export default {
	name: "login",
	components: {
		Icp,
		CaptchaImage
	},
	data() {
		return {
			loginForm: {
				userName: '',
				password: '',
				phone: '',
				email: '',
				code: '',
				loginType: 'password',
				terminal: 0,
				remember: false
			},
			rules: {
				userName: [
					{ required: true, message: '请输入用户名', trigger: 'blur' }
				],
				password: [
					{ 
						required: true, 
						message: '请输入密码', 
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (this.loginForm.loginType === 'code') {
								callback();
							} else if (!value) {
								callback(new Error('请输入密码'));
							} else {
								callback();
							}
						}
					}
				],
				phone: [
					{ required: true, message: '请输入手机号', trigger: 'blur' },
					{ pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
				],
				email: [
					{ required: true, message: '请输入邮箱', trigger: 'blur' },
					{ type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
				],
				code: [
					{ 
						required: true, 
						message: '请输入验证码', 
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (this.loginForm.loginType === 'code' && !value) {
								callback(new Error('请输入验证码'));
							} else {
								callback();
							}
						}
					}
				]
			},
			phoneLockTime: 0,
			phoneLockTimer: null,
			emailLockTime: 0,
			emailLockTimer: null,
			loginMode: 'username',
			countdown: 0,
			loading: false
		};
	},
	methods: {
		resetForm() {
			// 重置表单数据
			this.loginForm = {
				userName: '',
				password: '',
				phone: '',
				email: '',
				code: '',
				loginType: 'password',
				terminal: 0,
				remember: false
			};
			// 重置表单验证
			this.$nextTick(() => {
				if (this.$refs.formRef) {
					this.$refs.formRef.clearValidate();
				}
			});
		},
		submitForm() {
			this.$refs.formRef.validate((valid) => {
				if (valid) {
					this.loading = true;
					let url = '/login';
					let data = {
						...this.loginForm
					};
					
					// 根据登录方式和类型构建登录数据
					if (this.loginMode === 'phone') {
						url = '/login/phone';
						if (this.loginForm.loginType === 'code') {
							// 手机号验证码登录
							data = {
								phone: this.loginForm.phone,
								code: this.loginForm.code,
								loginType: 'code',
								terminal: this.loginForm.terminal
							};
						} else {
							// 手机号密码登录
							data = {
								phone: this.loginForm.phone,
								password: this.loginForm.password,
								loginType: 'password',
								terminal: this.loginForm.terminal
							};
						}
					} else if (this.loginMode === 'email') {
						url = '/login/email';
						if (this.loginForm.loginType === 'code') {
							// 邮箱验证码登录
							data = {
								email: this.loginForm.email,
								code: this.loginForm.code,
								loginType: 'code',
								terminal: this.loginForm.terminal
							};
						} else {
							// 邮箱密码登录
							data = {
								email: this.loginForm.email,
								password: this.loginForm.password,
								loginType: 'password',
								terminal: this.loginForm.terminal
							};
						}
					} else {
						// 用户名密码登录
						data = {
							userName: this.loginForm.userName,
							password: this.loginForm.password,
							loginType: 'password',
							terminal: this.loginForm.terminal
						};
					}
					
					this.$http({
						url: url,
						method: 'post',
						data: data
					})
						.then((data) => {
							// 只在用户选择记住密码时才保存密码
							if (this.loginForm.remember) {
								// 设置cookie过期时间为30天
								const expireDate = new Date();
								expireDate.setDate(expireDate.getDate() + 30);
								document.cookie = `username=${escape(this.loginForm.userName)};expires=${expireDate.toUTCString()}`;
								document.cookie = `password=${escape(this.loginForm.password)};expires=${expireDate.toUTCString()}`;
								document.cookie = `remember=true;expires=${expireDate.toUTCString()}`;
							} else {
								// 如果用户没有选择记住密码，清除之前保存的cookie
								document.cookie = 'username=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
								document.cookie = 'password=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
								document.cookie = 'remember=false;expires=Thu, 01 Jan 1970 00:00:00 GMT';
							}
							// 保存token
							sessionStorage.setItem("accessToken", data.accessToken);
							sessionStorage.setItem("refreshToken", data.refreshToken);
							this.$message.success("登录成功");
							this.$router.push("/home/<USER>");
						})
						.catch(error => {
							this.$message.error("登录失败: " + (error.response ? error.response.data.message : error.message));
						})
						.finally(() => {
							this.loading = false;
						});
				}
			});
		},
		// 获取cookie、
		getCookie(name) {
			let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
			let arr = document.cookie.match(reg)
			if (arr) {
				return unescape(arr[2]);
			}
			return '';
		},
		// 设置cookie,增加到vue实例方便全局调用
		setCookie(name, value) {
			document.cookie = name + "=" + escape(value);
		},
		onLoadSmsCaptcha() {
			this.$refs.formRef.validateField('phone', (valid) => {
				if (valid == '') {
					// 发短信前先验证验证码，防止盗刷
					console.log('打开验证码对话框');
					this.$refs.captchaRef.open((id, code) => {
						console.log('验证码验证通过，id:', id, 'code:', code);
						// 60s内不允许再次发送
						this.phoneLockTime = 60;
						this.phoneLockTimer && clearInterval(this.phoneLockTimer);
						this.phoneLockTimer = setInterval(() => {
							this.phoneLockTime -= 1;
							if (this.phoneLockTime <= 0) {
								this.phoneLockTimer && clearInterval(this.phoneLockTimer);
							}
						}, 1000)
						// 发送短信
						let data = {
							phone: this.loginForm.phone,
							id: id,
							code: code
						}
						console.log('发送短信验证码请求:', data);
						this.$http({
							url: "/captcha/sms/code",
							method: 'post',
							data: data
						}).then(() => {
							this.$message.success("验证码已发送至您的手机，请注意查收")
						}).catch(error => {
							console.error('发送短信验证码失败:', error);
							this.$message.error("获取验证码失败: " + (error.response ? error.response.data.message : error.message));
							// 重置倒计时
							this.phoneLockTime = 0;
							this.phoneLockTimer && clearInterval(this.phoneLockTimer);
						});
					});
				}
			})
		},
		onLoadMailCaptcha() {
			this.$refs.formRef.validateField('email', (valid) => {
				if (valid == '') {
					// 发邮件前先验证验证码，防止盗刷
					console.log('打开验证码对话框');
					this.$refs.captchaRef.open((id, code) => {
						console.log('验证码验证通过，id:', id, 'code:', code);
						// 60s内不允许再次发送
						this.emailLockTime = 60;
						this.emailLockTimer && clearInterval(this.emailLockTimer);
						this.emailLockTimer = setInterval(() => {
							this.emailLockTime -= 1;
							if (this.emailLockTime <= 0) {
								this.emailLockTimer && clearInterval(this.emailLockTimer);
							}
						}, 1000)
						// 发送邮件
						let data = {
							email: this.loginForm.email,
							id: id,
							code: code
						}
						console.log('发送邮件验证码请求:', data);
						this.$http({
							url: "/captcha/mail/code",
							method: 'post',
							data: data
						}).then(() => {
							this.$message.success("验证码已发送至您的邮箱，请注意查收")
						}).catch(error => {
							console.error('发送邮件验证码失败:', error);
							this.$message.error("获取验证码失败: " + (error.response ? error.response.data.message : error.message));
							// 重置倒计时
							this.emailLockTime = 0;
							this.emailLockTimer && clearInterval(this.emailLockTimer);
						});
					});
				}
			})
		},
		handleModeChange(mode) {
			this.loginMode = mode;
			this.resetForm();
		},
		async getCode(type) {
			try {
				if (type === 'phone') {
					if (!this.loginForm.phone) {
						this.$message.warning('请输入手机号');
						return;
					}
					if (!/^1[3-9]\d{9}$/.test(this.loginForm.phone)) {
						this.$message.warning('手机号格式不正确');
						return;
					}
					// 使用 open 方法打开验证码对话框
					this.$refs.captchaRef.open((id, code) => {
						// 发送短信验证码
						this.$http({
							url: "/captcha/sms/code",
							method: 'post',
							data: {
								phone: this.loginForm.phone,
								id: id,
								code: code
							}
						}).then(() => {
							this.$message.success("验证码已发送至您的手机，请注意查收");
							this.countdown = 60;
							const timer = setInterval(() => {
								this.countdown--;
								if (this.countdown <= 0) {
									clearInterval(timer);
								}
							}, 1000);
						}).catch(error => {
							this.$message.error("获取验证码失败: " + (error.response ? error.response.data.message : error.message));
						});
					});
				} else if (type === 'email') {
					if (!this.loginForm.email) {
						this.$message.warning('请输入邮箱');
						return;
					}
					if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(this.loginForm.email)) {
						this.$message.warning('邮箱格式不正确');
						return;
					}
					// 使用 open 方法打开验证码对话框
					this.$refs.captchaRef.open((id, code) => {
						// 发送邮箱验证码
						this.$http({
							url: "/captcha/mail/code",
							method: 'post',
							data: {
								email: this.loginForm.email,
								id: id,
								code: code
							}
						}).then(() => {
							this.$message.success("验证码已发送至您的邮箱，请注意查收");
							this.countdown = 60;
							const timer = setInterval(() => {
								this.countdown--;
								if (this.countdown <= 0) {
									clearInterval(timer);
								}
							}, 1000);
						}).catch(error => {
							this.$message.error("获取验证码失败: " + (error.response ? error.response.data.message : error.message));
						});
					});
				}
			} catch (error) {
				this.$message.error(error.message || '获取验证码失败');
			}
		},
		async sendSmsCode(phone) {
			const res = await this.$http({
				url: '/captcha/sms/code',
				method: 'post',
				data: { phone }
			});
			if (res.code === 0) {
				this.$message.success('验证码已发送');
			}
		},
		async sendEmailCode(email) {
			const res = await this.$http({
				url: '/captcha/mail/code',
				method: 'post',
				data: { email }
			});
			if (res.code === 0) {
				this.$message.success('验证码已发送');
			}
		}
	},
	mounted() {
		// 如果用户之前选择了记住密码，则从cookie中获取值
		const remember = this.getCookie("remember");
		if (remember === "true") {
			const savedUsername = this.getCookie("username");
			const savedPassword = this.getCookie("password");
			if (savedUsername && savedPassword) {
				this.loginForm.userName = unescape(savedUsername);
				this.loginForm.password = unescape(savedPassword);
				this.loginForm.remember = true;
			}
		}
	},
	computed:{
		loginNamePlaceholder(){
			let mode = this.$store.state.configStore.registration.mode;
			let strText = "用户名";
			if(mode.includes("phone")){
				strText += "/手机号"
			}
			if(mode.includes("email")){
				strText += "/邮箱"
			}
			return strText;
		}
	}
}
</script>

<style scoped lang="scss">
.login-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	background-color: #f5f5f5;
}

.login-box {
	width: 400px;
	padding: 20px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-title {
	text-align: center;
	font-size: 24px;
	margin-bottom: 20px;
}

.code-input {
	display: flex;
	gap: 10px;
}

.register-link {
	margin-left: 10px;
	color: #409EFF;
	text-decoration: none;
}

.register-link:hover {
	color: #66b1ff;
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>