<template>
  <div class="project-update-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button type="primary" icon="el-icon-back" @click="goBack">返回项目列表</el-button>
      <h2>修改项目信息</h2>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="initialFetchError" class="error-state">
      <el-result
        icon="error"
        :title="initialFetchError"
        sub-title="加载失败，请重试"
      >
        <template #extra>
          <el-button type="primary" @click="retryLoad">重试</el-button>
        </template>
      </el-result>
    </div>

    <!-- 表单内容 -->
    <el-form 
      v-else
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="project-form"
    >
      <!-- 基本信息 -->
      <el-card class="form-card">
        <div slot="header">
          <span class="card-title">基本信息</span>
        </div>
        
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目描述" prop="projectDesc">
          <el-input
            type="textarea"
            v-model="formData.projectDesc"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </el-form-item>

        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="formData.startDate"
            type="date"
            placeholder="选择开始时间"
            value-format="yyyy-MM-dd"
            @change="handleDateChange('startDate', $event)"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="formData.endDate"
            type="date"
            placeholder="选择结束时间"
            value-format="yyyy-MM-dd"
            @change="handleDateChange('endDate', $event)"
          />
        </el-form-item>
      </el-card>

      <!-- 负责人信息 -->
      <el-card class="form-card">
        <div slot="header">
          <span class="card-title">负责人</span>
        </div>

        <el-form-item label="所属部门" prop="departmentId">
          <el-select 
            v-model="formData.departmentId"
            placeholder="请选择部门"
            @change="handleDepartmentChange"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.departmentName"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择人员" prop="managerId">
          <el-select
            v-model="formData.managerId"
            placeholder="请选择人员"
            :loading="fetchingEmployees"
            :disabled="!formData.departmentId || fetchingEmployees"
            @change="handleEmployeeChange"
          >
            <el-option
              v-for="emp in employees"
              :key="emp.id"
              :label="emp.name"
              :value="emp.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="管理员微信" v-if="formData.managerWechat">
          <span class="readonly-value">{{ formData.managerWechat || '未提供' }}</span>
        </el-form-item>
      </el-card>

      <!-- 项目进度 -->
      <el-card class="form-card">
        <div slot="header">
          <span class="card-title">项目进度</span>
        </div>

        <el-form-item label="进度状态" prop="progressStatus">
          <el-select v-model="formData.progressStatus" placeholder="请选择状态">
            <el-option
              v-for="status in statusOptions"
              :key="status"
              :label="status"
              :value="status"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="当前进度(%)" prop="currentProgress">
          <el-input-number
            v-model="formData.currentProgress"
            :min="0"
            :max="100"
            :precision="0"
            @change="validateProgress('currentProgress')"
          />
        </el-form-item>

        <el-form-item label="计划进度(%)" prop="plannedProgress">
          <el-input-number
            v-model="formData.plannedProgress"
            :min="0"
            :max="100"
            :precision="0"
            @change="validateProgress('plannedProgress')"
          />
        </el-form-item>

        <el-form-item label="进度说明" prop="progressDesc">
          <el-input
            type="textarea"
            v-model="formData.progressDesc"
            :rows="4"
            placeholder="请输入进度说明"
          />
        </el-form-item>
      </el-card>

      <!-- 财务信息 -->
      <el-card class="form-card">
        <div slot="header">
          <span class="card-title">财务信息</span>
        </div>

        <el-form-item label="总价值" prop="totalValue">
          <el-input-number
            v-model="formData.totalValue"
            :precision="2"
            :step="1000"
            :min="0"
          />
        </el-form-item>

        <el-form-item label="总成本" prop="totalCost">
          <el-input-number
            v-model="formData.totalCost"
            :precision="2"
            :step="1000"
            :min="0"
          />
        </el-form-item>
      </el-card>

      <!-- 底部按钮 -->
      <div class="form-actions">
        <el-button @click="goBack">取消</el-button>
        <el-button 
          type="primary" 
          @click="saveChanges" 
          :loading="isSaving"
          :disabled="isSaving || loading"
        >
          {{ isSaving ? '保存中...' : '保存修改' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ProjectUpdate',
  data() {
    return {
      loading: true,
      isSaving: false,
      initialFetchError: null,
      departments: [],
      employees: [],
      fetchingEmployees: false,
      statusOptions: ['正常', '延期', '已完成', '未开始', '已暂停'],
      
      formData: {
        projectId: null,
        projectName: '',
        projectDesc: '',
        startDate: null,
        endDate: null,
        totalValue: 0,
        totalCost: 0,
        currentProgress: 0,
        plannedProgress: 0,
        progressStatus: '正常',
        progressDesc: '',
        departmentId: null,
        managerId: null,
        managerName: '',
        managerWechat: ''
      },

      rules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        departmentId: [
          { required: true, message: '请选择所属部门', trigger: 'change' }
        ],
        managerId: [
          { required: true, message: '请选择负责人', trigger: 'change' }
        ],
        progressStatus: [
          { required: true, message: '请选择进度状态', trigger: 'change' }
        ]
      }
    }
  },

  created() {
    const projectId = this.$route.params.id;
    if (projectId) {
      this.formData.projectId = parseInt(projectId);
      this.loadData();
    } else {
      this.initialFetchError = "项目ID无效";
      this.loading = false;
    }
  },

  methods: {
    goBack() {
      this.$router.push(`/home/<USER>/detail/${this.formData.projectId}`);
    },

    async loadData() {
      this.loading = true;
      this.initialFetchError = null;

      try {
        // 1. 获取项目详情
        const projectRes = await axios.get(`http://projectmanagement.jenasi.ai:99/project/selectById/${this.formData.projectId}`);
        if (projectRes.data.code === "200") {
          const projectData = projectRes.data.data;
          Object.assign(this.formData, projectData);
        }

        // 2. 获取部门列表
        const deptRes = await axios.get('http://projectmanagement.jenasi.ai:99/departments/selectAll');
        if (deptRes.data.code === "200") {
          this.departments = deptRes.data.data;
        }

        // 3. 如果有部门ID，获取该部门的人员列表
        if (this.formData.departmentId) {
          await this.fetchEmployeesByDepartment(this.formData.departmentId);
        }

      } catch (error) {
        console.error('加载数据失败:', error);
        this.initialFetchError = error.message || '加载失败';
      } finally {
        this.loading = false;
      }
    },

    async fetchEmployeesByDepartment(departmentId) {
      if (!departmentId) {
        this.employees = [];
        return;
      }

      this.fetchingEmployees = true;
      try {
        const res = await axios.get(`http://projectmanagement.jenasi.ai:99/employees/selectAllByDepartmentId/${departmentId}`);
        if (res.data.code === "200") {
          this.employees = res.data.data;
        }
      } catch (error) {
        console.error('获取部门人员失败:', error);
        this.$message.error('加载人员列表失败');
      } finally {
        this.fetchingEmployees = false;
      }
    },

    handleDepartmentChange(departmentId) {
      this.formData.managerId = null;
      this.formData.managerName = '';
      this.formData.managerWechat = '';
      this.fetchEmployeesByDepartment(departmentId);
    },

    handleEmployeeChange(employeeId) {
      const selectedEmployee = this.employees.find(emp => emp.id === employeeId);
      if (selectedEmployee) {
        this.formData.managerName = selectedEmployee.name;
        this.formData.managerWechat = selectedEmployee.wechatName || '';
      }
    },

    handleDateChange(field, value) {
      if (this.formData.startDate && this.formData.endDate) {
        if (new Date(this.formData.startDate) > new Date(this.formData.endDate)) {
          this.$message.warning('结束时间不能早于开始时间');
        }
      }
    },

    validateProgress(field) {
      const value = this.formData[field];
      if (value < 0) {
        this.formData[field] = 0;
      } else if (value > 100) {
        this.formData[field] = 100;
      }
    },

    async saveChanges() {
      try {
        await this.$refs.form.validate();
        
        this.isSaving = true;
        const res = await axios.post('http://projectmanagement.jenasi.ai:99/project/update', this.formData);
        
        if (res.data.code === "200") {
          this.$message.success('保存成功');
          this.$router.push('/home/<USER>/list');
        } else {
          this.$message.error(res.data.message || '保存失败');
        }
      } catch (error) {
        if (error === false) {
          this.$message.warning('请检查表单填写是否正确');
        } else {
          console.error('保存失败:', error);
          this.$message.error('保存失败，请重试');
        }
      } finally {
        this.isSaving = false;
      }
    },

    retryLoad() {
      this.loadData();
    }
  }
}
</script>

<style scoped>
.project-update-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.project-update-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
  /* 新增样式 */
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  /* 新增样式 */
  position: sticky;
  top: 0;
  z-index: 10;
}

.project-form {
  max-width: 800px;
  margin: 0 auto;
  /* 新增样式 */
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
  width: 100%;
}

/* 自定义滚动条样式 */
.project-form::-webkit-scrollbar {
  width: 8px;
}

.project-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.project-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.project-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}


.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.page-header h2 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 24px;
}

.project-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-left: 4px solid #1890ff;
  padding-left: 10px;
  line-height: 1;
}

.readonly-value {
  color: #606266;
  line-height: 32px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.loading-state {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.error-state {
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .project-update-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .project-form {
    padding: 0 10px;
  }
}
</style> 