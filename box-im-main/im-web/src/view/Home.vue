<template>
  <div class="home-page">
    <div class="app-container" :class="{ fullscreen: isFullscreen }">
      <div class="navi-bar">
        <div class="navi-bar-box">
          <div class="top">
            <div class="user-head-image">
              <head-image :name="$store.state.userStore.userInfo.nickName" :size="38"
                          :url="$store.state.userStore.userInfo.headImageThumb"
                          @click.native="showSettingDialog = true">
              </head-image>
            </div>
            <div class="menu">
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <span class="icon iconfont icon-chat"></span>
                  <span class="menu-item-text">聊天</span>
                  <div v-show="unreadCount > 0" class="unread-text">{{ unreadCount }}</div>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <span class="icon iconfont icon-friend"></span>
                  <span class="menu-item-text">好友</span>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <span class="icon iconfont icon-group" style="font-size: 28px"></span>
                  <span class="menu-item-text">群聊</span>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <i class="el-icon-truck" style="font-size: 28px"></i>
                  <span class="menu-item-text">订单物流</span>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <i class="el-icon-link" style="font-size: 28px"></i>
                  <span class="menu-item-text">PLC编程</span>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>'">
                <div class="menu-item">
                  <i class="el-icon-collection" style="font-size: 28px"></i>
                  <span class="menu-item-text">工作日志</span>
                </div>
              </router-link>
              <router-link class="link" v-bind:to="'/home/<USER>/list'">
                <div class="menu-item">
                  <i class="el-icon-star-on" style="font-size: 28px"></i>
                  <span class="menu-item-text">项目管理</span>
                </div>
              </router-link>
              <div class="link" @click="goToProblem">
                <div class="menu-item">
                  <i class="el-icon-question" style="font-size: 28px"></i>
                  <span class="menu-item-text">产品生命周期管理</span>
                </div>
              </div>
              <div class="link" @click="goToElectrical">
                <div class="menu-item">
                  <i class="el-icon-cpu" style="font-size: 28px"></i>
                  <span class="menu-item-text">电气编程</span>
                </div>
              </div>
            </div>
          </div>

          <div class="botoom">
            <div class="botoom-item" @click="isFullscreen = !isFullscreen">
              <i class="el-icon-full-screen"></i>
              <span class="botoom-item-text">缩放</span>
            </div>
            <div class="botoom-item" @click="showSetting">
              <span class="icon iconfont icon-setting" style="font-size: 20px"></span>
              <span class="botoom-item-text">设置</span>
            </div>
            <div class="botoom-item" @click="takeScreenshot()">
              <i class="el-icon-camera-solid"></i>
              <span class="botoom-item-text">截图</span>
            </div>
            <div class="botoom-item" @click="clearLocalData">
              <i class="el-icon-delete"></i>
              <span class="botoom-item-text">清除本地数据</span>
            </div>
            <div class="botoom-item" @click="onExit()">
              <span class="icon iconfont icon-exit"></span>
              <span class="botoom-item-text">退出</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content-box">
        <router-view></router-view>
      </div>
      <setting :visible="showSettingDialog" @close="closeSetting()"></setting>
      <user-info v-show="uiStore.userInfo.show" :pos="uiStore.userInfo.pos" :user="uiStore.userInfo.user"
                 @close="$store.commit('closeUserInfoBox')"></user-info>
      <full-image :visible="uiStore.fullImage.show" :url="uiStore.fullImage.url"
                  @close="$store.commit('closeFullImageBox')"></full-image>
      <rtc-private-video ref="rtcPrivateVideo"></rtc-private-video>
      <rtc-group-video ref="rtcGroupVideo"></rtc-group-video>
    </div>

    <!-- 截图选择区域覆盖层 -->
    <div v-if="isScreenshotMode"
         :class="[
				'screenshot-overlay', 
				hasSelection ? 'has-selection' : '', 
				!selectionConfirmed && hasSelection ? 'selecting-mode' : ''
			]"
         @mousedown="startSelection"
         @mousemove="updateSelection"
         @mouseup="endSelection"
         @contextmenu.prevent="confirmSelection">
      <div class="selection-area" :style="selectionStyle"></div>
      <div class="screenshot-toolbar" v-if="hasSelection && selectionConfirmed" :style="toolbarStyle">
        <button @click.stop="captureSelectedArea">保存截图</button>
        <button @click.stop="cancelScreenshot">取消</button>
      </div>
      <div class="screenshot-hint">
        按住鼠标左键拖动选择截图区域，右击确认选区，按ESC退出
      </div>
    </div>
  </div>
</template>

<script>
import HeadImage from '../components/common/HeadImage.vue';
import Setting from '../components/setting/Setting.vue';
import UserInfo from '../components/common/UserInfo.vue';
import FullImage from '../components/common/FullImage.vue';
import RtcPrivateVideo from '../components/rtc/RtcPrivateVideo.vue';
import RtcPrivateAcceptor from '../components/rtc/RtcPrivateAcceptor.vue';
import RtcGroupVideo from '../components/rtc/RtcGroupVideo.vue';
import html2canvas from 'html2canvas';
import localforage from "localforage";

export default {
  components: {
    HeadImage,
    Setting,
    UserInfo,
    FullImage,
    RtcPrivateVideo,
    RtcPrivateAcceptor,
    RtcGroupVideo
  },
  data() {
    return {
      showSettingDialog: false,
      lastPlayAudioTime: new Date().getTime() - 1000,
      isFullscreen: true,
      isScreenshotMode: false,
      selectionStart: null,
      selectionEnd: null,
      hasSelection: false,
      selectionConfirmed: false
    }
  },
  methods: {
    init() {
      this.$eventBus.$on('openPrivateVideo', (rctInfo) => {
        // 进入单人视频通话
        this.$refs.rtcPrivateVideo.open(rctInfo);
      });
      this.$eventBus.$on('openGroupVideo', (rctInfo) => {
        // 进入多人视频通话
        this.$refs.rtcGroupVideo.open(rctInfo);
      });
      this.$store.dispatch("load").then(() => {
        // ws初始化
        this.$wsApi.connect(process.env.VUE_APP_WS_URL, sessionStorage.getItem("accessToken"));
        this.$wsApi.onConnect(() => {
          // 加载离线消息
          this.pullPrivateOfflineMessage(this.$store.state.chatStore.privateMsgMaxId);
          this.pullGroupOfflineMessage(this.$store.state.chatStore.groupMsgMaxId);
          this.pullSystemOfflineMessage(this.$store.state.chatStore.systemMsgMaxSeqNo);
        });
        this.$wsApi.onMessage((cmd, msgInfo) => {
          if (cmd == 2) {
            // 关闭ws
            this.$wsApi.close(3000)
            // 异地登录，强制下线
            this.$alert("您已在其他地方登录，将被强制下线", "强制下线通知", {
              confirmButtonText: '确定',
              callback: action => {
                location.href = "/";
              }
            });
          } else if (cmd == 3) {
            // 插入私聊消息
            this.handlePrivateMessage(msgInfo);
          } else if (cmd == 4) {
            // 插入群聊消息
            this.handleGroupMessage(msgInfo);
          } else if (cmd == 5) {
            // 处理系统消息
            this.handleSystemMessage(msgInfo);
          }
        });
        this.$wsApi.onClose((e) => {
          console.log(e);
          if (e.code != 3000) {
            // 断线重连
            this.$message.error("连接断开，正在尝试重新连接...");
            this.$wsApi.reconnect(process.env.VUE_APP_WS_URL, sessionStorage.getItem(
                "accessToken"));
          }
        });
      }).catch((e) => {
        console.log("初始化失败", e);
      })
    },
    pullPrivateOfflineMessage(minId) {
      this.$store.commit("loadingPrivateMsg", true)
      this.$http({
        url: "/message/private/pullOfflineMessage?minId=" + minId,
        method: 'GET'
      }).catch(() => {
        this.$store.commit("loadingPrivateMsg", false)
      })
    },
    pullGroupOfflineMessage(minId) {
      this.$store.commit("loadingGroupMsg", true)
      this.$http({
        url: "/message/group/pullOfflineMessage?minId=" + minId,
        method: 'GET'
      }).catch(() => {
        this.$store.commit("loadingGroupMsg", false)
      })
    },
    pullSystemOfflineMessage(minSeqNo) {
      this.$store.commit("loadingSystemMsg", true)
      this.$http({
        url: "/message/system/pullOfflineMessage?minSeqNo=" + minSeqNo,
        method: 'GET'
      }).catch(() => {
        this.$store.commit("loadingSystemMsg", false)
      })
    },
    handlePrivateMessage(msg) {
      // 消息加载标志
      if (msg.type == this.$enums.MESSAGE_TYPE.LOADING) {
        this.$store.commit("loadingPrivateMsg", JSON.parse(msg.content))
        return;
      }
      // 消息已读处理，清空已读数量
      if (msg.type == this.$enums.MESSAGE_TYPE.READED) {
        this.$store.commit("resetUnreadCount", {
          type: 'PRIVATE',
          targetId: msg.recvId
        })
        return;
      }
      // 消息回执处理,改消息状态为已读
      if (msg.type == this.$enums.MESSAGE_TYPE.RECEIPT) {
        this.$store.commit("readedMessage", {
          friendId: msg.sendId
        })
        return;
      }
      // 标记这条消息是不是自己发的
      msg.selfSend = msg.sendId == this.$store.state.userStore.userInfo.id;
      // 单人webrtc 信令
      if (this.$msgType.isRtcPrivate(msg.type)) {
        this.$refs.rtcPrivateVideo.onRTCMessage(msg)
        return;
      }
      // 好友id
      let friendId = msg.selfSend ? msg.recvId : msg.sendId;
      this.loadFriendInfo(friendId).then((friend) => {
        this.insertPrivateMessage(friend, msg);
      })
    },
    insertPrivateMessage(friend, msg) {
      let chatInfo = {
        type: 'PRIVATE',
        targetId: friend.id,
        showName: friend.showNickName,
        headImage: friend.headImage
      };
      // 打开会话
      this.$store.commit("openChat", chatInfo);
      // 插入消息
      this.$store.commit("insertMessage", [msg, chatInfo]);
      // 播放提示音
      if (!msg.selfSend && this.$msgType.isNormal(msg.type) &&
          msg.status != this.$enums.MESSAGE_STATUS.READED) {
        this.playAudioTip();
      }
    },
    handleGroupMessage(msg) {
      // 消息加载标志
      if (msg.type == this.$enums.MESSAGE_TYPE.LOADING) {
        this.$store.commit("loadingGroupMsg", JSON.parse(msg.content))
        return;
      }
      // 消息已读处理
      if (msg.type == this.$enums.MESSAGE_TYPE.READED) {
        // 我已读对方的消息，清空已读数量
        let chatInfo = {
          type: 'GROUP',
          targetId: msg.groupId
        }
        this.$store.commit("resetUnreadCount", chatInfo)
        return;
      }
      // 消息回执处理
      if (msg.type == this.$enums.MESSAGE_TYPE.RECEIPT) {
        let chatInfo = {
          type: 'GROUP',
          targetId: msg.groupId
        }
        // 更新消息已读人数
        let msgInfo = {
          id: msg.id,
          groupId: msg.groupId,
          readedCount: msg.readedCount,
          receiptOk: msg.receiptOk
        };
        this.$store.commit("updateMessage", [msgInfo, chatInfo])
        return;
      }
      // 标记这条消息是不是自己发的
      msg.selfSend = msg.sendId == this.$store.state.userStore.userInfo.id;
      // 群视频信令
      if (this.$msgType.isRtcGroup(msg.type)) {
        this.$nextTick(() => {
          this.$refs.rtcGroupVideo.onRTCMessage(msg);
        })
        return;
      }
      this.loadGroupInfo(msg.groupId).then((group) => {
        // 插入群聊消息
        this.insertGroupMessage(group, msg);
      })
    },
    insertGroupMessage(group, msg) {
      let chatInfo = {
        type: 'GROUP',
        targetId: group.id,
        showName: group.showGroupName,
        headImage: group.headImageThumb
      };
      // 打开会话
      this.$store.commit("openChat", chatInfo);
      // 插入消息
      this.$store.commit("insertMessage", [msg, chatInfo]);
      // 播放提示音
      if (!msg.selfSend && msg.type <= this.$enums.MESSAGE_TYPE.VIDEO &&
          msg.status != this.$enums.MESSAGE_STATUS.READED) {
        this.playAudioTip();
      }
    },
    handleSystemMessage(msg) {
      // 消息加载标志
      if (msg.type == this.$enums.MESSAGE_TYPE.LOADING) {
        this.$store.commit("loadingSystemMsg", JSON.parse(msg.content))
        return;
      }
      // 消息已读处理，清空已读数量
      if (msg.type == this.$enums.MESSAGE_TYPE.READED) {
        this.$store.commit("resetUnreadCount", {
          type: 'SYSTEM',
          targetId: 0
        })
        return;
      }
      // 用户被封禁
      if (msg.type == this.$enums.MESSAGE_TYPE.USER_BANNED) {
        this.$wsApi.close(3000);
        this.$alert("您的账号已被管理员封禁,原因:" + msg.content, "账号被封禁", {
          confirmButtonText: '确定',
          callback: action => {
            this.onExit();
          }
        });
        return;
      }
      // 用户账户注销
      if (msg.type == this.$enums.MESSAGE_TYPE.USER_UNREG) {
        this.$wsApi.close(3000);
        this.$alert("您的账号已注销", {
          confirmButtonText: '确定',
          callback: action => {
            this.onExit();
          }
        });
        return;
      }
      // 插入消息
      this.insertSystemMessage(msg);
    },
    insertSystemMessage(msg) {
      let headImage = require('@/assets/image/chat_system.png')
      let chatInfo = {
        type: 'SYSTEM',
        targetId: 0,
        showName: "系统通知",
        headImage: headImage
      };
      // 打开会话
      this.$store.commit("openChat", chatInfo);
      // 插入消息
      this.$store.commit("insertMessage", [msg, chatInfo]);
    },
    onExit() {
      this.$wsApi.close(3000);
      sessionStorage.removeItem("accessToken");
      location.href = "/";
    },
    playAudioTip() {
      // 离线消息不播放铃声
      if (this.$store.getters.isLoading()) {
        return;
      }
      // 防止过于密集播放
      if (new Date().getTime() - this.lastPlayAudioTime > 1000) {
        this.lastPlayAudioTime = new Date().getTime();
        let audio = new Audio();
        let url = require(`@/assets/audio/tip.wav`);
        audio.src = url;
        audio.play();
      }

    },
    showSetting() {
      this.showSettingDialog = true;
    },
    closeSetting() {
      this.showSettingDialog = false;
    },
    loadFriendInfo(id) {
      return new Promise((resolve, reject) => {
        let friend = this.$store.state.friendStore.friends.find((f) => f.id == id);
        if (friend) {
          resolve(friend);
        } else {
          this.$http({
            url: `/friend/find/${id}`,
            method: 'get'
          }).then((friend) => {
            this.$store.commit("addFriend", friend);
            resolve(friend)
          })
        }
      });
    },
    loadGroupInfo(id) {
      return new Promise((resolve, reject) => {
        let group = this.$store.state.groupStore.groups.find((g) => g.id == id);
        if (group) {
          resolve(group);
        } else {
          this.$http({
            url: `/group/find/${id}`,
            method: 'get'
          }).then((group) => {
            resolve(group)
            this.$store.commit("addGroup", group);
          })
        }
      });
    },
    // 截图功能
    takeScreenshot() {
      this.isScreenshotMode = true;
      this.selectionStart = null;
      this.selectionEnd = null;
      this.hasSelection = false;
      this.selectionConfirmed = false;
    },
    startSelection(event) {
      // 只有在未确认选区时才能开始新的选择
      if (this.selectionConfirmed) return;

      // 只响应左键
      if (event.button !== 0) return;

      this.selectionStart = {x: event.clientX, y: event.clientY};
      this.selectionEnd = {x: event.clientX, y: event.clientY};
      this.hasSelection = true;
    },
    updateSelection(event) {
      // 只有在未确认选区时才能更新选择
      if (!this.selectionStart || this.selectionConfirmed) return;

      this.selectionEnd = {x: event.clientX, y: event.clientY};
    },
    endSelection(event) {
      // 如果没有开始选择，或者选区已经确认，不处理
      if (!this.selectionStart || this.selectionConfirmed) return;

      const width = Math.abs(this.selectionStart.x - this.selectionEnd.x);
      const height = Math.abs(this.selectionStart.y - this.selectionEnd.y);

      if (width < 10 || height < 10) {
        // 选择区域太小，取消选择
        this.cancelScreenshot();
        return;
      }
    },
    confirmSelection() {
      // 如果没有选区或已经确认，不处理
      if (!this.hasSelection) return;

      // 确认选区
      this.selectionConfirmed = true;

      // 更改鼠标样式
      const overlay = document.querySelector('.screenshot-overlay');
      if (overlay) {
        overlay.style.cursor = 'default';
      }
    },
    captureSelectedArea() {
      const appContainer = document.querySelector('.app-container');
      if (!appContainer) {
        this.$message.error('无法找到截图区域');
        return;
      }

      const selection = {
        left: Math.min(this.selectionStart.x, this.selectionEnd.x),
        top: Math.min(this.selectionStart.y, this.selectionEnd.y),
        width: Math.abs(this.selectionStart.x - this.selectionEnd.x),
        height: Math.abs(this.selectionStart.y - this.selectionEnd.y)
      };

      this.$message({
        message: '正在截图...',
        type: 'info',
        duration: 1000
      });

      // 在截图前临时隐藏边框
      const selectionArea = document.querySelector('.selection-area');
      const originalBorder = selectionArea.style.border;
      selectionArea.style.border = 'none';

      // 使用html2canvas对整个屏幕截图
      html2canvas(document.body, {
        useCORS: true,
        allowTaint: true,
        backgroundColor: null
      }).then(canvas => {
        // 截图完成后，如果需要可以恢复边框
        // selectionArea.style.border = originalBorder;

        // 从整个截图中裁剪出选择的区域
        const cropCanvas = document.createElement('canvas');
        cropCanvas.width = selection.width;
        cropCanvas.height = selection.height;
        const ctx = cropCanvas.getContext('2d');

        // 裁剪选定区域
        ctx.drawImage(
            canvas,
            selection.left,
            selection.top,
            selection.width,
            selection.height,
            0, 0,
            selection.width,
            selection.height
        );

        // 获取图片URL
        const imageUrl = cropCanvas.toDataURL('image/png');

        // 将截图数据保存到localStorage，供粘贴时使用
        try {
          localStorage.setItem('screenshot', imageUrl);
          this.$message({
            message: '截图已完成，请在聊天框中按Ctrl+V粘贴',
            type: 'success',
            duration: 3000
          });
        } catch (e) {
          console.error('保存截图到localStorage失败', e);
          // 如果存储失败，回退到下载图片
          this.downloadScreenshot(imageUrl);
        }

        // 恢复正常模式
        this.isScreenshotMode = false;

      }).catch(err => {
        // 发生错误时恢复边框
        // selectionArea.style.border = originalBorder;

        console.error('截图失败:', err);
        this.$message.error('截图失败，请重试');
        this.isScreenshotMode = false;
      });
    },
    // 下载截图
    downloadScreenshot(imageUrl) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `截图_${new Date().getTime()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$message({
        message: '截图已保存到下载文件夹',
        type: 'success',
        duration: 3000
      });
    },
    cancelScreenshot() {
      this.isScreenshotMode = false;
      this.selectionStart = null;
      this.selectionEnd = null;
      this.hasSelection = false;
      this.selectionConfirmed = false;
    },
    // 处理键盘按下事件
    handleKeyDown(event) {
      // F1键的keyCode是112
      if (event.keyCode === 112) {
        // 阻止F1默认行为（通常是打开帮助）
        event.preventDefault();
        // 触发截图
        this.takeScreenshot();
      }

      // ESC键的keyCode是27
      if (event.keyCode === 27 && this.isScreenshotMode) {
        // 如果当前处于截图模式，按ESC键退出
        this.cancelScreenshot();
      }
    },
    clearLocalData() {
      this.$confirm('此操作将清除所有本地数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
            // 清空 localforage 存储的所有键值对，实际是在 indexedDB 中删除数据
            localforage.clear().then(function () {
              console.log('✅ 已清除 indexedDB 下 localforage 存储的所有键值对');
              // 刷新页面
              window.location.reload();
            }).catch(function (err) {
              console.error('❌ 清除 localforage 失败', err);
            });
          }
      ).catch(() => {
        this.$message.info('已取消清除');
      });
    },
    goToProblem() {
      const accessToken = sessionStorage.getItem('accessToken');
      const currentPath = this.$route.path;
      const currentQuery = this.$route.query;

      if (
        currentPath === '/home/<USER>' &&
        currentQuery.accessToken === accessToken
      ) {
        console.log('当前已在 /home/<USER>');
        return; // 已经在该地址，避免重复跳转
      }

      this.$router.push({
        path: '/home/<USER>',
        query: { accessToken }
      });
    },
    goToElectrical() {
      const currentPath = this.$route.path;
      const currentQuery = this.$route.query;
      const userName = this.$store.state.userStore.userInfo.nickName;

      if (
        currentPath === '/electrical' &&
        currentQuery.userName === userName
      ) {
        console.log('当前已在/electrical，跳转取消');
        return; // 已经在该地址，避免重复跳转
      }

      this.$router.push({
        path: '/electrical',
        query: { userName }
      });
    },
  },
  computed: {
    uiStore() {
      return this.$store.state.uiStore;
    }
    ,
    unreadCount() {
      let unreadCount = 0;
      let chats = this.$store.state.chatStore.chats;
      chats.forEach((chat) => {
        if (!chat.delete) {
          unreadCount += chat.unreadCount
        }
      });
      return unreadCount;
    }
    ,
    selectionStyle() {
      if (this.selectionStart && this.selectionEnd) {
        const left = Math.min(this.selectionStart.x, this.selectionEnd.x);
        const top = Math.min(this.selectionStart.y, this.selectionEnd.y);
        const width = Math.abs(this.selectionStart.x - this.selectionEnd.x);
        const height = Math.abs(this.selectionStart.y - this.selectionEnd.y);
        return {
          left: `${left}px`,
          top: `${top}px`,
          width: `${width}px`,
          height: `${height}px`
        };
      }
      return {};
    }
    ,
    toolbarStyle() {
      if (this.selectionStart && this.selectionEnd) {
        const left = Math.min(this.selectionStart.x, this.selectionEnd.x);
        const top = Math.min(this.selectionStart.y, this.selectionEnd.y);
        const width = Math.abs(this.selectionStart.x - this.selectionEnd.x);
        const height = Math.abs(this.selectionStart.y - this.selectionEnd.y);

        return {
          left: `${left + width / 2}px`,
          top: `${top + height + 10}px`,
          transform: 'translateX(-50%)'
        };
      }
      return {};
    }
  }
  ,
  watch: {
    unreadCount: {
      handler(newCount, oldCount) {
        let tip = newCount > 0 ? `${newCount}条未读` : "";
        this.$elm.setTitleTip(tip);
      }
      ,
      immediate: true
    }
  }
  ,
  mounted() {
    this.$wsApi.close();
    this.init();

    // 添加按键监听，F1键触发截图
    document.addEventListener('keydown', this.handleKeyDown);
  }
  ,
  unmounted() {
    this.$wsApi.close();
    // 移除按键监听
    document.removeEventListener('keydown', this.handleKeyDown);
  }
}
</script>

<style scoped lang="scss">
.home-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
  background: var(--im-color-primary-light-9);

  .app-container {
    width: 62vw;
    height: 80vh;
    display: flex;
    min-height: 600px;
    min-width: 970px;
    position: absolute;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: var(--im-box-shadow-dark);
    transition: 0.2s;

    &.fullscreen {
      transition: 0.2s;
      width: 100vw;
      height: 100vh;
    }
  }

  .navi-bar {
    --icon-font-size: 22px;
    --width: 60px;
    width: var(--width);
    background: var(--im-color-primary-light-1);
    padding-top: 20px;

    .navi-bar-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .botoom {
        margin-bottom: 10px;
      }
    }

    .user-head-image {
      display: flex;
      justify-content: center;
    }

    .menu {
      height: auto;
      // margin-top: 70px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-content: center;

      .link {
        text-decoration: none;
      }

      .router-link-active .menu-item {
        color: #fff;
        background: var(--im-color-primary-light-2);
      }

      .link:not(.router-link-active) .menu-item:hover {
        color: var(--im-color-primary-light-7);
      }

      .menu-item {
        position: relative;
        color: var(--im-color-primary-light-4);
        width: var(--width);
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 12px;
        padding: 5px 0;

        .icon {
          font-size: var(--icon-font-size)
        }

        .menu-item-text {
          font-size: 12px;
          margin-top: 5px;
        }

        .unread-text {
          position: absolute;
          background-color: var(--im-color-danger);
          right: 8px;
          top: 5px;
          color: white;
          border-radius: 30px;
          padding: 0 5px;
          font-size: var(--im-font-size-smaller);
          text-align: center;
          white-space: nowrap;
          border: 1px solid #f1e5e5;
        }
      }
    }

    .botoom-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 60px;
      width: 100%;
      cursor: pointer;
      color: var(--im-color-primary-light-4);
      font-size: var(--icon-font-size);
      padding: 5px 0;

      .icon {
        font-size: var(--icon-font-size)
      }

      .botoom-item-text {
        font-size: 12px;
        margin-top: 5px;
      }

      &:hover {
        font-weight: 600;
        color: var(--im-color-primary-light-7);
      }
    }
  }

  .content-box {
    flex: 1;
    padding: 0;
    background-color: #fff;
    text-align: center;
  }

  .screenshot-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    cursor: crosshair;
    z-index: 9999;

    &.has-selection {
      &.selecting-mode {
        cursor: crosshair;
      }

      &:not(.selecting-mode) {
        cursor: default;
      }
    }

    .selection-area {
      position: absolute;
      background: none;
      border: 2px dashed #1890ff;
      outline: none;
      box-shadow: none;
      pointer-events: none;
    }

    .screenshot-toolbar {
      position: absolute;
      background: white;
      padding: 8px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      pointer-events: auto;
      cursor: default;

      button {
        margin-left: 5px;
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        background: var(--im-color-primary);
        color: white;
        cursor: pointer !important;
        font-size: 14px;

        &:hover {
          background: var(--im-color-primary-light-1);
        }

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .screenshot-hint {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.75);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      pointer-events: none;
    }
  }
}
</style>
