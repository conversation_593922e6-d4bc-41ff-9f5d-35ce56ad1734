<template>
  <div class="webview-container">
    <iframe :src="url" class="iframe-content" frameborder="0"></iframe>
  </div>
</template>

<script>
export default {
  name: 'Electrical',
  data() {
    return {
      url: ''
    }
  },
  created() {
    const accessToken = sessionStorage.getItem('accessToken');
    let baseUrl = 'http://************:7860/';
    if (accessToken) {
      if (baseUrl.indexOf('?') > -1) {
        baseUrl += `&accessToken=${encodeURIComponent(accessToken)}`;
      } else {
        baseUrl += `?accessToken=${encodeURIComponent(accessToken)}`;
      }
    }
    this.url = baseUrl;
  }

}
</script>

<style scoped lang="scss">
.webview-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  
  .iframe-content {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style> 