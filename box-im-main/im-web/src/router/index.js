import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../view/Login'
import Register from '../view/Register'
import Home from '../view/Home'
import ProjectList from '../view/ProjectList'
import ProjectDetail from '../view/ProjectDetail'
import Problem from '../view/Problem'
import Electrical from '@/view/jenasi/Electrical.vue'

Vue.use(VueRouter);

export default new VueRouter({
  routes: [
    {
      path: "/",
      redirect: "/login"
    },
    {
      name: "Login",
      path: '/login',
      component: Login
    },
    {
      name: "Register",
      path: '/register',
      component: Register
    },
    {
      name: "Home",
      path: '/home',
      component: Home,
      redirect: '/home/<USER>/list',
      children: [
        {
          name: "Cha<PERSON>",
          path: "/home/<USER>",
          component: () => import("../view/Chat"),
        },
        {
          name: "Friend",
          path: "/home/<USER>",
          component: () => import("../view/Friend"),
        },
        {
          name: "GROUP",
          path: "/home/<USER>",
          component: () => import("../view/Group"),
        },
        {
          name: "WebView",
          path: "/home/<USER>",
          component: () => import("../view/WebView"),
        },
        {
          name: "Wuliu",
          path: "/home/<USER>",
          component: () => import("../view/wuliu"),
        },
        {
          name: "KnowledgeManager",
          path: "/home/<USER>",
          component: () => import("../view/KnowledgeManager"),
        },
        {
          name: "NewPage",
          path: "/home/<USER>",
          component: () => import("../view/NewPage"),
        },
        {
          name: "ProjectList",
          path: "/home/<USER>/list",
          component: ProjectList
        },
        {
          name: "ProjectDetail",
          path: "/home/<USER>/detail/:id",
          component: ProjectDetail
        },
        {
          name: "ProjectUpdate",
          path: "/home/<USER>/update/:id",
          component: () => import('@/view/project/ProjectUpdate.vue')
        },
        {
          name: "PlanAdd",
          path: "/home/<USER>/task/add/:id",
          component: () => import("../view/plan/PlanAdd"),
        },
        {
          path: '/home/<USER>/task/detail/:id',
          name: 'PlanDetail',
          component: () => import('@/view/plan/PlanDetail.vue')
        },
        {
          path: '/home/<USER>/task/update/:id',
          name: 'PlanUpdate',
          component: () => import('@/view/plan/PlanUpdate.vue')
        },
        {
          path: '/home/<USER>/list',
          name: 'PlanList',
          component: () => import('@/view/plan/PlanList.vue')
        },
        {
          path: '/home/<USER>/employee',
          name: 'EmployeePlanList',
          component: () => import('@/view/plan/EmployeePlanList.vue')
        },
        {
          name: "Problem",
          path: "/home/<USER>",
          component: Problem
        },
        {
          name: "WorkLog",
          path: "/home/<USER>",
          component: () => import("../view/KnowledgeManager"),
        },
        {
          path: '/electrical',
          name: 'Electrical',
          component: Electrical,
          meta: {
            title: '电气设备'
          }
        }
      ]
    }
  ]
});