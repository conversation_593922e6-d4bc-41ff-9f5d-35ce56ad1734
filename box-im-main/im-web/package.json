{"name": "boxim", "version": "3.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"ansi-regex": "^6.1.0", "axios": "1.7.7", "core-js": "3.38.1", "element-ui": "2.15.14", "html2canvas": "^1.4.1", "js-audio-recorder": "1.0.7", "js-base64": "3.7.7", "localforage": "1.10.0", "minio": "^8.0.5", "sass": "1.32.12", "sass-loader": "10.1.1", "vue": "2.7.16", "vue-axios": "3.5.2", "vue-router": "3.6.5", "vuex": "3.6.2", "vuex-persist": "3.1.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.12", "@vue/cli-plugin-eslint": "~4.5.12", "@vue/cli-service": "~4.5.12", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-mixed-spaces-and-tabs": 0, "generator-star-spacing": "off", "no-tabs": "off", "no-unused-vars": "off", "no-unused-labels": "off", "no-console": "off", "vue/no-unused-components": "off", "no-irregular-whitespace": "off", "no-debugger": "off", "no-useless-escape": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}