{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/components/LocalVideo.vue?0410", "webpack:///./src/components/HeadImage.vue?f8d6", "webpack:///./src/App.vue", "webpack:///./src/view/ChatVideo.vue", "webpack:///./src/common/request.js", "webpack:///./src/common/webrtc.js", "webpack:///./src/common/permission.js", "webpack:///./src/common/camera.js", "webpack:///./src/common/api.js", "webpack:///./src/common/uniEvent.js", "webpack:///./src/components/HeadImage.vue", "webpack:///src/components/HeadImage.vue", "webpack:///./src/components/HeadImage.vue?7a04", "webpack:///./src/components/HeadImage.vue?73d2", "webpack:///./src/components/LocalVideo.vue", "webpack:///src/components/LocalVideo.vue", "webpack:///./src/components/LocalVideo.vue?7532", "webpack:///./src/components/LocalVideo.vue?c1b8", "webpack:///./src/components/RemoteVideo.vue", "webpack:///src/components/RemoteVideo.vue", "webpack:///./src/components/RemoteVideo.vue?ec91", "webpack:///./src/components/RemoteVideo.vue?4fac", "webpack:///./src/components/InviteMember.vue", "webpack:///src/components/InviteMember.vue", "webpack:///./src/components/InviteMember.vue?6c11", "webpack:///./src/components/InviteMember.vue?f267", "webpack:///src/view/ChatVideo.vue", "webpack:///./src/view/ChatVideo.vue?fb2b", "webpack:///./src/view/ChatVideo.vue?e517", "webpack:///src/App.vue", "webpack:///./src/App.vue?03b3", "webpack:///./src/App.vue?315a", "webpack:///./src/common/enums.js", "webpack:///./src/main.js", "webpack:///./src/App.vue?7624", "webpack:///./src/components/RemoteVideo.vue?3895", "webpack:///./src/components/InviteMember.vue?d724", "webpack:///./src/assets/audio/call.wav", "webpack:///./src/view/ChatVideo.vue?ccec"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "staticClass", "ref", "require", "directives", "rawName", "isReady", "isHost", "expression", "inviter", "headImage", "nick<PERSON><PERSON>", "_v", "_s", "_e", "_l", "userInfos", "user", "id", "inviterId", "isLeaderMode", "style", "toVideoStyle", "on", "$event", "onClickVideo", "mine", "refInFor", "toVw", "toVh", "onSwitchFacing", "groupId", "config", "maxChannel", "videoStyle", "API", "appendUser", "chatTimeString", "isMicroPhone", "onSwitchMicroPhone", "isSpeaker", "onSwitchSpeaker", "isCamera", "onSwitchCamera", "isChating", "onReject", "onAccept", "onCancel", "onQuit", "createHttpRequest", "baseUrl", "loginInfo", "plus", "XMLHttpRequest", "net", "http", "axios", "baseURL", "timeout", "withCredentials", "headers", "interceptors", "request", "use", "accessToken", "encodeURIComponent", "error", "Promise", "reject", "response", "async", "code", "console", "log", "refreshToken", "method", "url", "catch", "toast", "undefined", "message", "ImWebRtc", "constructor", "configuration", "stream", "senders", "isEnable", "RTCPeerConnection", "webkitRTCPeerConnection", "mozRTCPeerConnection", "RTCSessionDescription", "webkitRTCSessionDescription", "mozRTCSessionDescription", "RTCIceCandidate", "webkitRTCIceCandidate", "mozRTCIceCandidate", "init", "isAndroid11", "fixAndroid", "setupPeerConnection", "callback", "peerConnection", "ontrack", "e", "streams", "setStream", "for<PERSON>ach", "sender", "removeTrack", "getTracks", "track", "addTrack", "switchStream", "getSenders", "videoTrack", "getVideoTracks", "audioTrack", "getAudioTracks", "kind", "replaceTrack", "onIcecandidate", "onicecandidate", "event", "candidate", "onStateChange", "oniceconnectionstatechange", "state", "target", "iceConnectionState", "createOffer", "resolve", "offerParam", "then", "offer", "setLocalDescription", "createAnswer", "setRemoteDescription", "answer", "addIceCandidate", "close", "uid", "onaddstream", "deviceInfo", "navigator", "userAgent", "androidVersion", "match", "iceCandidatePoolSize", "peer", "offerToReceiveAudio", "offerToReceiveVideo", "setTimeout", "requestPermissions", "scope", "android", "resultObj", "topMessageView", "createTopMessage", "title", "nativeObj", "View", "width", "height", "backgroundColor", "drawText", "top", "left", "color", "size", "whiteSpace", "show", "checkAndRequest", "messageTip", "settingTip", "os", "res", "clearTimeout", "granted", "storage", "camera", "micro", "ImCamera", "mediaDevices", "getUserMedia", "openVideo", "isFacing", "permission", "facingMode", "constraints", "video", "screen", "audio", "echoCancellation", "noiseSuppression", "openAudio", "stop", "ImA<PERSON>", "findGroupMembers", "setup", "formData", "accept", "failed", "reason", "join", "invite", "userId", "quit", "cancel", "device", "heartbeat", "UniEvent", "listen", "onEvent", "JSON", "parse", "decodeURIComponent", "addEventListener", "avatarImageStyle", "avatarTextStyle", "substring", "toUpperCase", "online", "_t", "colors", "props", "type", "Number", "default", "radius", "String", "Boolean", "methods", "computed", "w", "h", "textColor", "hash", "charCodeAt", "component", "userInfo", "stopPropagation", "domProps", "components", "HeadImage", "open", "$refs", "srcObject", "document", "getElementById", "muted", "play", "setMicroPhone", "enabled", "setCamera", "$emit", "isConnected", "webrtc", "localStream", "remoteStream", "candidates", "isInit", "stringify", "reconnect", "connect", "setMute", "isMute", "pause", "onOffer", "onAnswer", "sendCandidate", "setCandidate", "onPopup", "model", "isPopup", "$$v", "onClean", "onOk", "checkedSize", "members", "checked", "showNickName", "searchText", "includes", "onClickItem", "locked", "onSwitchChecked", "$set", "Array", "isExist", "filter", "$toast", "find", "env", "uniEvent", "chatTime", "chatTimer", "heartbeatTimer", "tipTimer", "lastTipTime", "leaderId", "vw", "vh", "leader<PERSON><PERSON>", "leader<PERSON><PERSON>", "LocalVideo", "RemoteVideo", "InviteMember", "onNavBack", "isClose", "refreshVideoWH", "reLayoutVideo", "localVideo", "openStream", "refName", "<PERSON><PERSON><PERSON><PERSON>", "showToast", "onRTCMessage", "msg", "$enums", "MESSAGE_TYPE", "RTC_GROUP_SETUP", "onRTCSetup", "RTC_GROUP_ACCEPT", "onRTCAccept", "RTC_GROUP_REJECT", "onRTCReject", "RTC_GROUP_JOIN", "onRTCJoin", "RTC_GROUP_FAILED", "onRTCFailed", "RTC_GROUP_CANCEL", "onRTCCancel", "RTC_GROUP_QUIT", "onRTCQuit", "RTC_GROUP_INVITE", "onRTCInvite", "RTC_GROUP_OFFER", "onRTCOffer", "RTC_GROUP_ANSWER", "onRTCAnswer", "RTC_GROUP_CANDIDATE", "onRTCCandidate", "RTC_GROUP_DEVICE", "onRTCDevice", "callAudio", "selfSend", "remoteUserId", "sendId", "startChatTime", "removeUser", "failedInfo", "content", "userIds", "firUserId", "firUserName", "u", "tip", "$nextTick", "initUserVideo", "devInfo", "checkDevEnable", "finally", "onSetup", "onJoin", "onInviteMember", "invMember", "idx", "findIndex", "<PERSON><PERSON><PERSON><PERSON>", "iceServers", "setInterval", "startHeartBeat", "clearInterval", "Date", "getTime", "leader", "invite1", "invite2", "append<PERSON><PERSON><PERSON>", "before", "innerWidth", "innerHeight", "count", "row", "Math", "ceil", "sqrt", "col", "leader<PERSON><PERSON><PERSON>", "initEvent", "uni", "postMessage", "isWaiting", "userCount", "min", "floor", "sec", "strTime", "watch", "handler", "newCount", "oldCount", "mounted", "URL", "location", "href", "searchParams", "getEnv", "ChatVideo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "enums", "productionTip", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,wJCvJT,W,oCCAA,W,yHCAIyC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,eAAe,IAEhHG,EAAkB,GCFlBN,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,QAAQ,CAACK,IAAI,YAAYH,MAAM,CAAC,KAAO,OAAO,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,KAAK,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,IAAMI,EAAQ,aAAgCN,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIW,UAAUX,EAAIY,OAAQC,WAAW,sBAAsBP,YAAY,YAAY,CAAEN,EAAIc,QAASZ,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,aAAa,CAACE,MAAM,CAAC,IAAMJ,EAAIc,QAAQC,UAAU,KAAOf,EAAIc,QAAQE,SAAS,KAAO,MAAM,CAACd,EAAG,MAAM,CAACI,YAAY,YAAY,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGlB,EAAIc,QAAQE,gBAAgB,GAAGhB,EAAImB,KAAKjB,EAAG,MAAM,CAACI,YAAY,YAAY,CAACN,EAAIiB,GAAG,eAAef,EAAG,MAAM,CAACI,YAAY,kBAAkB,CAACN,EAAIiB,GAAG,cAAcf,EAAG,MAAM,CAACI,YAAY,aAAaN,EAAIoB,GAAIpB,EAAIqB,WAAW,SAASC,GAAM,OAAOpB,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOuC,EAAKC,IAAIvB,EAAIwB,UAAWX,WAAW,uBAAuBxB,IAAIiC,EAAKC,IAAI,CAACrB,EAAG,aAAa,CAACE,MAAM,CAAC,IAAMkB,EAAKP,UAAU,KAAOO,EAAKN,SAAS,KAAO,OAAO,MAAK,KAAKd,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAASX,EAAIY,OAAQC,WAAW,oBAAoBP,YAAY,aAAa,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIyB,aAAcZ,WAAW,kBAAkBP,YAAY,eAAeF,MAAM,CAAC,GAAK,WAAW,CAACJ,EAAIoB,GAAIpB,EAAIqB,WAAW,SAASC,GAAM,OAAOpB,EAAG,MAAM,CAACb,IAAIiC,EAAKC,GAAGG,MAAO1B,EAAI2B,aAAaL,GAAOlB,MAAM,CAAC,GAAK,QAAQkB,EAAKC,IAAIK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAI8B,aAAaR,MAAS,CAAEA,EAAKC,IAAIvB,EAAI+B,KAAKR,GAAIrB,EAAG,cAAc,CAACK,IAAI,aAAayB,UAAS,EAAK5B,MAAM,CAAC,SAAWJ,EAAI+B,KAAK,MAAQ/B,EAAIiC,KAAKX,GAAM,OAAStB,EAAIkC,KAAKZ,IAAOM,GAAG,CAAC,aAAe5B,EAAImC,kBAAkBnC,EAAImB,KAAMG,EAAKC,IAAIvB,EAAI+B,KAAKR,GAAIrB,EAAG,eAAe,CAACK,IAAI,cAAce,EAAKC,GAAGS,UAAS,EAAK5B,MAAM,CAAC,SAAWkB,EAAK,QAAUtB,EAAIoC,QAAQ,MAAQpC,EAAIiC,KAAKX,GAAM,OAAStB,EAAIkC,KAAKZ,MAAStB,EAAImB,MAAM,MAAKjB,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIqB,UAAU3E,OAAOsD,EAAIqC,OAAOC,WAAYzB,WAAW,uCAAuCa,MAAO1B,EAAIuC,WAAYnC,MAAM,CAAC,GAAK,YAAY,CAACF,EAAG,gBAAgB,CAACK,IAAI,YAAYH,MAAM,CAAC,IAAMJ,EAAIwC,IAAI,QAAUxC,EAAIoC,QAAQ,UAAYpC,EAAIqB,UAAU,WAAarB,EAAIqC,OAAOC,YAAYV,GAAG,CAAC,GAAK5B,EAAIyC,eAAe,IAAI,GAAGvC,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIyB,aAAcZ,WAAW,iBAAiBP,YAAY,gBAAgB,CAACJ,EAAG,MAAM,CAACI,YAAY,SAASF,MAAM,CAAC,GAAK,YAAYF,EAAG,MAAM,CAACI,YAAY,WAAWF,MAAM,CAAC,GAAK,aAAa,CAACF,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIqB,UAAU3E,OAAOsD,EAAIqC,OAAOC,WAAYzB,WAAW,uCAAuCa,MAAO1B,EAAIuC,WAAYnC,MAAM,CAAC,GAAK,YAAY,CAACF,EAAG,gBAAgB,CAACK,IAAI,YAAYH,MAAM,CAAC,IAAMJ,EAAIwC,IAAI,QAAUxC,EAAIoC,QAAQ,UAAYpC,EAAIqB,UAAU,WAAarB,EAAIqC,OAAOC,YAAYV,GAAG,CAAC,GAAK5B,EAAIyC,eAAe,SAASvC,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAASX,EAAIY,OAAQC,WAAW,oBAAoBN,IAAI,UAAUD,YAAY,eAAe,CAACJ,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAGlB,EAAI0C,mBAAmBxC,EAAG,MAAM,CAACI,YAAY,WAAW,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAAWX,EAAI2C,aAAc9B,WAAW,4BAA4BP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,8CAA8CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAI4C,yBAAyB1C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,aAAaf,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,UAAYX,EAAI2C,aAAc9B,WAAW,6BAA6BP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,8CAA8CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAI4C,yBAAyB1C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,aAAaf,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAAWX,EAAI6C,UAAWhC,WAAW,yBAAyBP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,2CAA2CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAI8C,sBAAsB5C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,aAAaf,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,UAAYX,EAAI6C,UAAWhC,WAAW,0BAA0BP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,2CAA2CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAI8C,sBAAsB5C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,aAAaf,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAAWX,EAAI+C,SAAUlC,WAAW,wBAAwBP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,0CAA0CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIgD,qBAAqB9C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,aAAaf,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,UAAYX,EAAI+C,SAAUlC,WAAW,yBAAyBP,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACI,YAAY,0CAA0CsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIgD,qBAAqB9C,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIiB,GAAG,iBAAiBf,EAAG,MAAM,CAACK,IAAI,SAASD,YAAY,cAAc,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIY,SAAWZ,EAAIiD,UAAWpC,WAAW,0BAA0BP,YAAY,sCAAsCsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIkD,eAAehD,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIY,SAAWZ,EAAIiD,UAAWpC,WAAW,0BAA0BP,YAAY,kCAAkCsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAImD,eAAejD,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIY,SAAWZ,EAAIiD,UAAWpC,WAAW,yBAAyBP,YAAY,sCAAsCsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIoD,eAAelD,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIiD,UAAWpC,WAAW,cAAcP,YAAY,sCAAsCsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIqD,kBAEhiMhD,EAAkB,G,gGCCtB,IAAIiD,EAAoB,SAASC,EAASC,GACrC5D,OAAO6D,OAEV7D,OAAO8D,eAAiB9D,OAAO6D,KAAKE,IAAID,gBAGzC,MAAME,EAAOC,OAAMzE,OAAO,CACzB0E,QAASP,EACTQ,QAAS,IACTC,iBAAiB,EACjBC,QAAS,CACR,eAAgB,qCAkDlB,OA3CAL,EAAKM,aAAaC,QAAQC,IAAI/B,IAC7B,IAAIgC,EAAcb,EAAUa,YAI5B,OAHIA,IACHhC,EAAO4B,QAAQI,YAAcC,mBAAmBD,IAE1ChC,GACLkC,GACKC,QAAQC,OAAOF,IAMvBX,EAAKM,aAAaQ,SAASN,IAAIO,UAC9B,GAA0B,KAAtBD,EAASxI,KAAK0I,KACjB,OAAOF,EAASxI,KAAKA,KACf,GAA0B,KAAtBwI,EAASxI,KAAK0I,KAAa,CACrCC,QAAQC,IAAI,kBACZ,IAAIC,EAAevB,EAAUuB,aAgB7B,OAdAvB,QAAkBI,EAAK,CACtBoB,OAAQ,MACRC,IAAK,gBACLhB,QAAS,CACRc,aAAcA,KAEbG,MAAM,KACRC,eAAM,aAG4B,iBAAxBT,EAASrC,OAAOnG,OAC1BwI,EAASrC,OAAO4B,aAAUmB,GAGpBxB,EAAKc,EAASrC,QAGrB,OADA8C,eAAMT,EAASxI,KAAKmJ,SACbb,QAAQC,OAAOC,EAASxI,OAE9BqI,IACFY,eAAM,kBACCX,QAAQC,OAAOF,KAEhBX,GAION,QClEf,MAAMgC,EACLC,cACCtF,KAAKuF,cAAgB,GACrBvF,KAAKwF,OAAS,KACdxF,KAAKyF,QAAU,IAIjBJ,EAAS1I,UAAU+I,SAAW,WAO7B,OANA/F,OAAOgG,kBAAoBhG,OAAOgG,mBAAqBhG,OAAOiG,yBAA2BjG,OACvFkG,qBACFlG,OAAOmG,sBAAwBnG,OAAOmG,uBAAyBnG,OAAOoG,6BAA+BpG,OACnGqG,yBACFrG,OAAOsG,gBAAkBtG,OAAOsG,iBAAmBtG,OAAOuG,uBAAyBvG,OACjFwG,qBACOxG,OAAOgG,mBAGjBN,EAAS1I,UAAUyJ,KAAO,SAASb,GAClCvF,KAAKuF,cAAgBA,EAGlBvF,KAAKqG,eACPrG,KAAKsG,cAIPjB,EAAS1I,UAAU4J,oBAAsB,SAASC,GACjDxG,KAAKyG,eAAiB,IAAId,kBAAkB3F,KAAKuF,eACjDvF,KAAKyG,eAAeC,QAAWC,IAE9BH,EAASG,EAAEC,QAAQ,MAKrBvB,EAAS1I,UAAUkK,UAAY,SAASrB,GACvCxF,KAAKyF,QAAQqB,QAASC,IACrB/G,KAAKyG,eAAeO,YAAYD,KAEjC/G,KAAKyF,QAAU,GACfD,EAAOyB,YAAYH,QAASI,IAC3B,IAAIH,EAAS/G,KAAKyG,eAAeU,SAASD,EAAO1B,GACjDxF,KAAKyF,QAAQ1I,KAAKgK,KAEnB/G,KAAKwF,OAASA,GAGfH,EAAS1I,UAAUyK,aAAe,SAAS5B,GAC1C,IAAIC,EAAUzF,KAAKyG,eAAeY,aAC9BC,EAAa9B,EAAO+B,iBAAiB,GACrCC,EAAahC,EAAOiC,iBAAiB,GACzChC,EAAQqB,QAASC,IACZA,EAAOG,OAA8B,SAArBH,EAAOG,MAAMQ,MAChCX,EAAOY,aAAaL,GAEjBP,EAAOG,OAA8B,SAArBH,EAAOG,MAAMQ,MAChCX,EAAOY,aAAaH,MAMvBnC,EAAS1I,UAAUiL,eAAiB,SAASpB,GAC5CxG,KAAKyG,eAAeoB,eAAkBC,IAEjCA,EAAMC,WACTvB,EAASsB,EAAMC,aAKlB1C,EAAS1I,UAAUqL,cAAgB,SAASxB,GAE3CxG,KAAKyG,eAAewB,2BAA8BH,IACjD,IAAII,EAAQJ,EAAMK,OAAOC,mBACzBxD,QAAQC,IAAI,gBAAkBqD,GAC9B1B,EAAS0B,KAIX7C,EAAS1I,UAAU0L,YAAc,WAChC,OAAO,IAAI9D,QAAQ,CAAC+D,EAAS9D,KAC5B,MAAM+D,EAAa,CACnBA,oBAAiC,EACjCA,oBAAiC,GAEjCvI,KAAKyG,eAAe4B,YAAYE,GAAYC,KAAMC,IAEjDzI,KAAKyG,eAAeiC,oBAAoBD,GAExCH,EAAQG,KACNxD,MAAO0B,IACTnC,EAAOmC,QAMVtB,EAAS1I,UAAUgM,aAAe,SAASF,GAC1C,OAAO,IAAIlE,QAAQ,CAAC+D,EAAS9D,KAE5BxE,KAAK4I,qBAAqBH,GAE1B,MAAMF,EAAa,CACnBA,oBAAiC,EACjCA,oBAAiC,GACjCvI,KAAKyG,eAAekC,aAAaJ,GAAYC,KAAMK,IAElD7I,KAAKyG,eAAeiC,oBAAoBG,GAExCP,EAAQO,KACN5D,MAAO0B,IACTnC,EAAOmC,QAKVtB,EAAS1I,UAAUiM,qBAAuB,SAASH,GAElDzI,KAAKyG,eAAemC,qBAAqB,IAAI9C,sBAAsB2C,KAGpEpD,EAAS1I,UAAUmM,gBAAkB,SAASf,GAE7C/H,KAAKyG,eAAeqC,gBAAgB,IAAI7C,gBAAgB8B,KAGzD1C,EAAS1I,UAAUoM,MAAQ,SAASC,GAE/BhJ,KAAKyG,iBACRzG,KAAKyG,eAAesC,QACpB/I,KAAKyG,eAAeoB,eAAiB,KACrC7H,KAAKyG,eAAewC,YAAc,OAMpC5D,EAAS1I,UAAU0J,YAAc,WAChC,GAAI1G,OAAO6D,KAAM,CAChB,MAAM0F,EAAaC,UAAUC,UACvBC,EAAiBH,EAAWI,MAAM,oBACxC,GAAID,GAA4C,IAA1BA,EAAe5M,OAEpC,OADAmI,QAAQC,IAAI,kBAAkBwE,GACJ,MAAnBA,EAAe,GAGxB,OAAO,GAGRhE,EAAS1I,UAAU2J,WAAa,WAC/B1B,QAAQC,IAAI,cACZ7E,KAAKuF,cAAcgE,qBAAuB,EAC1C,IAAIC,EAAO,IAAI7D,kBAAkB3F,KAAKuF,eACtCiE,EAAKnB,YAAY,CAChBoB,qBAAqB,EACrBC,qBAAqB,IACnBlB,KAAMC,IACRe,EAAKd,oBAAoBD,GACzBkB,WAAW,KACVH,EAAKT,QACLnE,QAAQC,IAAI,qBACV,QAGUQ,QCxKf,MAAMuE,EAAqBA,CAACC,EAAOzE,IAC3B,IAAIb,QAAQ,CAAC+D,EAAS9D,KAC5B7E,OAAO6D,KAAKsG,QAAQF,mBACnB,CAAC,sBAAwBC,IACzB,SAASE,GACRnF,QAAQC,IAAIkF,EAAW,aACvBzB,EAAQyB,MAET,SAASzF,GACRE,SAQJ,IAAIwF,EACJ,MAAMC,EAAmBA,CAACC,EAAO9E,KAChC4E,EAAiB,IAAIrK,OAAO6D,KAAK2G,UAAUC,KAAK,iBAAkB,CACjEC,MAAO,OACPC,OAAQ,OACRC,gBAAiB,qBAElBP,EAAeQ,SACdN,EAAO,CACNO,IAAK,SACLC,KAAM,MACNL,MAAO,MACPC,OAAQ,OACN,CACFK,MAAO,UACPC,KAAM,SAGRZ,EAAeQ,SACdpF,EAAS,CACRqF,IAAK,QACLC,KAAM,MACNL,MAAO,MACPC,OAAQ,OACN,CACFK,MAAO,UACPE,WAAY,WAIdb,EAAec,QAGVC,EAAkBrG,MAAOmF,EAAOK,EAAOc,EAAYC,KACxD,GAAItL,OAAO6D,MAAgC,QAAxB7D,OAAO6D,KAAK0H,GAAG7M,KAAgB,CACjD,IAAIU,EAAG4K,WAAW,IAAMM,EAAiBC,EAAOc,GAAa,KACzDG,QAAYvB,EAAmBC,EAAOoB,GAI1C,GAHAG,aAAarM,GACbiL,GAAkBA,EAAejB,QACjCnE,QAAQC,IAAI,OAAQsG,IACfA,EAAIE,QAAQ,GAEhB,OAAO,EAGT,OAAO,GAGFC,EAAU5G,UACf,MAAMmF,EAAQ,yBACRK,EAAQ,cACRc,EAAa,yBACbC,EAAa,kDACnB,OAAOF,EAAgBlB,EAAOK,EAAOc,EAAYC,IAG5CM,EAAS7G,UACd,MAAMmF,EAAQ,SACRK,EAAQ,WACRc,EAAa,kBACbC,EAAa,wCACnB,OAAOF,EAAgBlB,EAAOK,EAAOc,EAAYC,IAI5CO,EAAQ9G,UACb,MAAMmF,EAAQ,eACRK,EAAQ,YACRc,EAAa,yBACbC,EAAa,kDACnB,OAAOF,EAAgBlB,EAAOK,EAAOc,EAAYC,IAGnC,OACdK,UACAC,SACAC,SC3FD,MAAMC,EACLnG,cACCtF,KAAKwF,OAAS,MAIhBiG,EAAS9O,UAAU+I,SAAW,WAC7B,QAASyD,aAAeA,UAAUuC,gBAAkBvC,UAAUuC,aAAaC,cAG5EF,EAAS9O,UAAUiP,UAAY,SAASC,GAIvC,OAHI7L,KAAKwF,QACRxF,KAAK+I,QAEC,IAAIxE,QAAQ,CAAC+D,EAAS9D,KAC5BmF,WAAWjF,UACV,UAAWoH,EAAWP,SACrB,OAAO/G,EAAO,CACbY,QAAS,gBAGX,UAAW0G,EAAWN,QACrB,OAAOhH,EAAO,CACbY,QAAS,cAGX,IAAI2G,EAAaF,EAAW,OAAS,cACjCG,EAAc,CACjBC,MAAO,CAEN5B,MAAO1K,OAAOuM,OAAO7B,MACrBC,OAAQ3K,OAAOuM,OAAO7B,MACtB0B,WAAYA,GAMbI,MAAO,CACNC,kBAAkB,EAClBC,kBAAkB,IAGpBlD,UAAUuC,aAAaC,aAAaK,GAAaxD,KAAMhD,IACtDZ,QAAQC,IAAI,SACZ7E,KAAKwF,OAASA,EACd8C,EAAQ9C,KACNP,MAAO0B,IACTnC,EAAO,CACNY,QAAS,qBAOdqG,EAAS9O,UAAU2P,UAAY,WAI9B,OAHItM,KAAKwF,QACRxF,KAAK+I,QAEC,IAAIxE,QAAQ,CAAC+D,EAAS9D,KAC5BmF,WAAWjF,UACV,UAAWoH,EAAWN,QACrB,OAAOhH,EAAO,CACbY,QAAS,cAGX,IAAI4G,EAAc,CACjBC,OAAO,EACPE,MAAO,CACNC,kBAAkB,EAClBC,kBAAkB,IAGpBlD,UAAUuC,aAAaC,aAAaK,GAAaxD,KAAMhD,IACtDxF,KAAKwF,OAASA,EACd8C,EAAQ9C,KACNP,MAAM,KACRL,QAAQC,IAAI,aACZL,EAAO,CACNG,KAAM,EACNS,QAAS,qBAQdqG,EAAS9O,UAAUoM,MAAQ,WAEtB/I,KAAKwF,SACRxF,KAAKwF,OAAOyB,YAAYH,QAASI,IAChCA,EAAMqF,SAEPvM,KAAKwF,OAAS,OAIDiG,QCnGf,MAAMe,EACLlH,YAAYhC,EAASC,GACpBvD,KAAK2D,KAAON,EAAkBC,EAASC,IAIzCiJ,EAAM7P,UAAU8P,iBAAmB,SAAStK,GAC3C,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,kBAAoB7C,EACzB4C,OAAQ,SAIVyH,EAAM7P,UAAU+P,MAAQ,SAASvK,EAASf,GACzC,IAAIuL,EAAW,CACdxK,UACAf,aAED,OAAOpB,KAAK2D,KAAK,CAChBqB,IAAK,sBACLD,OAAQ,OACR9I,KAAM0Q,KAIRH,EAAM7P,UAAUiQ,OAAS,SAASzK,GACjC,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,gCAAgC7C,EACrC4C,OAAQ,UAIVyH,EAAM7P,UAAU6H,OAAS,SAASrC,GACjC,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,gCAAgC7C,EACrC4C,OAAQ,UAIVyH,EAAM7P,UAAUkQ,OAAS,SAAS1K,EAAQ2K,GACzC,IAAIH,EAAW,CACdxK,UACA2K,UAED,OAAO9M,KAAK2D,KAAK,CAChBqB,IAAK,uBACLD,OAAQ,OACR9I,KAAM0Q,KAKRH,EAAM7P,UAAUoQ,KAAO,SAAS5K,GAC/B,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,8BAA8B7C,EACnC4C,OAAQ,UAIVyH,EAAM7P,UAAUqQ,OAAS,SAAS7K,EAASf,GAC1C,IAAIuL,EAAW,CACdxK,UACAf,aAED,OAAOpB,KAAK2D,KAAK,CAChBqB,IAAK,uBACLD,OAAQ,OACR9I,KAAM0Q,KAKRH,EAAM7P,UAAU8L,MAAQ,SAAStG,EAAS8K,EAAQxE,GACjD,IAAIkE,EAAW,CACdxK,UACA8K,SACAxE,SAED,OAAOzI,KAAK2D,KAAK,CAChBqB,IAAK,sBACLD,OAAQ,OACR9I,KAAM0Q,KAIRH,EAAM7P,UAAUkM,OAAS,SAAS1G,EAAS8K,EAAQpE,GAClD,IAAI8D,EAAW,CACdxK,UACA8K,SACApE,UAED,OAAO7I,KAAK2D,KAAK,CAChBqB,IAAK,uBACLD,OAAQ,OACR9I,KAAM0Q,KAIRH,EAAM7P,UAAUuQ,KAAO,SAAS/K,GAC/B,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,8BAAgC7C,EACrC4C,OAAQ,UAIVyH,EAAM7P,UAAUwQ,OAAS,SAAShL,GACjC,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,gCAAkC7C,EACvC4C,OAAQ,UAIVyH,EAAM7P,UAAUoL,UAAY,SAAS5F,EAAS8K,EAAQlF,GACrD,IAAI4E,EAAW,CACdxK,UACA8K,SACAlF,aAED,OAAO/H,KAAK2D,KAAK,CAChBqB,IAAK,0BACLD,OAAQ,OACR9I,KAAM0Q,KAIRH,EAAM7P,UAAUyQ,OAAS,SAASjL,EAASW,EAAUJ,GACpD,IAAIiK,EAAW,CACdxK,UACAW,WACAJ,gBAED,OAAO1C,KAAK2D,KAAK,CAChBqB,IAAK,uBACLD,OAAQ,OACR9I,KAAM0Q,KAKRH,EAAM7P,UAAUoL,UAAY,SAAS5F,EAAS8K,EAAQlF,GACrD,IAAI4E,EAAW,CACdxK,UACA8K,SACAlF,aAED,OAAO/H,KAAK2D,KAAK,CAChBqB,IAAK,0BACLD,OAAQ,OACR9I,KAAM0Q,KAIRH,EAAM7P,UAAU0Q,UAAY,SAASlL,GACpC,OAAOnC,KAAK2D,KAAK,CAChBqB,IAAK,mCAAqC7C,EAC1C4C,OAAQ,UAKKyH,QC9Jf,MAAMc,GAENA,EAAS3Q,UAAU4Q,OAAS,SAAS/G,GAEpC7G,OAAO6N,QAAW7G,IACjB,IAAImB,EAAQ2F,KAAKC,MAAMC,mBAAmBhH,IAC1CH,EAASsB,EAAM1I,IAAI0I,EAAM7L,OAG1B0D,OAAOiO,iBAAiB,WAAW,SAAUjH,GAC5C,MAAMmB,EAAQnB,EAAE1K,KAChBuK,EAASsB,EAAM1I,IAAI0I,EAAM7L,SACxB,IAIYqR,QCpBXxN,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIiF,IAAKpE,WAAW,QAAQP,YAAY,eAAeoB,MAAO1B,EAAI8N,iBAAkB1N,MAAM,CAAC,IAAMJ,EAAIiF,IAAI,QAAU,UAAU/E,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIiF,IAAKpE,WAAW,SAASP,YAAY,cAAcoB,MAAO1B,EAAI+N,iBAAkB,CAAC/N,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAI1B,KAAK0P,UAAU,EAAE,GAAGC,eAAe,OAAQjO,EAAIkO,OAAQhO,EAAG,MAAM,CAACI,YAAY,SAASF,MAAM,CAAC,MAAQ,YAAYJ,EAAImB,KAAKnB,EAAImO,GAAG,YAAY,IAEhlB9N,EAAkB,GCUtB,GACA/B,KAAA,YACApC,OACA,OACAkS,OAAA,mDACA,qDAIAC,MAAA,CACAxD,KAAA,CACAyD,KAAAC,OACAC,QAAA,IAEAlE,MAAA,CACAgE,KAAAC,QAEAE,OAAA,CACAH,KAAAI,OACAF,QAAA,OAEAjE,OAAA,CACA+D,KAAAC,QAEAtJ,IAAA,CACAqJ,KAAAI,QAEApQ,KAAA,CACAgQ,KAAAI,OACAF,QAAA,KAEAN,OAAA,CACAI,KAAAK,QACAH,SAAA,IAGAI,QAAA,GACAC,SAAA,CACAf,mBACA,IAAAgB,EAAA,KAAAxE,MAAA,KAAAA,MAAA,KAAAO,KACAkE,EAAA,KAAAxE,OAAA,KAAAA,OAAA,KAAAM,KACA,eAAAiE,eAAAC,kCACsB9O,KAAtBwO,WAEAV,kBACA,IAAAe,EAAA,KAAAxE,MAAA,KAAAA,MAAA,KAAAO,KACAkE,EAAA,KAAAxE,OAAA,KAAAA,OAAA,KAAAM,KACA,gBAAAiE,cAAAC,yBACa9O,KAAb+O,uBAAA,GAAAF,kCACsB7O,KAAtBwO,WAEAO,YACA,IAAAC,EAAA,EACA,QAAAzS,EAAA,EAAAA,EAAA,KAAA8B,KAAA5B,OAAAF,IACAyS,GAAA,KAAA3Q,KAAA4Q,WAAA1S,GAEA,YAAA4R,OAAAa,EAAA,KAAAb,OAAA1R,WCpEkV,I,wBCQ9UyS,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBXpP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,eAAe,CAACJ,EAAG,aAAa,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIW,UAAUX,EAAIoP,SAASrM,SAAUlC,WAAW,iCAAiCP,YAAY,aAAaF,MAAM,CAAC,MAAQJ,EAAIsK,MAAM,OAAStK,EAAIuK,OAAO,IAAMvK,EAAIoP,SAASrO,UAAU,KAAOf,EAAIoP,SAASpO,SAAS,OAAS,OAAOd,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAASX,EAAIoP,SAASrM,SAAUlC,WAAW,+BAA+BP,YAAY,eAAe,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAAWX,EAAI8L,SAAUjL,WAAW,wBAAwBP,YAAY,kCAAkCsB,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwN,kBAAyBrP,EAAImC,qBAAqBjC,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,UAAYX,EAAI8L,SAAUjL,WAAW,yBAAyBP,YAAY,iCAAiCsB,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOwN,kBAAyBrP,EAAImC,uBAAuBjC,EAAG,QAAQ,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIW,SAASX,EAAIoP,SAASrM,SAAUlC,WAAW,+BAA+BN,IAAI,QAAQD,YAAY,QAAQF,MAAM,CAAC,GAAK,aAAa,6BAA6B,OAAO,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,GAAG,MAAQ,IAAIkP,SAAS,CAAC,OAAQ,MAAS,IAE33CjP,EAAkB,GCetB,GACA/B,KAAA,aACAiR,WAAA,CACAC,aAEAtT,OACA,OACAsP,OAAA,IAAAE,EACAjG,OAAA,KACA9E,SAAA,EACAmL,UAAA,IAGAuC,MAAA,CACAe,SAAA,CACAd,KAAA3R,QAEA2N,MAAA,CACAgE,KAAAC,QAEAhE,OAAA,CACA+D,KAAAC,SAGAK,QAAA,CACAa,KAAAhK,GACA,KAAAA,SAEA,KAAAiK,MAAAxD,MAAAyD,UAAAlK,EAIAmK,SAAAC,eAAA,cAAAC,OAAA,EACA,KAAAJ,MAAAxD,MAAA6D,OAAA7K,MAAA,KACAL,QAAAC,IAAA,aAEA,KAAAnE,UAAA8E,GAEAuK,cAAArK,GACA,KAAAF,OAAAyB,YAAAH,QAAAI,IACA,UAAAA,EAAAQ,OACAR,EAAA8I,QAAAtK,MAIAuK,UAAAvK,GACA,KAAAF,OAAAyB,YAAAH,QAAAI,IACA,UAAAA,EAAAQ,OACAR,EAAA8I,QAAAtK,MAIAxD,iBACA,KAAA2J,UAAA,KAAAA,SACA,KAAAqE,MAAA,oBAAArE,aCvEmV,ICQ/U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBX/L,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,IAAI,MAAMD,YAAY,gBAAgB,CAACJ,EAAG,cAAc,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIoQ,YAAavP,WAAW,iBAAiBP,YAAY,UAAUF,MAAM,CAAC,MAAQ,WAAW,KAAO,QAAQF,EAAG,aAAa,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIoQ,cAAcpQ,EAAIoP,SAASrM,SAAUlC,WAAW,qCAAqCP,YAAY,aAAaF,MAAM,CAAC,MAAQJ,EAAIsK,MAAM,OAAStK,EAAIuK,OAAO,IAAMvK,EAAIoP,SAASrO,UAAU,KAAOf,EAAIoP,SAASpO,SAAS,OAAS,OAAOd,EAAG,MAAM,CAACI,YAAY,YAAY,CAACJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIoP,SAASzM,aAAc9B,WAAW,0BAA0BP,YAAY,qCAAqCJ,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,OAAQiB,EAAIoP,SAASzM,aAAc9B,WAAW,2BAA2BP,YAAY,sCAAsCJ,EAAG,MAAM,CAACI,YAAY,QAAQ,CAACN,EAAIiB,GAAG,IAAIjB,EAAIkB,GAAGlB,EAAIoP,SAASpO,UAAU,SAASd,EAAG,QAAQ,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAIoQ,aAAapQ,EAAIoP,SAASrM,SAAUlC,WAAW,mCAAmCN,IAAI,QAAQD,YAAY,QAAQF,MAAM,CAAC,GAAK,cAAc,6BAA6B,OAAO,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,OAAO,IAE70CC,EAAkB,GCqBtB,GACA/B,KAAA,aACAiR,WAAA,CACAC,aAEAtT,OACA,OACAsG,IAAA,KACA6N,OAAA,IAAA/K,EACAgL,YAAA,KACAC,aAAA,KACAC,WAAA,GACAC,QAAA,EACAL,aAAA,EACAzP,SAAA,IAGA0N,MAAA,CACA/D,MAAA,CACAgE,KAAAC,QAEAhE,OAAA,CACA+D,KAAAC,QAEAnM,QAAA,CACAkM,KAAAI,QAEAU,SAAA,CACAd,KAAA3R,SAGAiS,QAAA,CACAvI,KAAA7D,EAAAgD,EAAA8K,GACA,KAAAG,QAAA,EACA,KAAAjO,MACA,KAAA6N,OAAAhK,KAAAb,GACA,KAAA8K,cAEA,KAAAD,OAAA7J,oBAAA+J,IAEA1L,QAAAC,IAAA,UACA,KAAAyL,eACA,KAAAb,MAAAxD,MAAAyD,UAAAY,EACA,KAAAb,MAAAxD,MAAA6D,OAAA7K,MAAA,KACAL,QAAAC,IAAA,eAIA,KAAAuL,OAAAvJ,UAAAwJ,GAEA,KAAAD,OAAAxI,eAAAG,IACA,KAAArH,QAEA,KAAA6B,IAAAwF,UAAA,KAAA5F,QAAA,KAAAgN,SAAA7N,GAAAmM,KAAAgD,UAAA1I,IAGA,KAAAwI,WAAAxT,KAAAgL,KAIA,KAAAqI,OAAApI,cAAAE,IACA,aAAAA,IAEA,KAAAiI,aAAA,MAMAO,UAAAL,GACA,KAAAA,cACA,KAAAD,OAAAvJ,UAAAwJ,GACA,KAAAM,WAEAvJ,aAAAiJ,GACA,KAAAA,cACA,KAAAD,OAAAhJ,aAAAiJ,IAEAO,QAAAC,GACA,MAAA5E,EAAA0D,SAAAC,eAAA,eACA3D,EAAA6E,QACA7E,EAAA4D,MAAAgB,EACA5E,EAAA6D,QAEAa,UACA,KAAAP,OAAA/H,cAAAG,KAAAC,IAEA,KAAAlG,IAAAkG,MAAA,KAAAtG,QAAA,KAAAgN,SAAA7N,GAAAmM,KAAAgD,UAAAhI,OAGAsI,QAAAtI,GACA,KAAA2H,OAAAzH,aAAAF,GAAAD,KAAAK,IAEA,KAAAtG,IAAAsG,OAAA,KAAA1G,QAAA,KAAAgN,SAAA7N,GAAAmM,KAAAgD,UAAA5H,MAEA,KAAAnI,SAAA,GAEAsQ,SAAAnI,GAEA,KAAAuH,OAAAxH,qBAAAC,GAEA,KAAAoI,gBACA,KAAAvQ,SAAA,GAEAwQ,aAAAnJ,GACA,KAAAqI,OAAAtH,gBAAAf,IAEAkJ,gBACA,KAAAV,WAAAzJ,QAAAiB,IACA,KAAAxF,IAAAwF,UAAA,KAAA5F,QAAA,KAAAgN,SAAA7N,GAAAmM,KAAAgD,UAAA1I,MAEA,KAAAwI,WAAA,IAEAxH,QACA,KAAAqH,OAAArH,QACA,KAAA0G,MAAAxD,MAAAyD,UAAA,KACA,KAAAc,QAAA,EACA,KAAAL,aAAA,EACA,KAAAzP,SAAA,EACA,KAAA6P,WAAA,MC9IoV,ICQhV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXzQ,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,iBAAiB,CAACJ,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,MAAM,CAACI,YAAY,gCAAgCsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIoR,cAAclR,EAAG,MAAM,CAACI,YAAY,YAAY,CAACN,EAAIiB,GAAG,YAAYf,EAAG,YAAY,CAACE,MAAM,CAAC,SAAW,UAAUiR,MAAM,CAACtS,MAAOiB,EAAIsR,QAAS7K,SAAS,SAAU8K,GAAMvR,EAAIsR,QAAQC,GAAK1Q,WAAW,YAAY,CAACX,EAAG,MAAM,CAACI,YAAY,iBAAiB,CAACJ,EAAG,MAAM,CAACI,YAAY,WAAW,CAACJ,EAAG,MAAM,CAACI,YAAY,WAAW,CAACN,EAAIiB,GAAG,cAAcf,EAAG,aAAa,CAACI,YAAY,UAAUF,MAAM,CAAC,KAAO,SAAS,KAAO,UAAUwB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIwR,aAAa,CAACxR,EAAIiB,GAAG,SAASf,EAAG,aAAa,CAACI,YAAY,UAAUF,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUwB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIyR,UAAU,CAACzR,EAAIiB,GAAG,MAAMjB,EAAIkB,GAAGlB,EAAI0R,aAAa,QAAQ,GAAGxR,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOiB,EAAI0R,YAAY,EAAG7Q,WAAW,mBAAmB,CAACX,EAAG,MAAM,CAACI,YAAY,iBAAiBN,EAAIoB,GAAIpB,EAAI2R,SAAS,SAASxT,GAAG,OAAO+B,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOZ,EAAEyT,QAAS/Q,WAAW,cAAcxB,IAAIlB,EAAEoD,GAAGjB,YAAY,aAAa,CAACJ,EAAG,aAAa,CAACE,MAAM,CAAC,KAAOjC,EAAE0T,aAAa,IAAM1T,EAAE4C,UAAU,KAAO,OAAO,MAAK,KAAKb,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,aAAa,CAACE,MAAM,CAAC,WAAY,EAAM,eAAc,EAAM,YAAc,MAAMiR,MAAM,CAACtS,MAAOiB,EAAI8R,WAAYrL,SAAS,SAAU8K,GAAMvR,EAAI8R,WAAWP,GAAK1Q,WAAW,iBAAiB,GAAGX,EAAG,MAAM,CAACI,YAAY,gBAAgB,CAACJ,EAAG,MAAM,CAACI,YAAY,aAAaF,MAAM,CAAC,OAAS,SAASJ,EAAIoB,GAAIpB,EAAI2R,SAAS,SAASxT,GAAG,OAAO+B,EAAG,MAAM,CAACO,WAAW,CAAC,CAACnC,KAAK,OAAOoC,QAAQ,SAAS3B,MAAOZ,EAAE0T,aAAaE,SAAS/R,EAAI8R,YAAajR,WAAW,wCAAwCxB,IAAIlB,EAAE+O,QAAQ,CAAChN,EAAG,MAAM,CAACI,YAAY,cAAcsB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO7B,EAAIgS,YAAY7T,MAAM,CAAC+B,EAAG,aAAa,CAACE,MAAM,CAAC,KAAOjC,EAAE0T,aAAa,OAAS1T,EAAE+P,OAAO,IAAM/P,EAAE4C,UAAU,KAAO,MAAMb,EAAG,MAAM,CAACI,YAAY,eAAe,CAACN,EAAIiB,GAAGjB,EAAIkB,GAAG/C,EAAE0T,iBAAiB3R,EAAG,MAAM,CAACI,YAAY,kBAAkB,CAACJ,EAAG,eAAe,CAACE,MAAM,CAAC,SAAWjC,EAAE8T,OAAO,gBAAgB,WAAWrQ,GAAG,CAAC,OAAS,SAASC,GAAQ,OAAO7B,EAAIkS,gBAAgB/T,KAAKkT,MAAM,CAACtS,MAAOZ,EAAEyT,QAASnL,SAAS,SAAU8K,GAAMvR,EAAImS,KAAKhU,EAAG,UAAWoT,IAAM1Q,WAAW,gBAAgB,IAAI,QAAO,UAAU,IAEz1ER,EAAkB,GC8CtB,G,UAAA,CACA/B,KAAA,eACAiR,WAAA,CACAC,aAEAtT,OACA,OACAoV,SAAA,EACAQ,WAAA,GACAH,QAAA,KAGAtD,MAAA,CACA7L,IAAA,CACA8L,KAAA3R,QAEAyF,QAAA,CACAkM,KAAAI,QAEArN,UAAA,CACAiN,KAAA8D,OAEA9P,WAAA,CACAgM,KAAAC,SAGAK,QAAA,CACAwC,UACA,KAAAE,SAAA,EAEA,KAAA9O,IAAAkK,iBAAA,KAAAtK,SAAAqG,KAAAkJ,IACAA,EAAA5K,QAAA5I,IACAA,EAAAyT,QAAA,KAAAS,QAAAlU,EAAA+O,QACA/O,EAAA8T,OAAA9T,EAAAyT,UAEA,KAAAD,UAAAW,OAAAnU,MAAAgP,SAGA+E,gBAAA/T,GACA,KAAAuT,YAAA,KAAApP,aACAnE,EAAAyT,SAAA,EACA,KAAAW,OAAA,YAAAjQ,uBAGA0P,YAAA7T,GACAA,EAAA8T,SACA9T,EAAAyT,SAAAzT,EAAAyT,QACA,KAAAM,gBAAA/T,KAGAqT,UACA,KAAAG,QAAA5K,QAAA5I,IACAA,EAAA8T,SACA9T,EAAAyT,SAAA,MAIAH,OAEA,IAAApQ,EAAA,GACA,KAAAsQ,QAAA5K,QAAA5I,IACAA,EAAAyT,UAAAzT,EAAA8T,QACA5Q,EAAArE,KAAA,CACAuE,GAAApD,EAAA+O,OACAlM,SAAA7C,EAAA0T,aACA9Q,UAAA5C,EAAA4C,UACAgC,UAAA,EACAJ,cAAA,MAIAtB,EAAA3E,OAAA,IACA,KAAA8F,IAAAyK,OAAA,KAAA7K,QAAAf,GAEA,KAAA8O,MAAA,KAAA9O,IAGA,KAAAiQ,SAAA,GAEAe,QAAAnF,GACA,aAAA7L,UAAAmR,KAAAlR,KAAAC,IAAA2L,KAGA2B,SAAA,CACA6C,cACA,YAAAC,QAAAW,OAAAnU,KAAAyT,SAAAlV,WCrIqV,ICQjV,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,OAIa,I,QC8Ef,IACAR,OACA,OACAuW,IAAA,GACAjH,OAAA,IAAAE,EACA2E,OAAA,IAAA/K,EACAoN,SAAA,IAAAnF,EACA/K,IAAA,KACAiD,OAAA,KACA9C,cAAA,EACAmJ,UAAA,EACAjJ,WAAA,EACAE,UAAA,EACA4P,SAAA,EACAC,UAAA,KACAC,eAAA,KACAC,SAAA,KACAC,YAAA,KACA5K,MAAA,OACA5E,QAAA,GACAC,UAAA,GACApB,QAAA,KACA8K,OAAA,KACA1L,UAAA,KACAwR,SAAA,KACApS,QAAA,EACAS,UAAA,GACAgB,OAAA,GACA4Q,GAAA,IACAC,GAAA,IACAC,SAAA,IACAC,SAAA,MAGA7D,WAAA,CACAC,YACA6D,aACAC,cACAC,gBAEA3E,QAAA,CACA4E,YAEA,KAAAC,UAGA,KAAA7S,SAAA,KAAAqC,WAEA4B,QAAAC,IAAA,UACA,KAAAtC,IAAA4K,OAAA,KAAAhL,UACA,KAAAxB,QAAA,gBAAAuH,MAIA,KAAAlF,YAEA4B,QAAAC,IAAA,UACA,KAAAtC,IAAA2K,KAAA,KAAA/K,WALAyC,QAAAC,IAAA,UACA,KAAAtC,IAAAiC,OAAA,KAAArC,YAOAN,aAAAsN,GACAvK,QAAAC,IAAA,gBACAsK,EAAA7N,IAAA,KAAAyR,SAEA,KAAAA,SAAA,KAGA,KAAAA,SAAA5D,EAAA7N,GAEA,KAAAmS,iBACA,KAAAC,iBAEA/Q,qBACA,KAAAD,cAAA,KAAAA,aACA,KAAA+M,MAAAkE,WAAA,GAAA5D,cAAA,KAAArN,cAEA,KAAAH,IAAA6K,OAAA,KAAAjL,QAAA,KAAAW,SAAA,KAAAJ,cACAkC,QAAAC,IAAA,YAAAnC,eAEAR,eAAA2J,GACA,KAAAA,WACA,KAAA+H,aAAApL,KAAA,KAEA,KAAAiH,MAAAkE,WAAA,GAAA5D,cAAA,KAAArN,cAEA,KAAAtB,UAAA0F,QAAAzF,IACA,GAAAA,EAAAC,IAAA,KAAAQ,KAAAR,GAAA,CACA,MAAAuS,EAAA,cAAAxS,EAAAC,GACA,KAAAmO,MAAAoE,GAAA,GAAAzM,aAAA,KAAA5B,WAGA,MAAAsO,EAAAjI,EAAA,UACAjH,QAAAC,IAAA,SAAAiP,KACA7O,MAAA0B,IACA,KAAAoN,UAAA,WACAnP,QAAAC,IAAA,WAAA8B,EAAAvB,YAGArC,iBACA,KAAAD,UAAA,KAAAA,SACA,KAAAhB,KAAAgB,SAAA,KAAAA,SAEA,KAAA8Q,aAAApL,KAAA,KAEA,KAAApH,UAAA0F,QAAAzF,IACA,GAAAA,EAAAC,IAAA,KAAAQ,KAAAR,GAAA,CACA,MAAAuS,EAAA,cAAAxS,EAAAC,GAEA,KAAAmO,MAAAoE,GAAA,GAAAnD,UAAA,KAAAlL,WAIA,KAAAjD,IAAA6K,OAAA,KAAAjL,QAAA,KAAAW,SAAA,KAAAJ,cACAkC,QAAAC,IAAA,YAAA/B,aAGAD,kBACA,KAAAD,WAAA,KAAAA,UAEA,KAAAxB,UAAA0F,QAAAzF,IACA,GAAAA,EAAAC,IAAA,KAAAQ,KAAAR,GAAA,CACA,MAAAuS,EAAA,cAAAxS,EAAAC,GACA,KAAAmO,MAAAoE,GAAA,GAAAjD,SAAA,KAAAhO,cAGAgC,QAAAC,IAAA,cAAAjC,YAEAoR,aAAAC,GAEA,GAAAA,EAAA5F,MAAA,KAAA6F,OAAAC,aAAAC,iBACA,cAAAlM,MAIA,OAAA+L,EAAA5F,MACA,UAAA6F,OAAAC,aAAAC,gBACA,KAAAC,WAAAJ,GACA,MACA,UAAAC,OAAAC,aAAAG,iBACA,KAAAC,YAAAN,GACA,MACA,UAAAC,OAAAC,aAAAK,iBACA,KAAAC,YAAAR,GACA,MACA,UAAAC,OAAAC,aAAAO,eACA,KAAAC,UAAAV,GACA,MACA,UAAAC,OAAAC,aAAAS,iBACA,KAAAC,YAAAZ,GACA,MACA,UAAAC,OAAAC,aAAAW,iBACA,KAAAC,YAAAd,GACA,MACA,UAAAC,OAAAC,aAAAa,eACA,KAAAC,UAAAhB,GACA,MACA,UAAAC,OAAAC,aAAAe,iBACA,KAAAC,YAAAlB,GACA,MACA,UAAAC,OAAAC,aAAAiB,gBACA,KAAAC,WAAApB,GACA,MACA,UAAAC,OAAAC,aAAAmB,iBACA,KAAAC,YAAAtB,GACA,MACA,UAAAC,OAAAC,aAAAqB,oBACA,KAAAC,eAAAxB,GACA,MACA,UAAAC,OAAAC,aAAAuB,iBACA,KAAAC,YAAA1B,GACA,QAIAI,WAAAJ,GAEA,KAAAxE,MAAAmG,UAAA9F,QAEAyE,YAAAN,GACA,GAAAA,EAAA4B,SAGA,YADA,KAAA9M,MAAA,YAGA,MAAA+M,EAAA7B,EAAA8B,OACA,QAAA/S,UAAA,CAEA,MAAA6Q,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAAlD,eACA,QAAAhQ,OAAA,CAEA,MAAAkT,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAAlD,UAEA,KAAAzI,MAAA,UAEA,KAAAuH,MAAAmG,UAAA9E,QAEA,KAAAkF,kBAGAvB,YAAAR,GACA,GAAAA,EAAA4B,SAGA,YADA,KAAA9M,MAAA,YAIA,MAAA+M,EAAA7B,EAAA8B,OAEA,KAAAE,WAAAH,EAAA,UAEAjB,YAAAZ,GAEAA,EAAA8B,OAAA,MACAG,EAAAzI,KAAAC,MAAAuG,EAAAkC,SACA,GAAAD,EAAAE,QAAA7D,KAAAtF,MAAA,KAAAnL,KAAAR,IAEA,YADA,KAAAyH,MAAA,QAGA,IAAAsN,EAAAH,EAAAE,QAAA,GACAE,EAAA,KAAAlV,UAAAmR,KAAAgE,KAAAjV,IAAA+U,GAAAtV,SACAyV,EAAA,IAAAF,KACAJ,EAAAE,QAAA3Z,OAAA,IACA+Z,GAAA,IAAAN,EAAAE,QAAA3Z,WAEA+Z,GAAA,aAAAN,EAAApJ,OACA,KAAAiH,UAAAyC,GAEAN,EAAAE,QAAAtP,QAAAmG,GAAA,KAAAgJ,WAAAhJ,KAEA0H,UAAAV,GAEA,MAAA6B,EAAA7B,EAAA8B,OACA,IAAA5G,EAAA1B,KAAAC,MAAAuG,EAAAkC,SACA,KAAA/D,QAAA0D,IACA,KAAA1U,UAAArE,KAAAoS,GAEA,KAAAsH,UAAA,KAEA,KAAAC,gBAEA,MAAA7C,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAAlD,YAGA,KAAAzI,MAAA,UAEA,KAAAuH,MAAAmG,UAAA9E,QAEA,KAAAkF,iBAEAX,WAAApB,GAEA,MAAA6B,EAAA7B,EAAA8B,OACAtN,EAAAgF,KAAAC,MAAAuG,EAAAkC,SAEAtC,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAA9C,QAAAtI,IAEA8M,YAAAtB,GAEA,MAAA6B,EAAA7B,EAAA8B,OACAlN,EAAA4E,KAAAC,MAAAuG,EAAAkC,SAEAtC,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAA7C,SAAAnI,IAEAkM,cAEA,KAAAhM,MAAA,UAEAkM,UAAAhB,GAEA,MAAA6B,EAAA7B,EAAA8B,OAEA,KAAAE,WAAAH,EAAA,UAEAX,YAAAlB,GAEA,IAAA7S,EAAAqM,KAAAC,MAAAuG,EAAAkC,SAEA,KAAA3T,WAAApB,IAEAqU,eAAAxB,GAEA,MAAA6B,EAAA7B,EAAA8B,OACAhO,EAAA0F,KAAAC,MAAAuG,EAAAkC,SAEAtC,EAAA,cAAAiC,EACA,KAAArG,MAAAoE,GAAA,GAAA3C,aAAAnJ,IAEA4N,YAAA1B,GAEA,MAAA6B,EAAA7B,EAAA8B,OACAY,EAAAlJ,KAAAC,MAAAuG,EAAAkC,SAEA,IAAAhH,EAAA,KAAA/N,UAAAmR,KAAAgE,KAAAjV,IAAAwU,GACA3G,EAAArM,SAAA6T,EAAA7T,SACAqM,EAAAzM,aAAAiU,EAAAjU,cAEAQ,WACA,SAAA0T,iBAGA,OAFA,KAAArU,IAAAsK,OAAA,KAAA1K,QAAA,gBACA,KAAA4G,QAIA,KAAAb,MAAA,UAEA,KAAAuH,MAAAmG,UAAA9E,QAEA,KAAA2C,iBAEA,KAAAG,aAAAiD,QAAA,KAEA,KAAAH,gBAEA,KAAAnU,IAAAqK,OAAA,KAAAzK,SAAAqG,KAAA,KAEA,KAAAwN,kBACA/Q,MAAA,KACA,KAAA8D,aAIA9F,WAEA,KAAAV,IAAAiC,OAAA,KAAArC,SAEA,KAAA4G,MAAA,WAEA3F,SAEA,KAAAb,IAAA2K,KAAA,KAAA/K,SAEA,KAAA4G,MAAA,SAEA5F,WAEA,KAAAZ,IAAA4K,OAAA,KAAAhL,SAEA,KAAA4G,MAAA,UAEA+N,UACA,KAAAF,kBACA,KAAA7N,QAGA,KAAA6K,aAAAiD,QAAA,KAEA,KAAAH,gBAEA,KAAAnU,IAAAmK,MAAA,KAAAvK,QAAA,KAAAf,WAAAoH,KAAA,KAEA,KAAAN,MAAA,QAEA,KAAAuH,MAAAmG,UAAA9F,SACA7K,MAAA0B,IACA,KAAAoC,aAIAgO,SACA,KAAAH,kBACA,KAAA7N,QAGA,KAAAb,MAAA,QAEA,KAAAuL,iBAEA,KAAAG,aAAAiD,QAAA,KAEA,KAAAH,gBAEA,KAAAnU,IAAAwK,KAAA,KAAA5K,SAAAqG,KAAA,KAEA,KAAAN,MAAA,UAEA,KAAA8N,kBACA/Q,MAAA0B,IACA,KAAAoC,aAIAiO,iBACA,KAAAvH,MAAAwH,UAAAzH,QAEAhN,WAAApB,GACAA,EAAA0F,QAAAzF,IACA,KAAA+Q,QAAA/Q,EAAAC,MACA,KAAAF,UAAArE,KAAAsE,GACAuD,QAAAC,IAAA,IAAAxD,EAAAN,oBAIA,KAAA0V,UAAA,KACA,KAAAC,mBAGAtE,QAAAnF,GACA,aAAA7L,UAAAmR,KAAAgE,KAAAjV,IAAA2L,IAEAgJ,WAAAhJ,EAAAuJ,GACA,SAAApE,QAAAnF,GACA,OAGA,MAAA4G,EAAA,cAAA5G,EACA,KAAAwC,MAAAoE,IACA,KAAApE,MAAAoE,GAAA,GAAA9K,QAGA,MAAAmO,EAAA,KAAA9V,UAAA+V,UAAA9V,KAAAC,IAAA2L,GACAkC,EAAA,KAAA/N,UAAA8V,GAEA,KAAA9V,UAAAzD,OAAAuZ,EAAA,GACA,KAAAvW,QAAA6V,GACA,KAAAzC,UAAA,IAAA5E,EAAApO,YAAAyV,KAGA,KAAApV,UAAA3E,QAAA,GACA,KAAA2G,SAGA,KAAAgU,SAAAjI,KACA,KAAA4D,SAAA,KACA,KAAAU,iBACA,KAAAC,kBAGAE,aACA,WAAArP,QAAA,CAAA+D,EAAA9D,KACA,KAAA1B,SAEA,KAAAyI,OAAAK,UAAA,KAAAC,UAAArD,KAAAhD,IACAZ,QAAAC,IAAA,WACA,KAAAW,SAEA,KAAAiK,MAAAkE,WAAA,GAAAnE,KAAAhK,GACA8C,EAAA9C,KACAP,MAAA0B,IACA,KAAAoN,UAAApN,EAAAvB,SACAR,QAAAC,IAAA,aAAA8B,EAAAvB,SACAZ,EAAAmC,KAIA,KAAA4E,OAAAe,YAAA9D,KAAAhD,IACAZ,QAAAC,IAAA,WACA,KAAAW,SAEA,KAAAiK,MAAAkE,WAAA,GAAAnE,KAAAhK,GACA8C,EAAA9C,KACAP,MAAA0B,IACA,KAAAoN,UAAApN,EAAAvB,SACAR,QAAAC,IAAA,aAAA8B,EAAAvB,SACAZ,EAAAmC,QAKA+P,gBAEA,KAAAtV,UAAA0F,QAAAzF,IACA,GAAAA,EAAAC,IAAA,KAAA2L,OAAA,CACA,MAAA4G,EAAA,cAAAxS,EAAAC,GAEA,SAAAmO,MAAAoE,GAAA,GAAArD,OAAA,CACA,MAAAjL,EAAA,CACA8R,WAAA,KAAAjV,OAAAiV,YAEA,KAAA5H,MAAAoE,GAAA,GAAAzN,KAAA,KAAA7D,IAAAgD,EAAA,KAAAC,aAKAwQ,gBACA,KAAArD,YACA,KAAAD,SAAA,EACA,KAAAC,UAAA2E,YAAA,KACA,KAAA5E,YACA,OAGA6E,iBAEA,KAAA3E,gBAAA4E,cAAA,KAAA5E,gBACA,KAAAA,eAAA0E,YAAA,KACA,KAAA/U,IAAA8K,UAAA,KAAAlL,UACA,OAEAyU,iBAEA,YAAArL,OAAA7F,aAKA,KAAA0K,OAAA1K,aACA,KAAAqO,UAAA,iDACA,IANA,KAAAA,UAAA,YACA,IASAA,UAAAyC,IACA,KAAA1D,cAAA,IAAA2E,MAAAC,UAAA,KAAA5E,YAAA,KACA,KAAAR,OAAAkE,GACA,KAAA1D,aAAA,IAAA2E,MAAAC,UACA9S,QAAAC,IAAA2R,IAGA7M,WAAA,SAAAoK,UAAAyC,GAAA,MAGA9C,gBAEA9O,QAAAC,IAAA,iBACA,MAAA8S,EAAAhI,SAAAC,eAAA,UACAgI,EAAAjI,SAAAC,eAAA,WACAiI,EAAAlI,SAAAC,eAAA,WACA,KAAAxO,UAAA0F,QAAAqI,IACA,MAAA7N,EAAA,QAAA6N,EAAA7N,GACA2K,EAAA0D,SAAAC,eAAAtO,GACA,KAAAE,aAEA,KAAAuR,UAAA5D,EAAA7N,GACAqW,EAAAG,YAAA7L,GAEA4L,EAAAE,OAAA9L,GAJA2L,EAAAG,OAAA9L,MAQAwH,iBACA,IAAA5E,EAAAlP,OAAAqY,WACAlJ,EAAAnP,OAAAsY,YACArT,QAAAC,IAAA,OAAAgK,EAAAC,GACA,IAAAoJ,EAAA,KAAA9W,UAAA3E,OACA,GAAAoS,GAAAC,GAAAoJ,EAAA,CASA,GALApJ,GAAA,IAAAD,EAAA,IAEAqJ,EAAA,KAAA9V,OAAAC,aACA6V,GAAA,GAEA,KAAA1W,aACA,KAAAwR,GAAAnE,EAAA,EACA,KAAAoE,GAAApE,EAAA,EACA,KAAAqE,SAAArE,EACA,KAAAsE,SAAArE,EAAA,KAAAmE,GAEA,KAAAE,SAAA,IAAAtE,IACA,KAAAsE,SAAA,IAAAtE,OAEA,CAEA,IAAAsJ,EAAAC,KAAAC,KAAAD,KAAAE,KAAAJ,IACAK,EAAAH,KAAAC,KAAAH,EAAAC,GACA,KAAAnF,GAAAnE,EAAAsJ,EACA,KAAAlF,GAAAnE,EAAAyJ,EAEA,KAAAtF,GAAA,SAAAD,KACA,KAAAC,GAAA,SAAAD,IAGApO,QAAAC,IAAA,YAAAmO,GAAA,KAAAC,MAEAjR,KAAAmN,GACA,YAAAiI,SAAAjI,GAAA,KAAA+D,SAAA,KAAAF,IAEA/Q,KAAAkN,GACA,YAAAiI,SAAAjI,GAAA,KAAAgE,SAAA,KAAAF,IAEAvR,aAAAyN,GACA,YAAAiI,SAAAjI,GAAA,KAAAqJ,YAAA,KAAAlW,YAEA8U,SAAAjI,GACA,YAAA4D,UAAA5D,EAAA7N,IAEAmX,YACA,KAAAhG,SAAAlF,OAAA,CAAAnO,EAAAnD,KACA2I,QAAAC,IAAA,YAAAzF,EAAA,IAAAqO,KAAAgD,UAAAxU,IACA,eAAAmD,EAEA,KAAA4U,aAAA/X,GACA,YAAAmD,GACA,KAAAmU,eAIAnN,OAEA,KAAA7D,IAAA,IAAAiK,EAAA,KAAAlJ,QAAA,KAAAC,WAEA,KAAA2E,MAAA,UAEA,KAAAvH,OACA,KAAAmW,UACA,KAAAvV,WAAA,KAAA0L,QAEA,KAAA8J,SAGA,KAAAQ,kBAGAxO,MAAAyN,GAEAA,GAAA,KAAAzC,UAAAyC,GAEA,KAAA7D,WAAA6E,cAAA,KAAA7E,WACA,KAAAC,gBAAA4E,cAAA,KAAA5E,gBAEAjJ,WAAA,KAEA,KAAAzB,MAAA,QAEA,KAAAqD,OAAAxC,QAEApJ,OAAA+Y,IAAAC,YAAA,CACA1c,KAAA,CACAmD,IAAA,eAGA,KAEA,KAAAqQ,MAAAmG,UAAA9E,UAGAlC,SAAA,CACA9M,OACA,YAAAV,UAAAmR,KAAAlR,KAAAC,IAAA,KAAA2L,SAEApM,UACA,YAAAO,UAAAmR,KAAAlR,KAAAC,IAAA,KAAAC,YAEAyB,YACA,sBAAAkF,OAEAxH,UACA,sBAAAwH,OAAA,cAAAA,OAEAsL,UACA,oBAAAtL,OAEA0Q,YACA,sBAAA1Q,OAEA1G,eACA,aAAAuR,UAEAzQ,aACA,oBAAA0Q,eAAA,KAAAC,SAEAuF,cACA,oBAAAtF,qBAAA,KAAAC,eAEA0F,YACA,YAAAzX,UAAA3E,QAEAgG,iBACA,SAAAO,UACA,SAEA,IAAA8V,EAAAV,KAAAW,MAAA,KAAArG,SAAA,IACAsG,EAAA,KAAAtG,SAAA,GACAuG,EAAAH,EAAA,UAKA,OAJAG,GAAAH,EACAG,GAAA,IACAA,GAAAD,EAAA,UACAC,GAAAD,EACAC,IAGAC,MAAA,CACAL,UAAA,CACAM,QAAAC,EAAAC,GACA,KAAA5F,iBACA,KAAAgD,UAAA,KACA,KAAA/C,qBAKA4F,UAEA,MAAAtU,EAAA,IAAAuU,IAAA5Z,OAAA6Z,SAAAC,MACA,KAAAnW,QAAA0B,EAAA0U,aAAAhb,IAAA,WACA,KAAA6E,UAAAkK,KAAAC,MAAA1I,EAAA0U,aAAAhb,IAAA,cACA,KAAA6C,UAAAyD,EAAA0U,aAAAhb,IAAA,aACA,KAAAuO,OAAAjI,EAAA0U,aAAAhb,IAAA,UACA,KAAAiC,OAAA8M,KAAAC,MAAA1I,EAAA0U,aAAAhb,IAAA,WACA,KAAAyD,QAAA6C,EAAA0U,aAAAhb,IAAA,WACA,KAAA0C,UAAAqM,KAAAC,MAAA1I,EAAA0U,aAAAhb,IAAA,cACA,KAAA0D,OAAAqL,KAAAC,MAAA1I,EAAA0U,aAAAhb,IAAA,WAEAiR,SAAA/B,iBAAA,2BAEA,KAAAxH,OAEA,KAAAqS,YAEA9Y,OAAA+Y,IAAAiB,OAAAnH,IACA5N,QAAAC,IAAA,QAAA4I,KAAAgD,UAAA+B,IACA,KAAAA,QAGA7S,OAAA+Y,IAAAC,YAAA,CACA1c,KAAA,CACAmD,IAAA,kBCzyBkV,MCQ9U,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCVA,IACff,KAAA,MACAiR,WAAA,CACAsK,eCZ6T,MCQzT,I,UAAY,eACd,GACA9Z,EACAM,GACA,EACA,KACA,KACA,OAIa,M,QClBf,MAAM+T,GAAe,CACpBC,gBAAgB,IAChBE,iBAAiB,IACdE,iBAAiB,IACjBI,iBAAiB,IACjBE,iBAAiB,IACjBE,eAAe,IACfE,iBAAiB,IACjBR,eAAe,IACfU,gBAAgB,IAChBE,iBAAiB,IACjBE,oBAAoB,IACpBE,iBAAiB,K,qCCJrBmE,OAAI1V,IAAI2V,SACRD,OAAIld,UAAUuX,OAAS6F,EACvBF,OAAIzX,OAAO4X,eAAgB,EAE3B,IAAIH,OAAI,CAEP/Z,OAAQgP,GAAKA,EAAEmL,MACbC,OAAO,S,2DChBV,W,oCCAA,W,kCCAA,W,0FCAAlc,EAAOD,QAAU,IAA0B,2B,kCCA3C", "file": "js/app.2ce7fc19.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LocalVideo.vue?vue&type=style&index=0&id=f75a1fc4&prod&lang=scss&scoped=true\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=style&index=0&id=df7705b6&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('chat-video')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-video\"},[_c('audio',{ref:\"callAudio\",attrs:{\"loop\":\"true\",\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\"}},[_c('source',{attrs:{\"src\":require(\"@/assets/audio/call.wav\")}})]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isReady&&!_vm.isHost),expression:\"!isReady&&!isHost\"}],staticClass:\"call-box\"},[(_vm.inviter)?_c('div',{staticClass:\"inv-avatar\"},[_c('head-image',{attrs:{\"url\":_vm.inviter.headImage,\"name\":_vm.inviter.nickName,\"size\":150}},[_c('div',{staticClass:\"inv-name\"},[_vm._v(_vm._s(_vm.inviter.nickName))])])],1):_vm._e(),_c('div',{staticClass:\"inv-text\"},[_vm._v(\"邀请你加入多人通话\")]),_c('div',{staticClass:\"user-list-text\"},[_vm._v(\"参与通话的还有:\")]),_c('div',{staticClass:\"user-list\"},_vm._l((_vm.userInfos),function(user){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(user.id!=_vm.inviterId),expression:\"user.id!=inviterId\"}],key:user.id},[_c('head-image',{attrs:{\"url\":user.headImage,\"name\":user.nickName,\"size\":40}})],1)}),0)]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady||_vm.isHost),expression:\"isReady||isHost\"}],staticClass:\"video-box\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isLeaderMode),expression:\"!isLeaderMode\"}],staticClass:\"video-list-1\",attrs:{\"id\":\"videos\"}},[_vm._l((_vm.userInfos),function(user){return _c('div',{key:user.id,style:(_vm.toVideoStyle(user)),attrs:{\"id\":'video'+user.id},on:{\"click\":function($event){return _vm.onClickVideo(user)}}},[(user.id==_vm.mine.id)?_c('local-video',{ref:\"localVideo\",refInFor:true,attrs:{\"userInfo\":_vm.mine,\"width\":_vm.toVw(user),\"height\":_vm.toVh(user)},on:{\"switchFacing\":_vm.onSwitchFacing}}):_vm._e(),(user.id!=_vm.mine.id)?_c('remote-video',{ref:'remoteVideo'+user.id,refInFor:true,attrs:{\"userInfo\":user,\"groupId\":_vm.groupId,\"width\":_vm.toVw(user),\"height\":_vm.toVh(user)}}):_vm._e()],1)}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.userInfos.length<_vm.config.maxChannel),expression:\"userInfos.length<config.maxChannel\"}],style:(_vm.videoStyle),attrs:{\"id\":\"invite1\"}},[_c('invite-member',{ref:\"invMember\",attrs:{\"API\":_vm.API,\"groupId\":_vm.groupId,\"userInfos\":_vm.userInfos,\"maxChannel\":_vm.config.maxChannel},on:{\"ok\":_vm.appendUser}})],1)],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isLeaderMode),expression:\"isLeaderMode\"}],staticClass:\"video-list-2\"},[_c('div',{staticClass:\"leader\",attrs:{\"id\":\"leader\"}}),_c('div',{staticClass:\"follower\",attrs:{\"id\":\"follower\"}},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.userInfos.length<_vm.config.maxChannel),expression:\"userInfos.length<config.maxChannel\"}],style:(_vm.videoStyle),attrs:{\"id\":\"invite2\"}},[_c('invite-member',{ref:\"invMember\",attrs:{\"API\":_vm.API,\"groupId\":_vm.groupId,\"userInfos\":_vm.userInfos,\"maxChannel\":_vm.config.maxChannel},on:{\"ok\":_vm.appendUser}})],1)])])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady||_vm.isHost),expression:\"isReady||isHost\"}],ref:\"refCtrl\",staticClass:\"control-bar\"},[_c('div',{staticClass:\"chat-time\"},[_vm._v(_vm._s(_vm.chatTimeString))]),_c('div',{staticClass:\"dev-bar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && _vm.isMicroPhone),expression:\"isReady && isMicroPhone\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-microphone-on icon-front\",on:{\"click\":function($event){return _vm.onSwitchMicroPhone()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"麦克风已开\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && !_vm.isMicroPhone),expression:\"isReady && !isMicroPhone\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-microphone-off icon-back\",on:{\"click\":function($event){return _vm.onSwitchMicroPhone()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"麦克风已关\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && _vm.isSpeaker),expression:\"isReady && isSpeaker\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-speaker-on icon-front\",on:{\"click\":function($event){return _vm.onSwitchSpeaker()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"扬声器已开\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && !_vm.isSpeaker),expression:\"isReady && !isSpeaker\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-speaker-off icon-back\",on:{\"click\":function($event){return _vm.onSwitchSpeaker()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"扬声器已关\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && _vm.isCamera),expression:\"isReady && isCamera\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-camera-on icon-front\",on:{\"click\":function($event){return _vm.onSwitchCamera()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"摄像头已开\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && !_vm.isCamera),expression:\"isReady && !isCamera\"}],staticClass:\"icon-box\"},[_c('div',{staticClass:\"icon iconfont icon-camera-off icon-back\",on:{\"click\":function($event){return _vm.onSwitchCamera()}}}),_c('div',{staticClass:\"icon-text\"},[_vm._v(\"摄像头已关\")])])])]),_c('div',{ref:\"refBot\",staticClass:\"bottom-bar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isHost && !_vm.isChating),expression:\"!isHost && !isChating\"}],staticClass:\"icon iconfont icon-phone-reject red\",on:{\"click\":function($event){return _vm.onReject()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isHost && !_vm.isChating),expression:\"!isHost && !isChating\"}],staticClass:\"icon iconfont icon-phone-accept\",on:{\"click\":function($event){return _vm.onAccept()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isHost && !_vm.isChating),expression:\"isHost && !isChating\"}],staticClass:\"icon iconfont icon-phone-reject red\",on:{\"click\":function($event){return _vm.onCancel()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isChating),expression:\"isChating\"}],staticClass:\"icon iconfont icon-phone-reject red\",on:{\"click\":function($event){return _vm.onQuit()}}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import axios from 'axios'\r\nimport { Toast as toast } from 'vant-green';\r\n\r\nlet createHttpRequest = function(baseUrl, loginInfo){\r\n\tif (window.plus) {\r\n\t\t// IOS系统必须使用plus.net.XMLHttpRequest，否则请求会报错\r\n\t\twindow.XMLHttpRequest = window.plus.net.XMLHttpRequest;\r\n\t}\r\n\r\n\tconst http = axios.create({\r\n\t\tbaseURL: baseUrl,\r\n\t\ttimeout: 1000 * 30,\r\n\t\twithCredentials: true,\r\n\t\theaders: {\r\n\t\t\t'Content-Type': 'application/json; charset=utf-8'\r\n\t\t}\r\n\t})\r\n\r\n\t/**\r\n\t * 请求拦截\r\n\t */\r\n\thttp.interceptors.request.use(config => {\r\n\t\tlet accessToken = loginInfo.accessToken;\r\n\t\tif (accessToken) {\r\n\t\t\tconfig.headers.accessToken = encodeURIComponent(accessToken);\r\n\t\t}\r\n\t\treturn config\r\n\t}, error => {\r\n\t\treturn Promise.reject(error)\r\n\t})\r\n\r\n\t/**\r\n\t * 响应拦截\r\n\t */\r\n\thttp.interceptors.response.use(async response => {\r\n\t\tif (response.data.code == 200) {\r\n\t\t\treturn response.data.data;\r\n\t\t} else if (response.data.code == 401) {\r\n\t\t\tconsole.log(\"token失效，尝试重新获取\")\r\n\t\t\tlet refreshToken = loginInfo.refreshToken;\r\n\t\t\t// 发送请求, 进行刷新token操作, 获取新的token\r\n\t\t\tloginInfo = await http({\r\n\t\t\t\tmethod: 'put',\r\n\t\t\t\turl: '/refreshToken',\r\n\t\t\t\theaders: {\r\n\t\t\t\t\trefreshToken: refreshToken\r\n\t\t\t\t}\r\n\t\t\t}).catch(() => {\r\n\t\t\t\ttoast(\"服务器请求异常\")\r\n\t\t\t})\r\n\t\t\t// 这里需要把headers清掉，否则请求时会报错，原因暂不详...\r\n\t\t\tif (typeof response.config.data != 'object') {\r\n\t\t\t\tresponse.config.headers = undefined;\r\n\t\t\t}\r\n\t\t\t// 重新发送刚才的请求\r\n\t\t\treturn http(response.config)\r\n\t\t} else {\r\n\t\t\ttoast(response.data.message)\r\n\t\t\treturn Promise.reject(response.data)\r\n\t\t}\r\n\t}, error => {\r\n\t\ttoast('服务器出了点小差，请稍后再试')\r\n\t\treturn Promise.reject(error)\r\n\t})\r\n\treturn http;\r\n}\r\n\r\n\r\nexport default createHttpRequest", "import http from \"@/common/request\"\r\n\r\nclass ImWebRtc {\r\n\tconstructor() {\r\n\t\tthis.configuration = {}\r\n\t\tthis.stream = null;\r\n\t\tthis.senders = [];\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.isEnable = function() {\r\n\twindow.RTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window\r\n\t\t.mozRTCPeerConnection;\r\n\twindow.RTCSessionDescription = window.RTCSessionDescription || window.webkitRTCSessionDescription || window\r\n\t\t.mozRTCSessionDescription;\r\n\twindow.RTCIceCandidate = window.RTCIceCandidate || window.webkitRTCIceCandidate || window\r\n\t\t.mozRTCIceCandidate;\r\n\treturn !!window.RTCPeerConnection;\r\n}\r\n\r\nImWebRtc.prototype.init = function(configuration) {\r\n\tthis.configuration = configuration;\r\n\t// 安卓11的webview有bug,需要提前发起一次连接\r\n\t// 参考博客： https://blog.csdn.net/logocool/article/details/136069364\r\n\tif(this.isAndroid11()){\r\n\t\tthis.fixAndroid()\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.setupPeerConnection = function(callback) {\r\n\tthis.peerConnection = new RTCPeerConnection(this.configuration);\r\n\tthis.peerConnection.ontrack = (e) => {\r\n\t\t// 对方的视频流\r\n\t\tcallback(e.streams[0]);\r\n\t};\r\n}\r\n\r\n\r\nImWebRtc.prototype.setStream = function(stream) {\r\n\tthis.senders.forEach((sender)=>{\r\n\t\tthis.peerConnection.removeTrack(sender)\r\n\t})\r\n\tthis.senders = [];\r\n\tstream.getTracks().forEach((track) => {\r\n\t\tlet sender = this.peerConnection.addTrack(track, stream);\r\n\t\tthis.senders.push(sender);\r\n\t});\r\n\tthis.stream = stream;\r\n}\r\n\r\nImWebRtc.prototype.switchStream = function(stream){\r\n\tlet senders = this.peerConnection.getSenders();\r\n\tlet videoTrack = stream.getVideoTracks()[0];\r\n\tlet audioTrack = stream.getAudioTracks()[0];\r\n\tsenders.forEach((sender) => {\r\n\t\tif (sender.track && sender.track.kind == 'video') {\r\n\t\t\tsender.replaceTrack(videoTrack);\r\n\t\t}\r\n\t\tif (sender.track && sender.track.kind == 'audio') {\r\n\t\t\tsender.replaceTrack(audioTrack);\r\n\t\t}\r\n\t});\r\n}\r\n\r\n\r\nImWebRtc.prototype.onIcecandidate = function(callback) {\r\n\tthis.peerConnection.onicecandidate = (event) => {\r\n\t\t// 追踪到候选信息\r\n\t\tif (event.candidate) {\r\n\t\t\tcallback(event.candidate)\r\n\t\t}\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.onStateChange = function(callback) {\r\n\t// 监听连接状态\r\n\tthis.peerConnection.oniceconnectionstatechange = (event) => {\r\n\t\tlet state = event.target.iceConnectionState;\r\n\t\tconsole.log(\"ICE连接状态变化: : \" + state)\r\n\t\tcallback(state)\r\n\t};\r\n}\r\n\r\nImWebRtc.prototype.createOffer = function() {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst offerParam = {};\r\n\t\tofferParam.offerToRecieveAudio = 1;\r\n\t\tofferParam.offerToRecieveVideo = 1;\r\n\t\t// 创建本地sdp信息\r\n\t\tthis.peerConnection.createOffer(offerParam).then((offer) => {\r\n\t\t\t// 设置本地sdp信息\r\n\t\t\tthis.peerConnection.setLocalDescription(offer);\r\n\t\t\t// 发起呼叫请求\r\n\t\t\tresolve(offer)\r\n\t\t}).catch((e) => {\r\n\t\t\treject(e)\r\n\t\t})\r\n\t});\r\n}\r\n\r\n\r\nImWebRtc.prototype.createAnswer = function(offer) {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// 设置远端的sdp\r\n\t\tthis.setRemoteDescription(offer);\r\n\t\t// 创建本地dsp\r\n\t\tconst offerParam = {};\r\n\t\tofferParam.offerToRecieveAudio = 1;\r\n\t\tofferParam.offerToRecieveVideo = 1;\r\n\t\tthis.peerConnection.createAnswer(offerParam).then((answer) => {\r\n\t\t\t// 设置本地sdp信息\r\n\t\t\tthis.peerConnection.setLocalDescription(answer);\r\n\t\t\t// 接受呼叫请求\r\n\t\t\tresolve(answer)\r\n\t\t}).catch((e) => {\r\n\t\t\treject(e)\r\n\t\t})\r\n\t});\r\n}\r\n\r\nImWebRtc.prototype.setRemoteDescription = function(offer) {\r\n\t// 设置对方的sdp信息\r\n\tthis.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));\r\n}\r\n\r\nImWebRtc.prototype.addIceCandidate = function(candidate) {\r\n\t// 添加对方的候选人信息\r\n\tthis.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\r\n}\r\n\r\nImWebRtc.prototype.close = function(uid) {\r\n\t// 关闭RTC连接\r\n\tif (this.peerConnection) {\r\n\t\tthis.peerConnection.close();\r\n\t\tthis.peerConnection.onicecandidate = null;\r\n\t\tthis.peerConnection.onaddstream = null;\r\n\t}\r\n}\r\n\r\n\r\n\r\nImWebRtc.prototype.isAndroid11 = function() {\r\n\tif (window.plus) {\r\n\t\tconst deviceInfo = navigator.userAgent;\r\n\t\tconst androidVersion = deviceInfo.match(/Android ([\\d.]+)/);\r\n\t\tif (androidVersion && androidVersion.length === 2) {\r\n\t\t\tconsole.log(\"androidVersion:\",androidVersion)\r\n\t\t\treturn androidVersion[1]=='11'\r\n\t\t}\r\n\t}\r\n\treturn false;\r\n}\r\n\r\nImWebRtc.prototype.fixAndroid = function() {\r\n\tconsole.log(\"fixAndroid\")\r\n\tthis.configuration.iceCandidatePoolSize = 1;\r\n\tlet peer = new RTCPeerConnection(this.configuration);\r\n\tpeer.createOffer({\r\n\t\tofferToReceiveAudio: true,\r\n\t\tofferToReceiveVideo: true\r\n\t}).then((offer) => {\r\n\t\tpeer.setLocalDescription(offer);\r\n\t\tsetTimeout(() => {\r\n\t\t\tpeer.close()\r\n\t\t\tconsole.log(\"fixAndroid close\")\r\n\t\t}, 1000)\r\n\t})\t\r\n}\r\nexport default ImWebRtc;", "const requestPermissions = (scope, message) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\twindow.plus.android.requestPermissions(\r\n\t\t\t[\"android.permission.\" + scope],\r\n\t\t\tfunction(resultObj) {\r\n\t\t\t\tconsole.log(resultObj, \"resultObj\");\r\n\t\t\t\tresolve(resultObj);\r\n\t\t\t},\r\n\t\t\tfunction(error) {\r\n\t\t\t\treject()\r\n\t\t\t}\r\n\t\t);\r\n\t});\r\n}\r\n\r\n\r\n// 跳转权限设置\r\nlet topMessageView;\r\nconst createTopMessage = (title, message) => {\r\n\ttopMessageView = new window.plus.nativeObj.View(\"topMessageView\", {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tbackgroundColor: \"rgba(0,0,0,0.85)\",\r\n\t});\r\n\ttopMessageView.drawText(\r\n\t\ttitle, {\r\n\t\t\ttop: \"-100px\",\r\n\t\t\tleft: \"10%\",\r\n\t\t\twidth: \"80%\",\r\n\t\t\theight: \"80%\",\r\n\t\t}, {\r\n\t\t\tcolor: \"#ffffff\",\r\n\t\t\tsize: \"22px\",\r\n\t\t}\r\n\t);\r\n\ttopMessageView.drawText(\r\n\t\tmessage, {\r\n\t\t\ttop: \"-50px\",\r\n\t\t\tleft: \"10%\",\r\n\t\t\twidth: \"80%\",\r\n\t\t\theight: \"80%\",\r\n\t\t}, {\r\n\t\t\tcolor: \"#ffffff\",\r\n\t\t\twhiteSpace: \"normal\",\r\n\t\t}\r\n\t);\r\n\t// 显示消息提示框\r\n\ttopMessageView.show();\r\n}\r\n\r\nconst checkAndRequest = async (scope, title, messageTip, settingTip) => {\r\n\tif (window.plus && window.plus.os.name !== \"iOS\") {\r\n\t\tlet t =setTimeout(() => createTopMessage(title, messageTip), 300);\r\n\t\tlet res = await requestPermissions(scope, settingTip);\r\n\t\tclearTimeout(t);\r\n\t\ttopMessageView && topMessageView.close();\r\n\t\tconsole.log(\"res:\", res);\r\n\t\tif (!res.granted[0]) {\r\n\t\t\t// 无权限\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nconst storage = async() => {\r\n\tconst scope = \"WRITE_EXTERNAL_STORAGE\"\r\n\tconst title = \"访问媒体和文件权限说明\";\r\n\tconst messageTip = \"用于用户发送图片、视频、文件或上传头像等场景\";\r\n\tconst settingTip = \"访问媒体和文件权限未获得,此权限用于用户发送图片、视频、文件或上传头像等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\nconst camera = async () => {\r\n\tconst scope = \"CAMERA\"\r\n\tconst title = \"相机使用权限说明\";\r\n\tconst messageTip = \"用于拍照、录像、视频通话等场景\";\r\n\tconst settingTip = \"相机使用权限未获得,此权限用于拍照、录像、视频通话等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\n\r\nconst micro = async() => {\r\n\tconst scope = \"RECORD_AUDIO\"\r\n\tconst title = \"麦克风使用权限说明\";\r\n\tconst messageTip = \"用于拍摄、录制语音消息、视频或语音通话等场景\";\r\n\tconst settingTip = \"麦克风使用权限未获得,此权限用于用于拍摄、录制语音消息、视频或语音通话等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\nexport default {\r\n\tstorage,\r\n\tcamera,\r\n\tmicro\r\n}", "import permission from \"./permission\"\r\n\r\nclass ImCamera {\r\n\tconstructor() {\r\n\t\tthis.stream = null;\r\n\t}\r\n}\r\n\r\nImCamera.prototype.isEnable = function() {\r\n\treturn !!navigator && !!navigator.mediaDevices && !!navigator.mediaDevices.getUserMedia;\r\n}\r\n\r\nImCamera.prototype.openVideo = function(isFacing) {\r\n\tif (this.stream) {\r\n\t\tthis.close();\r\n\t}\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tsetTimeout(async () => {\r\n\t\t\tif (!await permission.camera()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tmessage: \"未能获取摄像头访问权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (!await permission.micro()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tmessage: \"未能获取麦克风权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet facingMode = isFacing ? \"user\" : \"environment\";\r\n\t\t\tlet constraints = {\r\n\t\t\t\tvideo: {\r\n\t\t\t\t\t// 群聊的画面基本都是正方形,长宽均取width\r\n\t\t\t\t\twidth: window.screen.width,\r\n\t\t\t\t\theight: window.screen.width,\r\n\t\t\t\t\tfacingMode: facingMode\r\n\t\t\t\t\t// 当该前置或后置摄像头不可用时，会直接打开失败\r\n\t\t\t\t\t// facingMode: {\r\n\t\t\t\t\t// \texact: facingMode\r\n\t\t\t\t\t// }\r\n\t\t\t\t},\r\n\t\t\t\taudio: {\r\n\t\t\t\t\techoCancellation: true, //音频开启回音消除\r\n\t\t\t\t\tnoiseSuppression: true // 开启降噪\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tnavigator.mediaDevices.getUserMedia(constraints).then((stream) => {\r\n\t\t\t\tconsole.log(\"摄像头打开\")\r\n\t\t\t\tthis.stream = stream;\r\n\t\t\t\tresolve(stream);\r\n\t\t\t}).catch((e) => {\r\n\t\t\t\treject({\r\n\t\t\t\t\tmessage: \"摄像头未能正常打开\"\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t});\t\r\n\t})\r\n}\r\n\r\nImCamera.prototype.openAudio = function() {\r\n\tif (this.stream) {\r\n\t\tthis.close();\r\n\t}\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tsetTimeout(async()=>{\r\n\t\t\tif (!await permission.micro()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tmessage: \"未能获取麦克风权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet constraints = {\r\n\t\t\t\tvideo: false,\r\n\t\t\t\taudio: {\r\n\t\t\t\t\techoCancellation: true, //音频开启回音消除\r\n\t\t\t\t\tnoiseSuppression: true // 开启降噪\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tnavigator.mediaDevices.getUserMedia(constraints).then((stream) => {\r\n\t\t\t\tthis.stream = stream;\r\n\t\t\t\tresolve(stream);\r\n\t\t\t}).catch(() => {\r\n\t\t\t\tconsole.log(\"麦克风未能正常打开\")\r\n\t\t\t\treject({\r\n\t\t\t\t\tcode: 0,\r\n\t\t\t\t\tmessage: \"麦克风未能正常打开\"\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t})\r\n\t})\r\n}\r\n\r\n\r\nImCamera.prototype.close = function() {\r\n\t// 停止流\r\n\tif (this.stream) {\r\n\t\tthis.stream.getTracks().forEach((track) => {\r\n\t\t\ttrack.stop();\r\n\t\t});\r\n\t\tthis.stream = null;\r\n\t}\r\n}\r\n\r\nexport default ImCamera;", "import createHttpRequest from './request'\r\n\r\nclass ImApi {\r\n\tconstructor(baseUrl, loginInfo) {\r\n\t\tthis.http = createHttpRequest(baseUrl, loginInfo);\r\n\t}\r\n}\r\n\r\nImApi.prototype.findGroupMembers = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/group/members/' + groupId,\r\n\t\tmethod: 'get'\r\n\t})\r\n}\r\n\r\nImApi.prototype.setup = function(groupId, userInfos) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserInfos\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/setup',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\nImApi.prototype.accept = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/accept?groupId='+groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.reject = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/reject?groupId='+groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.failed = function(groupId,reason) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\treason\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/failed',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\n\r\nImApi.prototype.join = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/join?groupId='+groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.invite = function(groupId, userInfos) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserInfos\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/invite',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\n\r\nImApi.prototype.offer = function(groupId, userId, offer) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserId,\r\n\t\toffer\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/offer',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\nImApi.prototype.answer = function(groupId, userId, answer) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserId,\r\n\t\tanswer\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/answer',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\nImApi.prototype.quit = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/quit?groupId=' + groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.cancel = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/cancel?groupId=' + groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.candidate = function(groupId, userId, candidate) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserId,\r\n\t\tcandidate\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/candidate',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\nImApi.prototype.device = function(groupId, isCamera, isMicroPhone ) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tisCamera,\r\n\t\tisMicroPhone\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/device',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\n\r\nImApi.prototype.candidate = function(groupId, userId, candidate) {\r\n\tlet formData = {\r\n\t\tgroupId,\r\n\t\tuserId,\r\n\t\tcandidate\r\n\t}\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/candidate',\r\n\t\tmethod: 'post',\r\n\t\tdata: formData\r\n\t})\r\n}\r\n\r\nImApi.prototype.heartbeat = function(groupId) {\r\n\treturn this.http({\r\n\t\turl: '/webrtc/group/heartbeat?groupId=' + groupId,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\n\r\nexport default ImApi;", "\r\n/**\r\n * 接收来自uniapp的消息\r\n */\r\nclass UniEvent{}\r\n\r\nUniEvent.prototype.listen = function(callback){\r\n\t// APP\r\n\twindow.onEvent = (e)=>{\r\n\t\tlet event = JSON.parse(decodeURIComponent(e));\r\n\t\tcallback(event.key,event.data)\r\n\t}\r\n\t// H5\r\n\twindow.addEventListener('message', function (e) {\r\n\t\tconst event = e.data;\r\n\t\tcallback(event.key,event.data)\r\n\t},false)\r\n\t\r\n}\r\n\r\nexport default UniEvent;", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"head-image\"},[_c('img',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.url),expression:\"url\"}],staticClass:\"avatar-image\",style:(_vm.avatarImageStyle),attrs:{\"src\":_vm.url,\"loading\":\"lazy\"}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.url),expression:\"!url\"}],staticClass:\"avatar-text\",style:(_vm.avatarTextStyle)},[_vm._v(\" \"+_vm._s(_vm.name.substring(0,1).toUpperCase())+\" \")]),(_vm.online)?_c('div',{staticClass:\"online\",attrs:{\"title\":\"用户当前在线\"}}):_vm._e(),_vm._t(\"default\")],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"head-image\">\r\n\t\t<img class=\"avatar-image\" v-show=\"url\" :src=\"url\" :style=\"avatarImageStyle\" loading=\"lazy\" />\r\n\t\t<div class=\"avatar-text\" v-show=\"!url\" :style=\"avatarTextStyle\">\r\n\t\t\t{{name.substring(0,1).toUpperCase()}}\r\n\t\t</div>\r\n\t\t<div v-if=\"online\" class=\"online\" title=\"用户当前在线\"></div>\r\n\t\t<slot></slot>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"headImage\",\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcolors: [\"#7dd24b\", \"#c7515a\", \"#db68ef\", \"#15d29b\", \"#85029b\",\r\n\t\t\t\t\t\"#c9b455\", \"#fb2609\", \"#bda818\", \"#af0831\", \"#326eb6\"\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tsize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 50\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t\tradius:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"10%\"\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t\turl: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\tname: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"?\"\r\n\t\t\t},\r\n\t\t\tonline: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {},\r\n\t\tcomputed: {\r\n\t\t\tavatarImageStyle() {\r\n\t\t\t\tlet w = this.width ? this.width : this.size;\r\n\t\t\t\tlet h = this.height ? this.height : this.size;\r\n\t\t\t\treturn `width:${w}px; height:${h}px;\r\n\t\t\t\t\tborder-radius: ${this.radius};`\r\n\t\t\t},\r\n\t\t\tavatarTextStyle() {\r\n\t\t\t\tlet w = this.width ? this.width : this.size;\r\n\t\t\t\tlet h = this.height ? this.height : this.size;\r\n\t\t\t\treturn `width: ${w}px;height:${h}px;\r\n\t\t\t\t\tcolor:${this.textColor};font-size:${w*0.6}px;\r\n\t\t\t\t\tborder-radius: ${this.radius};`\r\n\t\t\t},\r\n\t\t\ttextColor() {\r\n\t\t\t\tlet hash = 0;\r\n\t\t\t\tfor (var i = 0; i < this.name.length; i++) {\r\n\t\t\t\t\thash += this.name.charCodeAt(i);\r\n\t\t\t\t}\r\n\t\t\t\treturn this.colors[hash % this.colors.length];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.head-image {\r\n\t\tposition: relative;\r\n\t\tcursor: pointer;\r\n\r\n\t\t.avatar-image {\r\n\t\t\tposition: relative;\r\n\t\t\toverflow: hidden;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.avatar-text {\r\n\t\t\tbackground-color: #f2f2f2;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tborder: 1px solid #ccc;\r\n\t\t}\r\n\t\t\r\n\t\t.online {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: -10%;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 28px;\r\n\t\t\theight: 28px;\r\n\t\t\tbackground: limegreen;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder: 4px solid white;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./HeadImage.vue?vue&type=template&id=df7705b6&scoped=true\"\nimport script from \"./HeadImage.vue?vue&type=script&lang=js\"\nexport * from \"./HeadImage.vue?vue&type=script&lang=js\"\nimport style0 from \"./HeadImage.vue?vue&type=style&index=0&id=df7705b6&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"df7705b6\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"local-video\"},[_c('head-image',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isReady||!_vm.userInfo.isCamera),expression:\"!isReady||!userInfo.isCamera\"}],staticClass:\"head-image\",attrs:{\"width\":_vm.width,\"height\":_vm.height,\"url\":_vm.userInfo.headImage,\"name\":_vm.userInfo.nickName,\"radius\":'0'}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady&&_vm.userInfo.isCamera),expression:\"isReady&&userInfo.isCamera\"}],staticClass:\"control-bar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && _vm.isFacing),expression:\"isReady && isFacing\"}],staticClass:\"icon iconfont icon-camera-front\",on:{\"click\":function($event){$event.stopPropagation();return _vm.onSwitchFacing()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady && !_vm.isFacing),expression:\"isReady && !isFacing\"}],staticClass:\"icon iconfont icon-camera-back\",on:{\"click\":function($event){$event.stopPropagation();return _vm.onSwitchFacing()}}})]),_c('video',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isReady&&_vm.userInfo.isCamera),expression:\"isReady&&userInfo.isCamera\"}],ref:\"video\",staticClass:\"video\",attrs:{\"id\":\"localVideo\",\"x5-video-player-fullscreen\":\"true\",\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\",\"muted\":\"\"},domProps:{\"muted\":true}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"local-video\">\r\n\t\t<head-image v-show=\"!isReady||!userInfo.isCamera\" class=\"head-image\" \r\n\t\t\t:width=\"width\" :height=\"height\" :url=\"userInfo.headImage\"\r\n\t\t\t:name=\"userInfo.nickName\" :radius=\"'0'\"></head-image>\r\n\t\t<div v-show=\"isReady&&userInfo.isCamera\" class=\"control-bar\">\r\n\t\t\t<div v-show=\"isReady && isFacing\" class=\"icon iconfont icon-camera-front\" @click.stop=\"onSwitchFacing()\"></div>\r\n\t\t\t<div v-show=\"isReady && !isFacing\" class=\"icon iconfont icon-camera-back\" @click.stop=\"onSwitchFacing()\"></div>\r\n\t\t</div>\r\n\t\t<video v-show=\"isReady&&userInfo.isCamera\" class=\"video\" id=\"localVideo\" ref=\"video\"  x5-video-player-fullscreen=\"true\" x5-playsinline\r\n\t\t\t   playsinline webkit-playsinline muted></video>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ImCamera from '@/common/camera'\r\n\timport HeadImage from '@/components/HeadImage'\r\n\texport default {\r\n\t\tname: \"localVideo\",\r\n\t\tcomponents: {\r\n\t\t\tHeadImage\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcamera: new ImCamera(), // 摄像头和麦克风\r\n\t\t\t\tstream: null,\r\n\t\t\t\tisReady: false,\r\n\t\t\t\tisFacing: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tuserInfo: {\r\n\t\t\t\ttype: Object\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\topen(stream) {\r\n\t\t\t\tthis.stream = stream;\r\n\t\t\t\t// 显示本地视频\r\n\t\t\t\tthis.$refs.video.srcObject = stream;\r\n\t\t\t\t// 这种方式有部分设备会失效\r\n\t\t\t\t// this.$refs.video.muted = true;\r\n\t\t\t\t// 这种原始方式才好使\r\n\t\t\t\tdocument.getElementById(\"localVideo\").muted = true;\r\n\t\t\t\tthis.$refs.video.play().catch(() => {\r\n\t\t\t\t\tconsole.log(\"本地流播放异常\")\r\n\t\t\t\t});\r\n\t\t\t\tthis.isReady = !!stream;\r\n\t\t\t},\r\n\t\t\tsetMicroPhone(isEnable) {\r\n\t\t\t\tthis.stream.getTracks().forEach((track => {\r\n\t\t\t\t\tif (track.kind === 'audio') {\r\n\t\t\t\t\t\ttrack.enabled = isEnable;\r\n\t\t\t\t\t}\r\n\t\t\t\t}))\r\n\t\t\t},\r\n\t\t\tsetCamera(isEnable) {\r\n\t\t\t\tthis.stream.getTracks().forEach((track => {\r\n\t\t\t\t\tif (track.kind === 'video') {\r\n\t\t\t\t\t\ttrack.enabled = isEnable;\r\n\t\t\t\t\t}\r\n\t\t\t\t}))\r\n\t\t\t},\r\n\t\t\tonSwitchFacing() {\r\n\t\t\t\tthis.isFacing = !this.isFacing;\r\n\t\t\t\tthis.$emit(\"switchFacing\", this.isFacing);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.local-video {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: darkgray;\r\n\r\n\t\t.head-image{\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\t\t\r\n\t\t.video {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tobject-fit: cover;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.control-bar {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 80px;\r\n\t\t\tbottom: 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\tpadding: 10px;\r\n\t\t\tz-index: 2;\r\n\t\t\t\r\n\t\t\t.icon {\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t\tfont-size: 40px;\r\n\t\t\t\tcolor: white;\r\n\t\t\t\tbackground-color: black;\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LocalVideo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LocalVideo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./LocalVideo.vue?vue&type=template&id=f75a1fc4&scoped=true\"\nimport script from \"./LocalVideo.vue?vue&type=script&lang=js\"\nexport * from \"./LocalVideo.vue?vue&type=script&lang=js\"\nimport style0 from \"./LocalVideo.vue?vue&type=style&index=0&id=f75a1fc4&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f75a1fc4\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{ref:\"box\",staticClass:\"remote-video\"},[_c('van-loading',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isConnected),expression:\"!isConnected\"}],staticClass:\"loading\",attrs:{\"color\":\"#5870e6 \",\"size\":\"50\"}}),_c('head-image',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isConnected||!_vm.userInfo.isCamera),expression:\"!isConnected||!userInfo.isCamera\"}],staticClass:\"head-image\",attrs:{\"width\":_vm.width,\"height\":_vm.height,\"url\":_vm.userInfo.headImage,\"name\":_vm.userInfo.nickName,\"radius\":'0'}}),_c('div',{staticClass:\"info-bar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.userInfo.isMicroPhone),expression:\"userInfo.isMicroPhone\"}],staticClass:\"icon iconfont icon-microphone-on\"}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.userInfo.isMicroPhone),expression:\"!userInfo.isMicroPhone\"}],staticClass:\"icon iconfont icon-microphone-off\"}),_c('div',{staticClass:\"name\"},[_vm._v(\" \"+_vm._s(_vm.userInfo.nickName)+\" \")])]),_c('video',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected&&_vm.userInfo.isCamera),expression:\"isConnected&&userInfo.isCamera\"}],ref:\"video\",staticClass:\"video\",attrs:{\"id\":\"remoteVideo\",\"x5-video-player-fullscreen\":\"true\",\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\"}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"remote-video\" ref=\"box\">\r\n\t\t<van-loading v-show=\"!isConnected\" class=\"loading\" color=\"#5870e6 \" size=\"50\" />\r\n\t\t<head-image v-show=\"!isConnected||!userInfo.isCamera\" class=\"head-image\" :width=\"width\" :height=\"height\"\r\n\t\t\t:url=\"userInfo.headImage\" :name=\"userInfo.nickName\" :radius=\"'0'\"></head-image>\r\n\t\t<div class=\"info-bar\">\r\n\t\t\t<div v-show=\"userInfo.isMicroPhone\" class=\"icon iconfont icon-microphone-on\"></div>\r\n\t\t\t<div v-show=\"!userInfo.isMicroPhone\" class=\"icon iconfont icon-microphone-off\"></div>\r\n\t\t\t<div class=\"name\">\r\n\t\t\t\t{{userInfo.nickName}}\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<video v-show=\"isConnected&&userInfo.isCamera\" id=\"remoteVideo\" class=\"video\" ref=\"video\" \r\n\t\t\tx5-video-player-fullscreen=\"true\" x5-playsinline\r\n\t\t\tplaysinline webkit-playsinline >\r\n\t\t</video>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ImWebRtc from '@/common/webrtc'\r\n\timport ImCamera from '@/common/camera'\r\n\timport HeadImage from '@/components/HeadImage'\r\n\texport default {\r\n\t\tname: \"localVideo\",\r\n\t\tcomponents: {\r\n\t\t\tHeadImage\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tAPI: null,\r\n\t\t\t\twebrtc: new ImWebRtc(), // webrtc相关\r\n\t\t\t\tlocalStream: null,\r\n\t\t\t\tremoteStream: null,\r\n\t\t\t\tcandidates: [],\r\n\t\t\t\tisInit: false,\r\n\t\t\t\tisConnected: false,\r\n\t\t\t\tisReady: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number\r\n\t\t\t},\r\n\t\t\tgroupId: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\tuserInfo: {\r\n\t\t\t\ttype: Object\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit(API, configuration, localStream) {\r\n\t\t\t\tthis.isInit = true;\r\n\t\t\t\tthis.API = API;\r\n\t\t\t\tthis.webrtc.init(configuration);\r\n\t\t\t\tthis.localStream = localStream;\r\n\t\t\t\t// 建立webrtc连接\r\n\t\t\t\tthis.webrtc.setupPeerConnection((remoteStream) => {\r\n\t\t\t\t\t// 对方视频流\r\n\t\t\t\t\tconsole.log(\"获取到远端流\")\r\n\t\t\t\t\tthis.remoteStream = remoteStream;\r\n\t\t\t\t\tthis.$refs.video.srcObject = remoteStream;\r\n\t\t\t\t\tthis.$refs.video.play().catch(() => {\r\n\t\t\t\t\t\tconsole.log(\"远端流播放失败\")\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t// 设置本地流\r\n\t\t\t\tthis.webrtc.setStream(localStream);\r\n\t\t\t\t// 监听候选信息\r\n\t\t\t\tthis.webrtc.onIcecandidate((candidate) => {\r\n\t\t\t\t\tif (this.isReady) {\r\n\t\t\t\t\t\t// 连接已就绪,直接发送\r\n\t\t\t\t\t\tthis.API.candidate(this.groupId, this.userInfo.id, JSON.stringify(candidate));\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 连接未就绪,缓存起来，连接后再发送\r\n\t\t\t\t\t\tthis.candidates.push(candidate)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// 监听连接成功状态\r\n\t\t\t\tthis.webrtc.onStateChange((state) => {\r\n\t\t\t\t\tif (state == \"connected\") {\r\n\t\t\t\t\t\t// 就绪\r\n\t\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\t} else if (state == \"disconnected\") {\r\n\t\t\t\t\t\t//this.$toast(\"当前通话质量不佳\")\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\treconnect(localStream) {\r\n\t\t\t\tthis.localStream = localStream;\r\n\t\t\t\tthis.webrtc.setStream(localStream);\r\n\t\t\t\tthis.connect();\r\n\t\t\t},\r\n\t\t\tswitchStream(localStream) {\r\n\t\t\t\tthis.localStream = localStream;\r\n\t\t\t\tthis.webrtc.switchStream(localStream);\r\n\t\t\t},\r\n\t\t\tsetMute(isMute) {\r\n\t\t\t\tconst video = document.getElementById(\"remoteVideo\");\r\n\t\t\t\tvideo.pause();\r\n\t\t\t\tvideo.muted = isMute;\r\n\t\t\t\tvideo.play();\r\n\t\t\t},\r\n\t\t\tconnect() {\r\n\t\t\t\tthis.webrtc.createOffer().then((offer) => {\r\n\t\t\t\t\t// 推送offer给对方\r\n\t\t\t\t\tthis.API.offer(this.groupId, this.userInfo.id, JSON.stringify(offer));\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonOffer(offer) {\r\n\t\t\t\tthis.webrtc.createAnswer(offer).then((answer) => {\r\n\t\t\t\t\t// 推送answer给对方\r\n\t\t\t\t\tthis.API.answer(this.groupId, this.userInfo.id, JSON.stringify(answer));\r\n\t\t\t\t});\r\n\t\t\t\tthis.isReady = true;\r\n\t\t\t},\r\n\t\t\tonAnswer(answer) {\r\n\t\t\t\t// 设置对方answer信息\r\n\t\t\t\tthis.webrtc.setRemoteDescription(answer);\r\n\t\t\t\t// 推送candidate\r\n\t\t\t\tthis.sendCandidate();\r\n\t\t\t\tthis.isReady = true;\r\n\t\t\t},\r\n\t\t\tsetCandidate(candidate) {\r\n\t\t\t\tthis.webrtc.addIceCandidate(candidate);\r\n\t\t\t},\r\n\t\t\tsendCandidate() {\r\n\t\t\t\tthis.candidates.forEach((candidate) => {\r\n\t\t\t\t\tthis.API.candidate(this.groupId, this.userInfo.id, JSON.stringify(candidate));\r\n\t\t\t\t});\r\n\t\t\t\tthis.candidates = [];\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.webrtc.close();\r\n\t\t\t\tthis.$refs.video.srcObject = null;\r\n\t\t\t\tthis.isInit = false;\r\n\t\t\t\tthis.isConnected = false;\r\n\t\t\t\tthis.isReady = false;\r\n\t\t\t\tthis.candidates = [];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.remote-video {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: darkgray;\r\n\r\n\t\t.loading {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\talign-items: center;\r\n\t\t\tz-index: 99\r\n\t\t}\r\n\r\n\t\t.head-image {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tobject-fit: cover;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.info-bar {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 10px;\r\n\t\t\tleft: 10px;\r\n\t\t\ttext-align: left;\r\n\t\t\toverflow: hidden;\r\n\t\t\tcolor: white;\r\n\t\t\tfont-size: 24px;\r\n\t\t\tdisplay: flex;\r\n\t\t\tz-index: 2;\r\n\t\t\tbackground-color: #888;\r\n\t\t\tpadding: 6px;\r\n\t\t\tborder-radius: 10px;\r\n\t\t\t\r\n\t\t\t.icon {\r\n\t\t\t\tfont-size: 30px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.video {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tobject-fit: cover;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RemoteVideo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RemoteVideo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RemoteVideo.vue?vue&type=template&id=8bee7048&scoped=true\"\nimport script from \"./RemoteVideo.vue?vue&type=script&lang=js\"\nexport * from \"./RemoteVideo.vue?vue&type=script&lang=js\"\nimport style0 from \"./RemoteVideo.vue?vue&type=style&index=0&id=8bee7048&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8bee7048\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"invite-member\"},[_c('div',{staticClass:\"invite-btn\"},[_c('div',{staticClass:\"icon iconfont icon-invite-rtc\",on:{\"click\":function($event){return _vm.onPopup()}}}),_c('div',{staticClass:\"inv-text\"},[_vm._v(\"点击邀请\")])]),_c('van-popup',{attrs:{\"position\":\"bottom\"},model:{value:(_vm.isPopup),callback:function ($$v) {_vm.isPopup=$$v},expression:\"isPopup\"}},[_c('div',{staticClass:\"popup-content\"},[_c('div',{staticClass:\"top-bar\"},[_c('div',{staticClass:\"top-tip\"},[_vm._v(\"邀请成员加入通话\")]),_c('van-button',{staticClass:\"top-btn\",attrs:{\"type\":\"danger\",\"size\":\"middle\"},on:{\"click\":function($event){return _vm.onClean()}}},[_vm._v(\"清空 \")]),_c('van-button',{staticClass:\"top-btn\",attrs:{\"type\":\"info\",\"size\":\"middle\"},on:{\"click\":function($event){return _vm.onOk()}}},[_vm._v(\"确定(\"+_vm._s(_vm.checkedSize)+\")\")])],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.checkedSize>0),expression:\"checkedSize>0\"}]},[_c('div',{staticClass:\"checked-users\"},_vm._l((_vm.members),function(m){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(m.checked),expression:\"m.checked\"}],key:m.id,staticClass:\"user-item\"},[_c('head-image',{attrs:{\"name\":m.showNickName,\"url\":m.headImage,\"size\":30}})],1)}),0)]),_c('div',{staticClass:\"search-bar\"},[_c('van-search',{attrs:{\"clearable\":false,\"show-action\":false,\"placeholder\":\"搜索\"},model:{value:(_vm.searchText),callback:function ($$v) {_vm.searchText=$$v},expression:\"searchText\"}})],1),_c('div',{staticClass:\"member-items\"},[_c('div',{staticClass:\"scroll-bar\",attrs:{\"scroll\":\"true\"}},_vm._l((_vm.members),function(m){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(m.showNickName.includes(_vm.searchText)),expression:\"m.showNickName.includes(searchText)\"}],key:m.userId},[_c('div',{staticClass:\"member-item\",on:{\"click\":function($event){return _vm.onClickItem(m)}}},[_c('head-image',{attrs:{\"name\":m.showNickName,\"online\":m.online,\"url\":m.headImage,\"size\":50}}),_c('div',{staticClass:\"member-name\"},[_vm._v(_vm._s(m.showNickName))]),_c('div',{staticClass:\"member-checked\"},[_c('van-checkbox',{attrs:{\"disabled\":m.locked,\"checked-color\":\"#0283ef\"},on:{\"change\":function($event){return _vm.onSwitchChecked(m)}},model:{value:(m.checked),callback:function ($$v) {_vm.$set(m, \"checked\", $$v)},expression:\"m.checked\"}})],1)],1)])}),0)])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"invite-member\">\r\n\t\t<div class=\"invite-btn\">\r\n\t\t\t<div class=\"icon iconfont icon-invite-rtc\" @click=\"onPopup()\"></div>\r\n\t\t\t<div class=\"inv-text\">点击邀请</div>\r\n\t\t</div>\r\n\t\t<van-popup v-model=\"isPopup\" position=\"bottom\">\r\n\t\t\t<div class=\"popup-content\">\r\n\t\t\t\t<div class=\"top-bar\">\r\n\t\t\t\t\t<div class=\"top-tip\">邀请成员加入通话</div>\r\n\t\t\t\t\t<van-button class=\"top-btn\" type=\"danger\" size=\"middle\" @click=\"onClean()\">清空 </van-button>\r\n\t\t\t\t\t<van-button class=\"top-btn\" type=\"info\" size=\"middle\"\r\n\t\t\t\t\t\t@click=\"onOk()\">确定({{checkedSize}})</van-button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-show=\"checkedSize>0\" >\r\n\t\t\t\t\t<div class=\"checked-users\">\r\n\t\t\t\t\t\t<div v-for=\"m in members\" v-show=\"m.checked\" class=\"user-item\" :key=\"m.id\">\r\n\t\t\t\t\t\t\t<head-image :name=\"m.showNickName\" :url=\"m.headImage\" :size=\"30\"></head-image>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"search-bar\">\r\n\t\t\t\t\t<van-search v-model=\"searchText\" :clearable=\"false\" :show-action=\"false\" placeholder=\"搜索\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"member-items\">\r\n\t\t\t\t\t<div class=\"scroll-bar\" scroll=\"true\">\r\n\t\t\t\t\t\t<div v-for=\"m in members\" v-show=\"m.showNickName.includes(searchText)\" :key=\"m.userId\">\r\n\t\t\t\t\t\t\t<div class=\"member-item\" @click=\"onClickItem(m)\">\r\n\t\t\t\t\t\t\t\t<head-image :name=\"m.showNickName\" :online=\"m.online\" :url=\"m.headImage\"\r\n\t\t\t\t\t\t\t\t\t:size=\"50\"></head-image>\r\n\t\t\t\t\t\t\t\t<div class=\"member-name\">{{ m.showNickName}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"member-checked\">\r\n\t\t\t\t\t\t\t\t\t<van-checkbox v-model=\"m.checked\" @change=onSwitchChecked(m) :disabled=\"m.locked\"\r\n\t\t\t\t\t\t\t\t\t\tchecked-color=\"#0283ef\">\r\n\t\t\t\t\t\t\t\t\t</van-checkbox>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</van-popup>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport HeadImage from '@/components/HeadImage'\r\n\r\n\texport default {\r\n\t\tname: \"InviteMember\",\r\n\t\tcomponents: {\r\n\t\t\tHeadImage\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisPopup: false,\r\n\t\t\t\tsearchText: \"\",\r\n\t\t\t\tmembers: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tAPI: {\r\n\t\t\t\ttype: Object\r\n\t\t\t},\r\n\t\t\tgroupId: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\tuserInfos: {\r\n\t\t\t\ttype: Array\r\n\t\t\t},\r\n\t\t\tmaxChannel: {\r\n\t\t\t\ttype: Number\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonPopup() {\r\n\t\t\t\tthis.isPopup = true;\r\n\t\t\t\t// 查找群用户\r\n\t\t\t\tthis.API.findGroupMembers(this.groupId).then((members) => {\r\n\t\t\t\t\tmembers.forEach((m) => {\r\n\t\t\t\t\t\tm.checked = this.isExist(m.userId);\r\n\t\t\t\t\t\tm.locked = m.checked;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.members = members.filter(m => !m.quit);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonSwitchChecked(m) {\r\n\t\t\t\tif (this.checkedSize > this.maxChannel) {\r\n\t\t\t\t\tm.checked = false;\r\n\t\t\t\t\tthis.$toast(`最多支持${this.maxChannel}人同时语音通话`);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonClickItem(m) {\r\n\t\t\t\tif (!m.locked) {\r\n\t\t\t\t\tm.checked = !m.checked;\r\n\t\t\t\t\tthis.onSwitchChecked(m)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonClean() {\r\n\t\t\t\tthis.members.forEach((m) => {\r\n\t\t\t\t\tif (!m.locked) {\r\n\t\t\t\t\t\tm.checked = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonOk() {\r\n\t\t\t\t// 发起邀请\r\n\t\t\t\tlet userInfos = [];\r\n\t\t\t\tthis.members.forEach(m => {\r\n\t\t\t\t\tif (m.checked && !m.locked) {\r\n\t\t\t\t\t\tuserInfos.push({\r\n\t\t\t\t\t\t\tid: m.userId,\r\n\t\t\t\t\t\t\tnickName: m.showNickName,\r\n\t\t\t\t\t\t\theadImage: m.headImage,\r\n\t\t\t\t\t\t\tisCamera: false,\r\n\t\t\t\t\t\t\tisMicroPhone: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif(userInfos.length>0){\r\n\t\t\t\t\tthis.API.invite(this.groupId, userInfos);\r\n\t\t\t\t\t// 显示邀请的用户\r\n\t\t\t\t\tthis.$emit(\"ok\", userInfos);\r\n\t\t\t\t}\r\n\t\t\t\t// 关闭\r\n\t\t\t\tthis.isPopup = false;\r\n\t\t\t},\r\n\t\t\tisExist(userId) {\r\n\t\t\t\treturn !!this.userInfos.find(user => user.id == userId);\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcheckedSize() {\r\n\t\t\t\treturn this.members.filter(m => m.checked).length\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.van-popup {\r\n\t\tborder-radius: 20px 20px 0 0 !important;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.van-search {\r\n\t\t\tpadding: 6px 6px !important;\r\n\r\n\t\t\t.van-search__content {\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t\t\r\n\t\t\t\t.van-field__left-icon{\r\n\t\t\t\t\tline-height: 50px !important;\r\n\t\t\t\t}\r\n\t\t\t\t.van-field__control {\r\n\t\t\t\t\ttext-align: left !important;\r\n\t\t\t\t\tline-height: 50px !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.invite-member {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tcolor: white;\r\n\t\tbackground-color: black;\r\n\t\t\r\n\t\t.invite-btn {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\t.icon {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 80px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.inv-text {\r\n\t\t\t\tfont-size: 30px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.popup-content {\r\n\t\tposition: relative;\r\n\t\tborder: #dddddd solid 1px;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-color: white;\r\n\t\tpadding: 15px;\r\n\t\tcolor: black;\r\n\r\n\t\t\r\n\r\n\t\t.top-bar {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 100px;\r\n\t\t\tpadding: 10px 20px;\r\n\r\n\t\t\t.top-tip {\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t.top-btn {\r\n\t\t\t\tmargin-left: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.checked-users {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 90px;\r\n\t\t\toverflow-x: scroll;\r\n\t\t\tpadding: 0 15px;\r\n\r\n\t\t\t.user-item {\r\n\t\t\t\tpadding: 3px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.member-items {\r\n\t\t\tposition: relative;\r\n\t\t\tflex: 1;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.member-item {\r\n\t\t\t\theight: 120px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: white;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tpadding: 0 15px;\r\n\r\n\t\t\t\t.member-name {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tpadding-left: 20px;\r\n\t\t\t\t\tfont-size: 30px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tline-height: 60px;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tcolor: black;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.van-icon {\r\n\t\t\t\t\tborder-radius: 50% !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.scroll-bar {\r\n\t\t\t\theight: 800px;\r\n\t\t\t\toverflow-y: scroll;\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./InviteMember.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./InviteMember.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./InviteMember.vue?vue&type=template&id=5160d286\"\nimport script from \"./InviteMember.vue?vue&type=script&lang=js\"\nexport * from \"./InviteMember.vue?vue&type=script&lang=js\"\nimport style0 from \"./InviteMember.vue?vue&type=style&index=0&id=5160d286&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div class=\"chat-video\">\r\n\t\t<audio ref=\"callAudio\" loop=\"true\" x5-playsinline playsinline webkit-playsinline>\r\n\t\t\t<source src=\"@/assets/audio/call.wav\">\r\n\t\t</audio>\r\n\t\t<div class=\"call-box\" v-show=\"!isReady&&!isHost\">\r\n\t\t\t<div class=\"inv-avatar\" v-if=\"inviter\">\r\n\t\t\t\t<head-image :url=\"inviter.headImage\" :name=\"inviter.nickName\" :size=\"150\">\r\n\t\t\t\t\t<div class=\"inv-name\">{{inviter.nickName}}</div>\r\n\t\t\t\t</head-image>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"inv-text\">邀请你加入多人通话</div>\r\n\t\t\t<div class=\"user-list-text\">参与通话的还有:</div>\r\n\t\t\t<div class=\"user-list\">\r\n\t\t\t\t<div v-for=\"user in userInfos\" v-show=\"user.id!=inviterId\" :key=\"user.id\">\r\n\t\t\t\t\t<head-image :url=\"user.headImage\" :name=\"user.nickName\" :size=\"40\">\r\n\t\t\t\t\t</head-image>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"video-box\" v-show=\"isReady||isHost\">\r\n\t\t\t<div v-show=\"!isLeaderMode\" id=\"videos\" class=\"video-list-1\">\r\n\t\t\t\t<div :style=\"toVideoStyle(user)\" v-for=\"user in userInfos\" :key=\"user.id\" :id=\"'video'+user.id\"\r\n\t\t\t\t\t@click=\"onClickVideo(user)\">\r\n\t\t\t\t\t<local-video v-if=\"user.id==mine.id\" ref=\"localVideo\" :userInfo=\"mine\" :width=\"toVw(user)\"\r\n\t\t\t\t\t\t:height=\"toVh(user)\" @switchFacing=\"onSwitchFacing\"></local-video>\r\n\t\t\t\t\t<remote-video v-if=\"user.id!=mine.id\" :ref=\"'remoteVideo'+user.id\" :userInfo=\"user\"\r\n\t\t\t\t\t\t:groupId=\"groupId\" :width=\"toVw(user)\" :height=\"toVh(user)\"></remote-video>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div v-show=\"userInfos.length<config.maxChannel\" id=\"invite1\" :style=\"videoStyle\">\r\n\t\t\t\t\t<invite-member ref=\"invMember\" :API=\"API\" :groupId=\"groupId\" :userInfos=\"userInfos\"\r\n\t\t\t\t\t\t:maxChannel=\"config.maxChannel\" @ok=\"appendUser\"></invite-member>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isLeaderMode\" class=\"video-list-2\">\r\n\t\t\t\t<div id=\"leader\" class=\"leader\"></div>\r\n\r\n\t\t\t\t<div id=\"follower\" class=\"follower\">\r\n\t\t\t\t\t<div v-show=\"userInfos.length<config.maxChannel\" id=\"invite2\" :style=\"videoStyle\">\r\n\t\t\t\t\t\t<invite-member ref=\"invMember\" :API=\"API\" :groupId=\"groupId\" :userInfos=\"userInfos\"\r\n\t\t\t\t\t\t\t:maxChannel=\"config.maxChannel\" @ok=\"appendUser\"></invite-member>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"control-bar\" ref=\"refCtrl\" v-show=\"isReady||isHost\">\r\n\t\t\t<div class=\"chat-time\">{{chatTimeString}}</div>\r\n\t\t\t<div class=\"dev-bar\">\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && isMicroPhone\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-microphone-on icon-front\" @click=\"onSwitchMicroPhone()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">麦克风已开</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && !isMicroPhone\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-microphone-off icon-back\" @click=\"onSwitchMicroPhone()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">麦克风已关</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && isSpeaker\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-speaker-on icon-front\" @click=\"onSwitchSpeaker()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">扬声器已开</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && !isSpeaker\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-speaker-off icon-back\" @click=\"onSwitchSpeaker()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">扬声器已关</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && isCamera\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-camera-on icon-front\" @click=\"onSwitchCamera()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">摄像头已开</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"icon-box\" v-show=\"isReady && !isCamera\">\r\n\t\t\t\t\t<div class=\"icon iconfont icon-camera-off icon-back\" @click=\"onSwitchCamera()\"></div>\r\n\t\t\t\t\t<div class=\"icon-text\">摄像头已关</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"bottom-bar\" ref=\"refBot\">\r\n\t\t\t<div v-show=\"!isHost && !isChating\" class=\"icon iconfont icon-phone-reject red\" @click=\"onReject()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"!isHost && !isChating\" class=\"icon iconfont icon-phone-accept \" @click=\"onAccept()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isHost && !isChating\" class=\"icon iconfont icon-phone-reject red\" @click=\"onCancel()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isChating\" class=\"icon iconfont icon-phone-reject red\" @click=\"onQuit()\">\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ImWebRtc from '@/common/webrtc'\r\n\timport ImCamera from '@/common/camera'\r\n\timport ImApi from '@/common/api'\r\n\timport UniEvent from '@/common/uniEvent'\r\n\timport HeadImage from '@/components/HeadImage'\r\n\timport LocalVideo from '@/components/LocalVideo'\r\n\timport RemoteVideo from '@/components/RemoteVideo'\r\n\timport InviteMember from '@/components/InviteMember'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tenv: {},\r\n\t\t\t\tcamera: new ImCamera(), // 摄像头和麦克风\r\n\t\t\t\twebrtc: new ImWebRtc(), // webrtc相关\r\n\t\t\t\tuniEvent: new UniEvent(), // 接受来自uniapp的消息\r\n\t\t\t\tAPI: null, // API接口请求对象\r\n\t\t\t\tstream: null, // 本地视频流\r\n\t\t\t\tisMicroPhone: true, // 是否开启麦克风\r\n\t\t\t\tisFacing: true, // 是否前置摄像头\r\n\t\t\t\tisSpeaker: true, // 是否开启扬声器\r\n\t\t\t\tisCamera: false, // 是否开启视频\r\n\t\t\t\tchatTime: 0, // 聊天时长\r\n\t\t\t\tchatTimer: null, // 聊天计时器\r\n\t\t\t\theartbeatTimer: null, // 心跳定时器\r\n\t\t\t\ttipTimer: null, // 提示语定时器\r\n\t\t\t\tlastTipTime: null, // 上一次提示时间\r\n\t\t\t\tstate: \"INIT\", // INIT:初始状态  WAITING:等待呼叫或接听 CHATING:聊天中  CLOSE:关闭 ERROR:出现异常\r\n\t\t\t\tbaseUrl: \"\", // 服务器基础接口路径\r\n\t\t\t\tloginInfo: {}, // token信息\r\n\t\t\t\tgroupId: null, // 群id\r\n\t\t\t\tuserId: null, // 当前用户id\r\n\t\t\t\tinviterId: null, // 发起邀请的用户id\r\n\t\t\t\tleaderId: null, // leader id (大屏显示)\r\n\t\t\t\tisHost: false, // \r\n\t\t\t\tuserInfos: [], // 用户列表，\r\n\t\t\t\tconfig: {}, // webrtc配置\r\n\t\t\t\tvw: 150, // 视频窗口宽度\r\n\t\t\t\tvh: 150, // 视频窗口高度\r\n\t\t\t\tleaderVw: 600, // leader窗口宽度\r\n\t\t\t\tleaderVh: 600, // leader窗口高度\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tHeadImage,\r\n\t\t\tLocalVideo,\r\n\t\t\tRemoteVideo,\r\n\t\t\tInviteMember\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonNavBack() {\r\n\t\t\t\t// 用户按下了左上角回退键，强制退出\r\n\t\t\t\tif (this.isClose) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isHost && !this.isChating) {\r\n\t\t\t\t\t// 强制终止呼叫\r\n\t\t\t\t\tconsole.log(\"强制终止呼叫\");\r\n\t\t\t\t\tthis.API.cancel(this.groupId);\r\n\t\t\t\t} else if (!this.isHost && this.state == \"WAITING\") {\r\n\t\t\t\t\t// 强制拒绝接听\r\n\t\t\t\t\tconsole.log(\"强制拒绝接听\");\r\n\t\t\t\t\tthis.API.reject(this.groupId);\r\n\t\t\t\t} else if (this.isChating) {\r\n\t\t\t\t\t// 强制退出通话\r\n\t\t\t\t\tconsole.log(\"强制退出通话\");\r\n\t\t\t\t\tthis.API.quit(this.groupId);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonClickVideo(userInfo) {\r\n\t\t\t\tconsole.log(\"onClickVideo\")\r\n\t\t\t\tif (userInfo.id == this.leaderId) {\r\n\t\t\t\t\t// 退出leader模式\r\n\t\t\t\t\tthis.leaderId = null;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 进入leader模式\r\n\t\t\t\t\tthis.leaderId = userInfo.id;\r\n\t\t\t\t}\r\n\t\t\t\tthis.refreshVideoWH();\r\n\t\t\t\tthis.reLayoutVideo();\r\n\t\t\t},\r\n\t\t\tonSwitchMicroPhone() {\r\n\t\t\t\tthis.isMicroPhone = !this.isMicroPhone;\r\n\t\t\t\tthis.$refs.localVideo[0].setMicroPhone(this.isMicroPhone);\r\n\t\t\t\t// 通知其他人我开/关了麦克风\r\n\t\t\t\tthis.API.device(this.groupId, this.isCamera, this.isMicroPhone);\r\n\t\t\t\tconsole.log(\"麦克风:\" + this.isMicroPhone)\r\n\t\t\t},\r\n\t\t\tonSwitchFacing(isFacing) {\r\n\t\t\t\tthis.isFacing = isFacing;\r\n\t\t\t\tthis.openStream().then(() => {\r\n\t\t\t\t\t// 保持麦克风状态\r\n\t\t\t\t\tthis.$refs.localVideo[0].setMicroPhone(this.isMicroPhone);\r\n\t\t\t\t\t// 切换传输的视频流\r\n\t\t\t\t\tthis.userInfos.forEach((user) => {\r\n\t\t\t\t\t\tif (user.id != this.mine.id) {\r\n\t\t\t\t\t\t\tconst refName = 'remoteVideo' + user.id;\r\n\t\t\t\t\t\t\tthis.$refs[refName][0].switchStream(this.stream);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconst cameaName = isFacing ? \"前置\" : \"后置\"\r\n\t\t\t\t\tconsole.log(\"摄像头翻转:\" + cameaName);\r\n\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\tthis.showToast(\"摄像头翻转失败\")\r\n\t\t\t\t\tconsole.log(\"摄像头翻转失败:\" + e.message)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonSwitchCamera() {\r\n\t\t\t\tthis.isCamera = !this.isCamera;\r\n\t\t\t\tthis.mine.isCamera = this.isCamera;\r\n\t\t\t\t// 重新打开设备\r\n\t\t\t\tthis.openStream().then(() => {\r\n\t\t\t\t\t// 切换传输的视频流\r\n\t\t\t\t\tthis.userInfos.forEach((user) => {\r\n\t\t\t\t\t\tif (user.id != this.mine.id) {\r\n\t\t\t\t\t\t\tconst refName = 'remoteVideo' + user.id;\r\n\t\t\t\t\t\t\t// 开关摄像头需要重新连接\r\n\t\t\t\t\t\t\tthis.$refs[refName][0].reconnect(this.stream);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 通知其他人我开/关了摄像头\r\n\t\t\t\t\tthis.API.device(this.groupId, this.isCamera, this.isMicroPhone);\r\n\t\t\t\t\tconsole.log(\"摄像头:\" + this.isCamera);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonSwitchSpeaker() {\r\n\t\t\t\tthis.isSpeaker = !this.isSpeaker;\r\n\t\t\t\t// 切换传输的视频流\r\n\t\t\t\tthis.userInfos.forEach((user) => {\r\n\t\t\t\t\tif (user.id != this.mine.id) {\r\n\t\t\t\t\t\tconst refName = 'remoteVideo' + user.id;\r\n\t\t\t\t\t\tthis.$refs[refName][0].setMute(!this.isSpeaker);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(\"扬声器切换:\" + this.isSpeaker);\r\n\t\t\t},\r\n\t\t\tonRTCMessage(msg) {\r\n\t\t\t\t// 除了发起通话，如果在关闭状态就无需处理\r\n\t\t\t\tif (msg.type != this.$enums.MESSAGE_TYPE.RTC_GROUP_SETUP &&\r\n\t\t\t\t\tthis.state == 'CLOSE') {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// RTC信令处理\r\n\t\t\t\tswitch (msg.type) {\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_SETUP:\r\n\t\t\t\t\t\tthis.onRTCSetup(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_ACCEPT:\r\n\t\t\t\t\t\tthis.onRTCAccept(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_REJECT:\r\n\t\t\t\t\t\tthis.onRTCReject(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_JOIN:\r\n\t\t\t\t\t\tthis.onRTCJoin(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_FAILED:\r\n\t\t\t\t\t\tthis.onRTCFailed(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_CANCEL:\r\n\t\t\t\t\t\tthis.onRTCCancel(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_QUIT:\r\n\t\t\t\t\t\tthis.onRTCQuit(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_INVITE:\r\n\t\t\t\t\t\tthis.onRTCInvite(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_OFFER:\r\n\t\t\t\t\t\tthis.onRTCOffer(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_ANSWER:\r\n\t\t\t\t\t\tthis.onRTCAnswer(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_CANDIDATE:\r\n\t\t\t\t\t\tthis.onRTCCandidate(msg)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_GROUP_DEVICE:\r\n\t\t\t\t\t\tthis.onRTCDevice(msg)\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonRTCSetup(msg) {\r\n\t\t\t\t// 呼叫铃声\r\n\t\t\t\tthis.$refs.callAudio.play();\r\n\t\t\t},\r\n\t\t\tonRTCAccept(msg) {\r\n\t\t\t\tif (msg.selfSend) {\r\n\t\t\t\t\t// 我在其他终端接受了的通话\r\n\t\t\t\t\tthis.close(\"已在其他设备接听\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tif (this.isChating) {\r\n\t\t\t\t\t// 与用户发起视频连接\r\n\t\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\t\tthis.$refs[refName][0].connect();\r\n\t\t\t\t} else if (this.isHost) {\r\n\t\t\t\t\t// 与用户发起视频连接\r\n\t\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\t\tthis.$refs[refName][0].connect();\r\n\t\t\t\t\t// 只有有人进来就进入聊天状态\r\n\t\t\t\t\tthis.state = 'CHATING';\r\n\t\t\t\t\t// 停止呼叫铃声\r\n\t\t\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t\t\t// 开始计时\r\n\t\t\t\t\tthis.startChatTime();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonRTCReject(msg) {\r\n\t\t\t\tif (msg.selfSend) {\r\n\t\t\t\t\t// 我在其他终端拒绝了的通话\r\n\t\t\t\t\tthis.close(\"已在其他设备拒绝\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 有人拒绝了通话\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\t// 移除用户\r\n\t\t\t\tthis.removeUser(remoteUserId, \"未进入通话\")\r\n\t\t\t},\r\n\t\t\tonRTCFailed(msg) {\r\n\t\t\t\t// 有人无法加入通话\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tconst failedInfo = JSON.parse(msg.content);\r\n\t\t\t\tif (failedInfo.userIds.find(userId => userId == this.mine.id)) {\r\n\t\t\t\t\tthis.close(\"您未接听\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet firUserId = failedInfo.userIds[0];\r\n\t\t\t\tlet firUserName = this.userInfos.find(u => u.id == firUserId).nickName;\r\n\t\t\t\tlet tip = `'${firUserName}'`\r\n\t\t\t\tif (failedInfo.userIds.length > 1) {\r\n\t\t\t\t\ttip += `等${failedInfo.userIds.length}人`;\r\n\t\t\t\t}\r\n\t\t\t\ttip += `未能进入通话,原因:${failedInfo.reason}`;\r\n\t\t\t\tthis.showToast(tip)\r\n\t\t\t\t// 移除用户\r\n\t\t\t\tfailedInfo.userIds.forEach((userId) => this.removeUser(userId));\r\n\t\t\t},\r\n\t\t\tonRTCJoin(msg) {\r\n\t\t\t\t// 有用户进入了通话\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tlet userInfo = JSON.parse(msg.content);\r\n\t\t\t\tif (!this.isExist(remoteUserId)) {\r\n\t\t\t\t\tthis.userInfos.push(userInfo);\r\n\t\t\t\t}\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t// 初始化视频窗口\r\n\t\t\t\t\tthis.initUserVideo();\r\n\t\t\t\t\t// 与用户发起视频连接\r\n\t\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\t\tthis.$refs[refName][0].connect();\r\n\t\t\t\t})\r\n\t\t\t\t// 只有有人进来就进入聊天状态\r\n\t\t\t\tthis.state = 'CHATING';\r\n\t\t\t\t// 停止呼叫铃声\r\n\t\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t\t// 开始计时\r\n\t\t\t\tthis.startChatTime();\r\n\t\t\t},\r\n\t\t\tonRTCOffer(msg) {\r\n\t\t\t\t// 有用户向往我发起视频连接\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tconst offer = JSON.parse(msg.content)\r\n\t\t\t\t// 回复对方\r\n\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\tthis.$refs[refName][0].onOffer(offer);\r\n\t\t\t},\r\n\t\t\tonRTCAnswer(msg) {\r\n\t\t\t\t// 对方同意了我的视频连接请求\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tconst answer = JSON.parse(msg.content);\r\n\t\t\t\t// 设置answer信息\r\n\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\tthis.$refs[refName][0].onAnswer(answer);\r\n\t\t\t},\r\n\t\t\tonRTCCancel() {\r\n\t\t\t\t// 通话取消，直接退出\r\n\t\t\t\tthis.close(\"通话已取消\");\r\n\t\t\t},\r\n\t\t\tonRTCQuit(msg) {\r\n\t\t\t\t// 有人退出了通话\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\t// 移除该用户\r\n\t\t\t\tthis.removeUser(remoteUserId, \"已退出通话\");\r\n\t\t\t},\r\n\t\t\tonRTCInvite(msg) {\r\n\t\t\t\t// 有用户被邀请进来\r\n\t\t\t\tlet userInfos = JSON.parse(msg.content);\r\n\t\t\t\t// 加入列表\r\n\t\t\t\tthis.appendUser(userInfos);\r\n\t\t\t},\r\n\t\t\tonRTCCandidate(msg) {\r\n\t\t\t\t// 对方同意了我的视频连接请求\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tconst candidate = JSON.parse(msg.content);\r\n\t\t\t\t// 设置answer信息\r\n\t\t\t\tconst refName = 'remoteVideo' + remoteUserId;\r\n\t\t\t\tthis.$refs[refName][0].setCandidate(candidate);\r\n\t\t\t},\r\n\t\t\tonRTCDevice(msg) {\r\n\t\t\t\t// 对方进行了设备操作\r\n\t\t\t\tconst remoteUserId = msg.sendId;\r\n\t\t\t\tconst devInfo = JSON.parse(msg.content);\r\n\t\t\t\t// 记录摄像头/麦克风开关标志\r\n\t\t\t\tlet userInfo = this.userInfos.find(u => u.id == remoteUserId);\r\n\t\t\t\tuserInfo.isCamera = devInfo.isCamera;\r\n\t\t\t\tuserInfo.isMicroPhone = devInfo.isMicroPhone;\r\n\t\t\t},\r\n\t\t\tonAccept() {\r\n\t\t\t\tif (!this.checkDevEnable()) {\r\n\t\t\t\t\tthis.API.failed(this.groupId, \"设备不支持通话\")\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 进入房间\r\n\t\t\t\tthis.state = \"CHATING\";\r\n\t\t\t\t// 停止呼叫铃声\r\n\t\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t\t// 刷新窗口大小\r\n\t\t\t\tthis.refreshVideoWH();\r\n\t\t\t\t// 打开摄像头\r\n\t\t\t\tthis.openStream().finally(() => {\r\n\t\t\t\t\t// 初始化视频窗口\r\n\t\t\t\t\tthis.initUserVideo();\r\n\t\t\t\t\t// 加入通话\r\n\t\t\t\t\tthis.API.accept(this.groupId).then(() => {\r\n\t\t\t\t\t\t// 聊天时长\r\n\t\t\t\t\t\tthis.startChatTime();\r\n\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\tthis.close();\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonReject() {\r\n\t\t\t\t// 退出通话\r\n\t\t\t\tthis.API.reject(this.groupId);\r\n\t\t\t\t// 退出\r\n\t\t\t\tthis.close(\"您拒绝了通话\");\r\n\t\t\t},\r\n\t\t\tonQuit() {\r\n\t\t\t\t// 退出通话\r\n\t\t\t\tthis.API.quit(this.groupId);\r\n\t\t\t\t// 退出\r\n\t\t\t\tthis.close(\"退出通话\");\r\n\t\t\t},\r\n\t\t\tonCancel() {\r\n\t\t\t\t// 取消通话\r\n\t\t\t\tthis.API.cancel(this.groupId);\r\n\t\t\t\t// 退出\r\n\t\t\t\tthis.close(\"您取消通话\");\r\n\t\t\t},\r\n\t\t\tonSetup() {\r\n\t\t\t\tif (!this.checkDevEnable()) {\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t}\r\n\t\t\t\t// 打开摄像头\r\n\t\t\t\tthis.openStream().finally(() => {\r\n\t\t\t\t\t// 不管是否成功都进行初始化\r\n\t\t\t\t\tthis.initUserVideo()\r\n\t\t\t\t\t// 发起呼叫\r\n\t\t\t\t\tthis.API.setup(this.groupId, this.userInfos).then(() => {\r\n\t\t\t\t\t\t// 准备状态\r\n\t\t\t\t\t\tthis.state = \"READY\";\r\n\t\t\t\t\t\t// 播放呼叫\r\n\t\t\t\t\t\tthis.$refs.callAudio.play();\r\n\t\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\tthis.close();\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonJoin() {\r\n\t\t\t\tif (!this.checkDevEnable()) {\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t}\r\n\t\t\t\t// 就绪\r\n\t\t\t\tthis.state = \"READY\";\r\n\t\t\t\t// 刷新窗口大小\r\n\t\t\t\tthis.refreshVideoWH();\r\n\t\t\t\t// 打开摄像头\r\n\t\t\t\tthis.openStream().finally(() => {\r\n\t\t\t\t\t// 不管是否成功都进行初始化\r\n\t\t\t\t\tthis.initUserVideo()\r\n\t\t\t\t\t// 发起加入请求\r\n\t\t\t\t\tthis.API.join(this.groupId).then(() => {\r\n\t\t\t\t\t\t// 聊天状态\r\n\t\t\t\t\t\tthis.state = \"CHATING\";\r\n\t\t\t\t\t\t// 开始计时\r\n\t\t\t\t\t\tthis.startChatTime();\r\n\t\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\tthis.close();\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonInviteMember() {\r\n\t\t\t\tthis.$refs.invMember.open();\r\n\t\t\t},\r\n\t\t\tappendUser(userInfos) {\r\n\t\t\t\tuserInfos.forEach(user => {\r\n\t\t\t\t\tif (!this.isExist(user.id)) {\r\n\t\t\t\t\t\tthis.userInfos.push(user);\r\n\t\t\t\t\t\tconsole.log(`'${user.nickName}'加入通话`);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// 初始化视频窗口\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.initUserVideo();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tisExist(userId) {\r\n\t\t\t\treturn !!this.userInfos.find(u => u.id == userId);\r\n\t\t\t},\r\n\t\t\tremoveUser(userId, tip) {\r\n\t\t\t\tif (!this.isExist(userId)) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 销毁资源\r\n\t\t\t\tconst refName = 'remoteVideo' + userId;\r\n\t\t\t\tif (this.$refs[refName]) {\r\n\t\t\t\t\tthis.$refs[refName][0].close();\r\n\t\t\t\t}\r\n\t\t\t\t// 提示语\r\n\t\t\t\tconst idx = this.userInfos.findIndex(user => user.id == userId);\r\n\t\t\t\tconst userInfo = this.userInfos[idx];\r\n\t\t\t\t// 移除用户\r\n\t\t\t\tthis.userInfos.splice(idx, 1);\r\n\t\t\t\tif (this.isHost && tip) {\r\n\t\t\t\t\tthis.showToast(`'${userInfo.nickName}'${tip}`);\r\n\t\t\t\t}\r\n\t\t\t\t// 如果只剩下自己了，则自己也退出\r\n\t\t\t\tif (this.userInfos.length <= 1) {\r\n\t\t\t\t\tthis.onQuit();\r\n\t\t\t\t}\r\n\t\t\t\t// leader退出，恢复到正常模式\r\n\t\t\t\tif (this.isLeader(userInfo)) {\r\n\t\t\t\t\tthis.leaderId = null;\r\n\t\t\t\t\tthis.refreshVideoWH();\r\n\t\t\t\t\tthis.reLayoutVideo();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topenStream() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tif (this.isCamera) {\r\n\t\t\t\t\t\t// 打开摄像头+麦克风\r\n\t\t\t\t\t\tthis.camera.openVideo(this.isFacing).then((stream) => {\r\n\t\t\t\t\t\t\tconsole.log(\"摄像头打开成功\")\r\n\t\t\t\t\t\t\tthis.stream = stream;\r\n\t\t\t\t\t\t\t// 显示本地视频\r\n\t\t\t\t\t\t\tthis.$refs.localVideo[0].open(stream);\r\n\t\t\t\t\t\t\tresolve(stream);\r\n\t\t\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\t\tthis.showToast(e.message)\r\n\t\t\t\t\t\t\tconsole.log(\"本地摄像头打开失败:\" + e.message)\r\n\t\t\t\t\t\t\treject(e);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 打开麦克风\r\n\t\t\t\t\t\tthis.camera.openAudio().then((stream) => {\r\n\t\t\t\t\t\t\tconsole.log(\"麦克风打开成功\")\r\n\t\t\t\t\t\t\tthis.stream = stream;\r\n\t\t\t\t\t\t\t// 显示本地视频\r\n\t\t\t\t\t\t\tthis.$refs.localVideo[0].open(stream);\r\n\t\t\t\t\t\t\tresolve(stream);\r\n\t\t\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\t\t\tthis.showToast(e.message)\r\n\t\t\t\t\t\t\tconsole.log(\"本地麦克风打开失败:\" + e.message)\r\n\t\t\t\t\t\t\treject(e);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinitUserVideo() {\r\n\t\t\t\t// 初始化视频窗口\r\n\t\t\t\tthis.userInfos.forEach(user => {\r\n\t\t\t\t\tif (user.id != this.userId) {\r\n\t\t\t\t\t\tconst refName = 'remoteVideo' + user.id\r\n\t\t\t\t\t\t// 防止重复初始化\r\n\t\t\t\t\t\tif (!this.$refs[refName][0].isInit) {\r\n\t\t\t\t\t\t\tconst configuration = {\r\n\t\t\t\t\t\t\t\ticeServers: this.config.iceServers\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthis.$refs[refName][0].init(this.API, configuration, this.stream)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstartChatTime() {\r\n\t\t\t\tif (!this.chatTimer) {\r\n\t\t\t\t\tthis.chatTime = 0;\r\n\t\t\t\t\tthis.chatTimer = setInterval(() => {\r\n\t\t\t\t\t\tthis.chatTime++;\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tstartHeartBeat() {\r\n\t\t\t\t// 每15s推送一次心跳\r\n\t\t\t\tthis.heartbeatTimer && clearInterval(this.heartbeatTimer);\r\n\t\t\t\tthis.heartbeatTimer = setInterval(() => {\r\n\t\t\t\t\tthis.API.heartbeat(this.groupId);\r\n\t\t\t\t}, 15000)\r\n\t\t\t},\r\n\t\t\tcheckDevEnable() {\r\n\t\t\t\t// 检测摄像头\r\n\t\t\t\tif (!this.camera.isEnable()) {\r\n\t\t\t\t\tthis.showToast(\"访问摄像头失败\");\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t// 检测webrtc\r\n\t\t\t\tif (!this.webrtc.isEnable()) {\r\n\t\t\t\t\tthis.showToast(\"初始化RTC失败，原因可能是: 1.服务器缺少ssl证书 2.您的设备不支持WebRTC\");\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\t\t\tshowToast(tip) {\r\n\t\t\t\tif (!this.lastTipTime || new Date().getTime() - this.lastTipTime > 1000) {\r\n\t\t\t\t\tthis.$toast(tip);\r\n\t\t\t\t\tthis.lastTipTime = new Date().getTime();\r\n\t\t\t\t\tconsole.log(tip);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 当前已有提示，1s之后再弹出\r\n\t\t\t\t\tsetTimeout(() => this.showToast(tip), 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treLayoutVideo() {\r\n\t\t\t\t// 移动视频窗口\r\n\t\t\t\tconsole.log(\"reLayoutVideo\")\r\n\t\t\t\tconst leader = document.getElementById(\"leader\");\r\n\t\t\t\tconst invite1 = document.getElementById(\"invite1\");\r\n\t\t\t\tconst invite2 = document.getElementById(\"invite2\");\r\n\t\t\t\tthis.userInfos.forEach((userInfo) => {\r\n\t\t\t\t\tconst id = 'video' + userInfo.id;\r\n\t\t\t\t\tconst video = document.getElementById(id);\r\n\t\t\t\t\tif (!this.isLeaderMode) {\r\n\t\t\t\t\t\tinvite1.before(video)\r\n\t\t\t\t\t} else if (this.leaderId == userInfo.id) {\r\n\t\t\t\t\t\tleader.appendChild(video);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tinvite2.before(video);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\trefreshVideoWH() {\r\n\t\t\t\tlet w = window.innerWidth;\r\n\t\t\t\tlet h = window.innerHeight;\r\n\t\t\t\tconsole.log(\"屏幕大小\", w, h)\r\n\t\t\t\tlet count = this.userInfos.length;\r\n\t\t\t\tif (!w || !h || !count) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 减去底下操作栏空间\r\n\t\t\t\th = h - 520 * w / 750;\r\n\t\t\t\t// 加上邀请按钮的数量\r\n\t\t\t\tif (count < this.config.maxChannel) {\r\n\t\t\t\t\tcount = count + 1;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isLeaderMode) {\r\n\t\t\t\t\tthis.vw = w / 3;\r\n\t\t\t\t\tthis.vh = w / 3;\r\n\t\t\t\t\tthis.leaderVw = w;\r\n\t\t\t\t\tthis.leaderVh = h - this.vh;\r\n\t\t\t\t\t// 长宽比不能超过1:1.2\r\n\t\t\t\t\tif (this.leaderVh > w * 1.2) {\r\n\t\t\t\t\t\tthis.leaderVh = w * 1.2;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 单行视频数量\r\n\t\t\t\t\tlet row = Math.ceil(Math.sqrt(count));\r\n\t\t\t\t\tlet col = Math.ceil(count / row);\r\n\t\t\t\t\tthis.vw = w / row;\r\n\t\t\t\t\tthis.vh = h / col;\r\n\t\t\t\t\t// 长宽比不能超过1:1.2\r\n\t\t\t\t\tif (this.vh > this.vw * 1.2) {\r\n\t\t\t\t\t\tthis.vh = this.vw * 1.2\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(\"视频大小\", this.vw, this.vh)\r\n\t\t\t},\r\n\t\t\ttoVw(userInfo) {\r\n\t\t\t\treturn this.isLeader(userInfo) ? this.leaderVw : this.vw;\r\n\t\t\t},\r\n\t\t\ttoVh(userInfo) {\r\n\t\t\t\treturn this.isLeader(userInfo) ? this.leaderVh : this.vh;\r\n\t\t\t},\r\n\t\t\ttoVideoStyle(userInfo) {\r\n\t\t\t\treturn this.isLeader(userInfo) ? this.leaderStyle : this.videoStyle;\r\n\t\t\t},\r\n\t\t\tisLeader(userInfo) {\r\n\t\t\t\treturn this.leaderId == userInfo.id;\r\n\t\t\t},\r\n\t\t\tinitEvent() {\r\n\t\t\t\tthis.uniEvent.listen((key, data) => {\r\n\t\t\t\t\tconsole.log(\"来自app的消息：\" + key + \":\" + JSON.stringify(data));\r\n\t\t\t\t\tif (key == \"RTC_MESSAGE\") {\r\n\t\t\t\t\t\t// RTC信令\r\n\t\t\t\t\t\tthis.onRTCMessage(data);\r\n\t\t\t\t\t} else if (key == \"NAV_BACK\") {\r\n\t\t\t\t\t\tthis.onNavBack();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinit() {\r\n\t\t\t\t// 创建API对象\r\n\t\t\t\tthis.API = new ImApi(this.baseUrl, this.loginInfo);\r\n\t\t\t\t// 等待用户接受呼叫\r\n\t\t\t\tthis.state = \"WAITING\";\r\n\t\t\t\t// 如果我是发起人，发起群通话\r\n\t\t\t\tif (this.isHost) {\r\n\t\t\t\t\tthis.onSetup();\r\n\t\t\t\t} else if (this.inviterId == this.userId) {\r\n\t\t\t\t\t// 自己邀请自己，表示自己主动加入通话\r\n\t\t\t\t\tthis.onJoin();\r\n\t\t\t\t}\r\n\t\t\t\t// 启动心跳\r\n\t\t\t\tthis.startHeartBeat();\r\n\t\t\t},\r\n\r\n\t\t\tclose(tip) {\r\n\t\t\t\t// 弹出关闭提示语\r\n\t\t\t\ttip && this.showToast(tip);\r\n\t\t\t\t// 清理定时器\r\n\t\t\t\tthis.chatTimer && clearInterval(this.chatTimer);\r\n\t\t\t\tthis.heartbeatTimer && clearInterval(this.heartbeatTimer);\r\n\t\t\t\t// 返回APP,延迟2000ms，让用户看到相关提示信息\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 关闭状态\r\n\t\t\t\t\tthis.state = \"CLOSE\";\r\n\t\t\t\t\t// 关闭摄像头等资源\r\n\t\t\t\t\tthis.camera.close();\r\n\t\t\t\t\t// 手动推消息到uniapp端进行关闭\r\n\t\t\t\t\twindow.uni.postMessage({\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tkey: \"WV_CLOSE\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 2000)\r\n\t\t\t\t// 停止呼叫语音\r\n\t\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tmine() {\r\n\t\t\t\treturn this.userInfos.find(user => user.id == this.userId);\r\n\t\t\t},\r\n\t\t\tinviter() {\r\n\t\t\t\treturn this.userInfos.find(user => user.id == this.inviterId);\r\n\t\t\t},\r\n\t\t\tisChating() {\r\n\t\t\t\treturn this.state == \"CHATING\";\r\n\t\t\t},\r\n\t\t\tisReady() {\r\n\t\t\t\treturn this.state == \"CHATING\" || this.state == \"READY\";\r\n\t\t\t},\r\n\t\t\tisClose() {\r\n\t\t\t\treturn this.state == \"CLOSE\";\r\n\t\t\t},\r\n\t\t\tisWaiting() {\r\n\t\t\t\treturn this.state == \"WAITING\";\r\n\t\t\t},\r\n\t\t\tisLeaderMode() {\r\n\t\t\t\treturn !!this.leaderId;\r\n\t\t\t},\r\n\t\t\tvideoStyle() {\r\n\t\t\t\treturn `width:${this.vw}px;height:${this.vh}px;`\r\n\t\t\t},\r\n\t\t\tleaderStyle() {\r\n\t\t\t\treturn `width:${this.leaderVw}px;height:${this.leaderVh}px;`\r\n\t\t\t},\r\n\t\t\tuserCount() {\r\n\t\t\t\treturn this.userInfos.length;\r\n\t\t\t},\r\n\t\t\tchatTimeString() {\r\n\t\t\t\tif (!this.isChating) {\r\n\t\t\t\t\treturn \"\";\r\n\t\t\t\t}\r\n\t\t\t\tlet min = Math.floor(this.chatTime / 60);\r\n\t\t\t\tlet sec = this.chatTime % 60;\r\n\t\t\t\tlet strTime = min < 10 ? \"0\" : \"\";\r\n\t\t\t\tstrTime += min;\r\n\t\t\t\tstrTime += \":\";\r\n\t\t\t\tstrTime += sec < 10 ? \"0\" : \"\";\r\n\t\t\t\tstrTime += sec;\r\n\t\t\t\treturn strTime;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tuserCount: {\r\n\t\t\t\thandler(newCount, oldCount) {\r\n\t\t\t\t\tthis.refreshVideoWH();\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.reLayoutVideo();\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 从url中解析带过来的参数\r\n\t\t\tconst url = new URL(window.location.href);\r\n\t\t\tthis.baseUrl = url.searchParams.get(\"baseUrl\")\r\n\t\t\tthis.loginInfo = JSON.parse(url.searchParams.get(\"loginInfo\"));\r\n\t\t\tthis.inviterId = url.searchParams.get(\"inviterId\");\r\n\t\t\tthis.userId = url.searchParams.get(\"userId\");\r\n\t\t\tthis.isHost = JSON.parse(url.searchParams.get(\"isHost\"));\r\n\t\t\tthis.groupId = url.searchParams.get(\"groupId\")\r\n\t\t\tthis.userInfos = JSON.parse(url.searchParams.get(\"userInfos\"));\r\n\t\t\tthis.config = JSON.parse(url.searchParams.get(\"config\"));\r\n\t\t\t// 待触发 `UniAppJSBridgeReady` 事件后，即可调用 uni 的 API。\r\n\t\t\tdocument.addEventListener('UniAppJSBridgeReady', () => {\r\n\t\t\t\t// 初始化工作\r\n\t\t\t\tthis.init();\r\n\t\t\t\t// 初始化来自uniapp的事件\r\n\t\t\t\tthis.initEvent();\r\n\t\t\t\t// 判断当前环境\r\n\t\t\t\twindow.uni.getEnv((env) => {\r\n\t\t\t\t\tconsole.log('当前环境：' + JSON.stringify(env));\r\n\t\t\t\t\tthis.env = env;\r\n\t\t\t\t});\r\n\t\t\t\t// 通知APP，web-view 已就绪\r\n\t\t\t\twindow.uni.postMessage({\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tkey: \"WV_READY\"\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.chat-video {\r\n\t\tposition: fixed;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-size: 100% 100%;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #333;\r\n\r\n\t\t.call-box {\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.inv-avatar {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-around;\r\n\t\t\t\tpadding-top: 150px;\r\n\r\n\t\t\t\t.inv-name {\r\n\t\t\t\t\tmargin-top: 10px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 50px;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.inv-text {\r\n\t\t\t\tmargin-top: 20px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 28px;\r\n\t\t\t\tcolor: #cfcfcf;\r\n\t\t\t}\r\n\r\n\t\t\t.user-list-text {\r\n\t\t\t\tmargin-top: 100px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 26px;\r\n\t\t\t\tcolor: #cfcfcf;\r\n\t\t\t}\r\n\r\n\t\t\t.user-list {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding: 10px 50px;\r\n\t\t\t\theight: 180px;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.video-box {\r\n\t\t\tflex: 1;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.video-list-1 {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\r\n\t\t\t.video-list-2 {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t.leader {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.follower {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\toverflow-x: scroll;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\tdiv {\r\n\t\t\t\t\t\tflex-shrink: 0\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.control-bar {\r\n\t\t\theight: 280px;\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.chat-time {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: white;\r\n\t\t\t\tline-height: 100px;\r\n\t\t\t\theight: 100px;\r\n\t\t\t}\r\n\r\n\t\t\t.dev-bar {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-around;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 0 40px;\r\n\r\n\t\t\t\t.icon-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\r\n\t\t\t\t\t.icon {\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tpadding: 20px;\r\n\t\t\t\t\t\tfont-size: 60px;\r\n\t\t\t\t\t\twidth: 100px;\r\n\t\t\t\t\t\theight: 100px;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.icon-front {\r\n\t\t\t\t\t\tcolor: black;\r\n\t\t\t\t\t\tbackground-color: white;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.icon-back {\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tbackground-color: black;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.icon-text {\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tfont-size: 30px;\r\n\t\t\t\t\t\tmargin-top: 10px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.bottom-bar {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\talign-items: center;\r\n\t\t\theight: 240px;\r\n\t\t\tpadding: 0 40px;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tcolor: white;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tfont-size: 100px;\r\n\t\t\t\tbackground-color: #1ba609;\r\n\r\n\t\t\t\t&.red {\r\n\t\t\t\t\tbackground-color: #e61d1d;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChatVideo.vue?vue&type=template&id=522107f0\"\nimport script from \"./ChatVideo.vue?vue&type=script&lang=js\"\nexport * from \"./ChatVideo.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChatVideo.vue?vue&type=style&index=0&id=522107f0&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div id=\"app\">\r\n    <chat-video></chat-video>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ChatVideo from \"./view/ChatVideo.vue\"\r\n\r\nexport default {\r\n  name: 'App',\r\n  components:{\r\n\t  ChatVideo\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t#app{\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=58d4d68d\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=58d4d68d&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "\r\nconst MESSAGE_TYPE = {\r\n\tRTC_GROUP_SETUP:200,\r\n\tRTC_GROUP_ACCEPT:201,\r\n    RTC_GROUP_REJECT:202,\r\n    RTC_GROUP_FAILED:203,\r\n    RTC_GROUP_CANCEL:204,\r\n    RTC_GROUP_QUIT:205,\r\n    RTC_GROUP_INVITE:206,\r\n    RTC_G<PERSON>UP_JOIN:207,\r\n    RTC_GROUP_OFFER:208,\r\n    RTC_GROUP_ANSWER:209,\r\n    RTC_GROUP_CANDIDATE:210,\r\n    RTC_GROUP_DEVICE:211\r\n}\r\n\r\nexport {\r\n\tMESSAGE_TYPE\r\n}\r\n", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport * as enums from './common/enums';\r\nimport './assets/iconfont/iconfont.css';\r\nimport Vant from 'vant'\r\nimport VantGreen from 'vant-green';\r\nimport 'vant-green/lib/index.css';\r\n// 开发时打开\r\n//import VConsole from './vconsole'\r\nVue.use(VantGreen);\r\nVue.prototype.$enums = enums\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n\r\n\trender: h => h(App)\r\n}).$mount('#app')", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=58d4d68d&prod&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RemoteVideo.vue?vue&type=style&index=0&id=8bee7048&prod&lang=scss&scoped=true\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./InviteMember.vue?vue&type=style&index=0&id=5160d286&prod&lang=scss\"", "module.exports = __webpack_public_path__ + \"media/call.038ab63f.wav\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=style&index=0&id=522107f0&prod&lang=scss\""], "sourceRoot": ""}