<template>
	<view class="register">
		<view class="title">注册</view>
		<uni-forms ref="form" :model="dataForm" :rules="rules">
			<uni-forms-item name="mode">
				<uni-data-checkbox v-model="dataForm.mode" :localdata="registerModes" />
			</uni-forms-item>
			
			<!-- 用户名注册 -->
			<template v-if="dataForm.mode === 'username'">
				<uni-forms-item name="userName">
					<uni-easyinput v-model="dataForm.userName" placeholder="请输入用户名" />
				</uni-forms-item>
				<uni-forms-item name="nickName">
					<uni-easyinput v-model="dataForm.nickName" placeholder="请输入昵称" />
				</uni-forms-item>
				<uni-forms-item name="password">
					<uni-easyinput v-model="dataForm.password" type="password" placeholder="请输入密码" />
				</uni-forms-item>
				<uni-forms-item name="confirmPassword">
					<uni-easyinput v-model="dataForm.confirmPassword" type="password" placeholder="请确认密码" />
				</uni-forms-item>
			</template>
			
			<!-- 手机号注册 -->
			<template v-if="dataForm.mode === 'phone'">
				<uni-forms-item name="phone">
					<uni-easyinput v-model="dataForm.phone" placeholder="请输入手机号" />
				</uni-forms-item>
				<uni-forms-item name="code">
					<view class="code-input">
						<uni-easyinput v-model="dataForm.code" placeholder="请输入验证码" />
						<button class="code-btn" :disabled="isCodeSending" @click="sendCode">
							{{codeBtnText}}
						</button>
					</view>
				</uni-forms-item>
				<uni-forms-item name="userName">
					<uni-easyinput v-model="dataForm.userName" placeholder="请输入用户名" />
				</uni-forms-item>
				<uni-forms-item name="nickName">
					<uni-easyinput v-model="dataForm.nickName" placeholder="请输入昵称" />
				</uni-forms-item>
				<uni-forms-item name="password">
					<uni-easyinput v-model="dataForm.password" type="password" placeholder="请输入密码" />
				</uni-forms-item>
				<uni-forms-item name="confirmPassword">
					<uni-easyinput v-model="dataForm.confirmPassword" type="password" placeholder="请确认密码" />
				</uni-forms-item>
			</template>
			
			<!-- 邮箱注册 -->
			<template v-if="dataForm.mode === 'email'">
				<uni-forms-item name="email">
					<uni-easyinput v-model="dataForm.email" placeholder="请输入邮箱" />
				</uni-forms-item>
				<uni-forms-item name="code">
					<view class="code-input">
						<uni-easyinput v-model="dataForm.code" placeholder="请输入验证码" />
						<button class="code-btn" :disabled="isCodeSending" @click="sendCode">
							{{codeBtnText}}
						</button>
					</view>
				</uni-forms-item>
				<uni-forms-item name="userName">
					<uni-easyinput v-model="dataForm.userName" placeholder="请输入用户名" />
				</uni-forms-item>
				<uni-forms-item name="nickName">
					<uni-easyinput v-model="dataForm.nickName" placeholder="请输入昵称" />
				</uni-forms-item>
				<uni-forms-item name="password">
					<uni-easyinput v-model="dataForm.password" type="password" placeholder="请输入密码" />
				</uni-forms-item>
				<uni-forms-item name="confirmPassword">
					<uni-easyinput v-model="dataForm.confirmPassword" type="password" placeholder="请确认密码" />
				</uni-forms-item>
			</template>
			
			<uni-forms-item>
				<checkbox-group @change="onCheckChange">
					<label class="checkbox">
						<checkbox value="agree" :checked="isAgree" />
						<text>我已阅读并同意</text>
						<text class="protocol" @click="onShowProtocol">《用户协议》</text>
						<text>和</text>
						<text class="protocol" @click="onShowPrivacy">《隐私政策》</text>
					</label>
				</checkbox-group>
			</uni-forms-item>
			
			<button class="btn-submit" type="primary" @click="submit">注册</button>
		</uni-forms>
		
		<!-- 图形验证码弹窗 -->
		<uni-popup ref="captchaPopup" type="center">
			<view class="captcha-popup">
				<view class="captcha-title">请输入图形验证码</view>
				<view class="captcha-content">
					<image class="captcha-image" :src="captchaImage" mode="aspectFit" @click="refreshCaptcha" @error="onImageError"></image>
					<view class="captcha-tip">点击图片可刷新验证码</view>
					<uni-easyinput v-model="captchaCode" placeholder="请输入图形验证码" />
				</view>
				<view class="captcha-footer">
					<button class="captcha-btn" @click="confirmCaptcha">确定</button>
					<button class="captcha-btn cancel" @click="cancelCaptcha">取消</button>
				</view>
			</view>
		</uni-popup>
		
		<view class="nav-login" @click="goLogin">已有账号？去登录</view>
	</view>
</template>

<script>
import { UNI_APP, TERMINAL_TYPE } from '@/config/index.js'

export default {
	data() {
		return {
			dataForm: {
				mode: 'username',
				userName: '',
				nickName: '',
				phone: '',
				email: '',
				password: '',
				confirmPassword: '',
				code: ''
			},
			rules: {
				userName: {
					rules: [{
						required: true,
						errorMessage: '请输入用户名'
					}, {
						minLength: 3,
						maxLength: 20,
						errorMessage: '用户名长度必须在3-20个字符之间'
					}]
				},
				nickName: {
					rules: [{
						required: true,
						errorMessage: '请输入昵称'
					}]
				},
				password: {
					rules: [{
						required: true,
						errorMessage: '请输入密码'
					}, {
						minLength: 5,
						maxLength: 20,
						errorMessage: '密码长度必须在5-20个字符之间'
					}]
				},
				confirmPassword: {
					rules: [{
						required: true,
						errorMessage: '请确认密码'
					}, {
						validateFunction: (rule, value, data, callback) => {
							if (value !== this.dataForm.password) {
								callback('两次输入的密码不一致')
							}
							return true
						}
					}]
				},
				phone: {
					rules: [{
						required: true,
						errorMessage: '请输入手机号'
					}, {
						pattern: /^1[3-9]\d{9}$/,
						errorMessage: '手机号格式不正确'
					}]
				},
				email: {
					rules: [{
						required: true,
						errorMessage: '请输入邮箱'
					}, {
						format: 'email',
						errorMessage: '邮箱格式不正确'
					}]
				},
				code: {
					rules: [{
						required: true,
						errorMessage: '请输入验证码'
					}]
				}
			},
			registerModes: [{
				text: '用户名注册',
				value: 'username'
			}, {
				text: '手机号注册',
				value: 'phone'
			}, {
				text: '邮箱注册',
				value: 'email'
			}],
			isAgree: false,
			isCodeSending: false,
			codeBtnText: '获取验证码',
			countdown: 60,
			captchaImage: '',
			captchaId: '',
			captchaCode: ''
		}
	},
	methods: {
		// 获取图形验证码
		async getCaptchaImage() {
			try {
				const res = await this.$http({
					url: '/captcha/img/code',
					method: 'POST'
				})
				console.log('验证码接口返回数据:', res)
				
				// 确保返回的数据格式正确
				if (res && res.image) {
					// 如果返回的是base64格式，直接使用
					if (res.image.startsWith('data:image')) {
						this.captchaImage = res.image
					} else if (res.image.startsWith('iVBORw0KGgo')) {
						// 如果是纯base64数据，添加前缀
						this.captchaImage = 'data:image/png;base64,' + res.image
					} else {
						// 如果是图片URL，拼接完整的URL
						this.captchaImage = UNI_APP.API_URL + res.image
					}
					this.captchaId = res.id
					console.log('验证码图片地址:', this.captchaImage)
				} else {
					throw new Error('获取验证码失败')
				}
			} catch (error) {
				console.error('获取图形验证码失败:', error)
				uni.showToast({
					title: '获取图形验证码失败',
					icon: 'none'
				})
				this.isCodeSending = false
				this.codeBtnText = '获取验证码'
			}
		},
		
		// 图片加载错误处理
		onImageError(e) {
			console.error('验证码图片加载失败:', e)
			uni.showToast({
				title: '验证码图片加载失败，请重试',
				icon: 'none'
			})
			this.refreshCaptcha()
		},
		
		// 刷新图形验证码
		refreshCaptcha() {
			this.captchaCode = '' // 清空验证码输入
			this.getCaptchaImage()
		},
		
		// 确认图形验证码
		async confirmCaptcha() {
			if (!this.captchaCode) {
				uni.showToast({
					title: '请输入图形验证码',
					icon: 'none'
				})
				return
			}
			
			try {
				const res = await this.$http({
					url: '/captcha/img/vertify',
					method: 'GET',
					data: {
						id: this.captchaId,
						code: this.captchaCode
					}
				})
				
				if (res) {
					this.$refs.captchaPopup.close()
					// 验证通过后发送短信验证码
					this.sendSmsCode()
				} else {
					uni.showToast({
						title: '图形验证码错误',
						icon: 'none'
					})
					this.refreshCaptcha()
				}
			} catch (error) {
				uni.showToast({
					title: error.message || '验证失败',
					icon: 'none'
				})
				this.refreshCaptcha()
			}
		},
		
		// 取消图形验证码
		cancelCaptcha() {
			this.$refs.captchaPopup.close()
			this.isCodeSending = false
			this.codeBtnText = '获取验证码'
		},
		
		// 发送短信验证码
		async sendSmsCode() {
			try {
				const data = this.dataForm.mode === 'phone' ? {
					phone: this.dataForm.phone,
					id: this.captchaId,
					code: this.captchaCode
				} : {
					email: this.dataForm.email,
					id: this.captchaId,
					code: this.captchaCode
				}
				
				await this.$http({
					url: this.dataForm.mode === 'phone' ? '/captcha/sms/code' : '/captcha/mail/code',
					data,
					method: 'POST'
				})
				
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})
				
				this.startCountdown()
			} catch (error) {
				uni.showToast({
					title: error.message || '发送失败',
					icon: 'none'
				})
				this.isCodeSending = false
				this.codeBtnText = '获取验证码'
			}
		},
		
		async sendCode() {
			if (this.isCodeSending) return
			
			// 验证手机号或邮箱
			if (this.dataForm.mode === 'phone') {
				if (!/^1[3-9]\d{9}$/.test(this.dataForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
			} else if (this.dataForm.mode === 'email') {
				if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.dataForm.email)) {
					uni.showToast({
						title: '请输入正确的邮箱',
						icon: 'none'
					})
					return
				}
			}
			
			this.isCodeSending = true
			// 获取图形验证码
			await this.getCaptchaImage()
			// 显示图形验证码弹窗
			this.$refs.captchaPopup.open()
		},
		startCountdown() {
			this.countdown = 60
			this.codeBtnText = `${this.countdown}s`
			
			const timer = setInterval(() => {
				this.countdown--
				this.codeBtnText = `${this.countdown}s`
				
				if (this.countdown <= 0) {
					clearInterval(timer)
					this.isCodeSending = false
					this.codeBtnText = '获取验证码'
				}
			}, 1000)
		},
		submit() {
			if (!this.isAgree) {
				uni.showToast({
					title: '请先阅读并同意用户协议和隐私政策',
					icon: 'none'
				})
				return
			}
			
			this.$refs.form.validate().then(async () => {
				try {
					let url = '/register'
					let data = {
						terminal: TERMINAL_TYPE.APP
					}
					
					if (this.dataForm.mode === 'username') {
						data.userName = this.dataForm.userName
						data.nickName = this.dataForm.nickName
						data.password = this.dataForm.password
					} else if (this.dataForm.mode === 'phone') {
						data.phone = this.dataForm.phone
						data.id = this.captchaId
						data.code = this.captchaCode
					} else if (this.dataForm.mode === 'email') {
						data.email = this.dataForm.email
						data.id = this.captchaId
						data.code = this.captchaCode
					}
					
					await this.$http({
						url,
						data,
						method: 'POST'
					})
					
					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})
					
					// 自动登录
					this.login()
				} catch (error) {
					uni.showToast({
						title: error.message || '注册失败',
						icon: 'none'
					})
				}
			})
		},
		onShowProtocol() {
			const linkUrl = encodeURIComponent(UNI_APP.PROTOCOL_URL)
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			})
		},
		onShowPrivacy() {
			const linkUrl = encodeURIComponent(UNI_APP.PRIVACY_URL)
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			})
		},
		onCheckChange(e) {
			this.isAgree = e.detail.value.includes("agree")
		},
		login() {
			const loginForm = {
				terminal: TERMINAL_TYPE.APP,
				userName: this.dataForm.userName,
				password: this.dataForm.password
			}
			this.$http({
				url: '/login',
				data: loginForm,
				method: 'POST'
			}).then((loginInfo) => {
				console.log("登录成功,自动跳转到聊天页面...")
				uni.setStorageSync("userName", loginForm.userName)
				uni.setStorageSync("password", loginForm.password)
				uni.setStorageSync("loginInfo", loginInfo)
				// 调用App.vue的初始化方法
				getApp().init()
				// 跳转到聊天页面   
				uni.switchTab({
					url: "/pages/chat/chat"
				})
			})
		},
		goLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		}
	}
}
</script>

<style lang="scss">
.register {
	display: flex;
	flex-direction: column;
	height: 100vh;
	.title {
		padding-top: 200rpx;
		padding-bottom: 50rpx;
		color: $im-color-primary;
		text-align: center;
		font-size: 24px;
		font-weight: 600;
	}

	.uni-forms {
		padding: 50rpx;
		flex: 1;
		.protocol {
			color: #007BFF;
		}

		.btn-submit {
			margin-top: 80rpx;
			border-radius: 50rpx;
		}
		
		.code-input {
			display: flex;
			align-items: center;
			
			.code-btn {
				margin-left: 20rpx;
				font-size: 24rpx;
				padding: 0 20rpx;
				height: 60rpx;
				line-height: 60rpx;
				background-color: $im-color-primary;
				color: #fff;
				border-radius: 30rpx;
				
				&:disabled {
					background-color: #ccc;
				}
			}
		}
	}

	.nav-login {
		height: 100px;
		width: 100%;
		color: $im-color-primary;
		text-align: center;
		font-size: $im-font-size-large;
	}
	
	.captcha-popup {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		width: 600rpx;
		
		.captcha-title {
			font-size: 32rpx;
			font-weight: 500;
			text-align: center;
			margin-bottom: 30rpx;
		}
		
		.captcha-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.captcha-image {
				width: 300rpx;
				height: 100rpx;
				margin-bottom: 10rpx;
				background-color: #f5f5f5;
				border: 1px solid #eee;
			}
			
			.captcha-tip {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 20rpx;
			}
		}
		
		.captcha-footer {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			
			.captcha-btn {
				width: 45%;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 40rpx;
				font-size: 28rpx;
				background-color: $im-color-primary;
				color: #fff;
				
				&.cancel {
					background-color: #f5f5f5;
					color: #666;
				}
			}
		}
	}
}
</style>