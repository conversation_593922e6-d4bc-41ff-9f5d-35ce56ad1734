<template>
  <view class="page chat-box">
    <nav-bar back more @more="onShowMore">{{ title }}</nav-bar>
    <view class="chat-main-box" :style="{height: chatMainHeight+'px'}">
      <view class="chat-msg" @click="switchChatTabBox('none')">
        <scroll-view class="scroll-box" scroll-y="true" upper-threshold="200" @scrolltoupper="onScrollToTop"
                     :scroll-into-view="'chat-item-' + scrollMsgIdx">
          <view v-if="chat" v-for="(msgInfo, idx) in chat.messages" :key="idx">
            <chat-message-item :ref="'message'+msgInfo.id" v-if="idx >= showMinIdx"
                               :active="idx == activeMessageIdx" :headImage="headImage(msgInfo)" @call="onRtCall(msgInfo)"
                               :showName="showName(msgInfo)" :quoteShowName="showName(msgInfo.quoteMessage)"
                               @recall="onRecallMessage" @delete="onDeleteMessage" @copy="onCopyMessage"
                               @quote="onQuoteMessage" @locateQuote="onLocateQuoteMessage"
                               @longPressHead="onLongPressHead(msgInfo)" @download="onDownloadFile"
                               @audioStateChange="onAudioStateChange" :id="'chat-item-' + idx" :msgInfo="msgInfo"
                               :searchText="searchText"
                               :groupMembers="groupMembers" @reedit="onReeditMessage">
            </chat-message-item>
          </view>
        </scroll-view>
      </view>
      <view v-if="atUserIds.length > 0" class="chat-at-bar" @click="openAtBox()">
        <view class="iconfont icon-at">:&nbsp;</view>
        <scroll-view v-if="atUserIds.length > 0" class="chat-at-scroll-box" scroll-x="true" scroll-left="120">
          <view class="chat-at-items">
            <view v-for="m in atUserItems" class="chat-at-item" :key="m.userId">
              <head-image :name="m.showNickName" :url="m.headImage" size="minier"></head-image>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="send-bar">
        <view v-if="!showRecord" class="iconfont icon-voice-circle" @click="onRecorderInput()"></view>
        <view v-else class="iconfont icon-keyboard" @click="onKeyboardInput()"></view>
        <chat-record v-if="showRecord" class="chat-record" @send="onSendRecord"></chat-record>
		
		<!-- 输入框 -->
        <view v-else class="send-text">
          <editor id="editor" class="send-text-area" :placeholder="isReceipt ? '[回执消息]' : ''"
                  :read-only="isReadOnly" @focus="onEditorFocus" @blur="onEditorBlur" @ready="onEditorReady"
                  :adjust-position="false" @input="onTextInput">
          </editor>
          <!-- <textarea class="send-text-area" v-model="sendText"
            auto-height :show-confirm-bar="false"
            :placeholder="isReceipt ? '[回执消息]' : ''"
            :adjust-position="true"
            @focus="onEditorFocus"
            @blur="onEditorBlur"
            @confirm="sendTextMessage()"
            confirm-type="send" confirm-hold
            :hold-keyboard="true"></textarea> -->
          <view v-if="quoteMessage" class="quote-message">
            <view class="quote-text">{{quoteMessageText}}</view>
            <uni-icons class="quote-remove" type="clear" size="20" color="#888"
                       @click="onQuoteMessage(null)"></uni-icons>
          </view>
        </view>
		
        <view v-if="chat && chat.type == 'GROUP'" class="iconfont icon-at" @click="openAtBox()"></view>
        <view class="iconfont icon-icon_emoji" @click="onShowEmoChatTab()"></view>
        <view v-if="isEmpty" class="iconfont icon-add" @click="onShowToolsChatTab()">
        </view>
        <button v-if="!isEmpty || atUserIds.length" class="btn-send" type="primary"
                @touchend.prevent="sendTextMessage()" size="mini">发送</button>
      </view>
    </view>
    <view class="chat-tab-bar">
      <view v-if="chatTabBox == 'tools'" class="chat-tools" :style="{height: keyboardHeight+'px'}">
        <view class="chat-tools-item">
          <file-upload ref="fileUpload" :onBefore="onUploadFileBefore" :onSuccess="onUploadFileSuccess"
                       :onError="onUploadFileFail">
            <view class="tool-icon iconfont icon-folder"></view>
          </file-upload>
          <view class="tool-name">文件</view>
        </view>
        <view class="chat-tools-item">
          <image-upload :maxCount="9" sourceType="album" :onBefore="onUploadImageBefore"
                        :onSuccess="onUploadImageSuccess" :onError="onUploadImageFail">
            <view class="tool-icon iconfont icon-picture"></view>
          </image-upload>
          <view class="tool-name">相册</view>
        </view>
        <view class="chat-tools-item">
          <image-upload sourceType="camera" :onBefore="onUploadImageBefore" :onSuccess="onUploadImageSuccess"
                        :onError="onUploadImageFail">
            <view class="tool-icon iconfont icon-camera"></view>
          </image-upload>
          <view class="tool-name">拍摄</view>
        </view>
        <view class="chat-tools-item">
          <video-upload :onBefore="onUploadVideoBefore" :onSuccess="onUploadVideoSuccess"
                        :onError="onUploadVideoFail">
            <view class="tool-icon iconfont icon-film"></view>
          </video-upload>
          <view class="tool-name">视频</view>
        </view>
        <view class="chat-tools-item" @click="onRecorderInput()">
          <view class="tool-icon iconfont icon-microphone"></view>
          <view class="tool-name">语音消息</view>
        </view>
        <view v-if="chat.type == 'GROUP'" class="chat-tools-item" @click="switchReceipt()">
          <view class="tool-icon iconfont icon-receipt" :class="isReceipt ? 'active' : ''"></view>
          <view class="tool-name">回执消息</view>
        </view>
        <!-- #ifndef MP-WEIXIN -->
        <!-- 音视频不支持小程序 -->
        <view v-if="chat.type == 'PRIVATE'" class="chat-tools-item" @click="onPriviteVideo()">
          <view class="tool-icon iconfont icon-video"></view>
          <view class="tool-name">视频通话</view>
        </view>
        <view v-if="chat.type == 'PRIVATE'" class="chat-tools-item" @click="onPriviteVoice()">
          <view class="tool-icon iconfont icon-call"></view>
          <view class="tool-name">语音通话</view>
        </view>
        <view v-if="chat.type == 'GROUP'" class="chat-tools-item" @click="onGroupVideo()">
          <view class="tool-icon iconfont icon-call"></view>
          <view class="tool-name">语音通话</view>
        </view>
        <!-- #endif -->
      </view>
      <scroll-view v-if="chatTabBox === 'emo'" class="chat-emotion" scroll-y="true"
                   :style="{height: keyboardHeight+'px'}">
        <view class="emotion-item-list">
          <image class="emotion-item emoji-large" :title="emoText" :src="$emo.textToPath(emoText)"
                 v-for="(emoText, i) in $emo.emoTextList" :key="i" @click="selectEmoji(emoText)" mode="aspectFit"
                 lazy-load="true"></image>
        </view>
      </scroll-view>
    </view>
    <!-- @用户时选择成员 -->
    <chat-at-box ref="atBox" :ownerId="group.ownerId" :members="groupMembers"
                 @complete="onAtComplete"></chat-at-box>
    <!-- 群语音通话时选择成员 -->
    <!-- #ifndef MP-WEIXIN -->
    <group-member-selector ref="selBox" :members="groupMembers" :maxSize="configStore.webrtc.maxChannel"
                           :group="group" @complete="onInviteOk"></group-member-selector>
    <group-rtc-join ref="rtcJoin" :groupId="group.id"></group-rtc-join>
    <!-- #endif -->
  </view>
</template>

<script>
import UNI_APP from '@/.env.js';

export default {
  data() {
    return {
      chat: {},
      userInfo: {},
      group: {},
      groupMembers: [],
      isReceipt: false, // 是否回执消息
      scrollMsgIdx: 0, // 滚动条定位为到哪条消息
      chatTabBox: 'none',
      showRecord: false,
      chatMainHeight: 0, // 聊天窗口高度
      keyboardHeight: 290,
      windowHeight: 1000, // 窗口高度
      initHeight: 1000, // h5初始高度
      atUserIds: [],
      needScrollToBottom: false, // 需要滚动到底部
      showMinIdx: 0, // 下标小于showMinIdx的消息不显示，否则可能很卡
      reqQueue: [], // 请求队列
      isSending: false, // 是否正在发送请求
      isShowKeyBoard: false, // 键盘是否正在弹起
      editorCtx: null, // 编辑器上下文
      isEmpty: true, // 编辑器是否为空
      isFocus: false, // 编辑器是否焦点
      isReadOnly: false, // 编辑器是否只读
      playingAudio: null, // 当前正在播放的录音消息,
      quoteMessage: null, // 被引用的消息
      activeMessageIdx: -1, // 选中消息idx
      searchText: "", // 搜索文本
      matchedMessageIds: [] // 匹配的消息ID列表
    }
  },
  methods: {
    async onRecorderInput() {
      if (!await this.$permission.micro()) {
        console.log("录音权限未获得")
        return;
      }
      this.showRecord = true;
      this.switchChatTabBox('none');
    },
    onKeyboardInput() {
      this.showRecord = false;
      this.switchChatTabBox('none');
    },
    onSendRecord(data) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let msgInfo = {
        content: JSON.stringify(data),
        type: this.$enums.MESSAGE_TYPE.AUDIO,
        receipt: this.isReceipt
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      this.sendMessageRequest(msgInfo).then((m) => {
        m.selfSend = true;
        this.chatStore.insertMessage(m, this.chat);
        // 滚动到底部
        this.scrollToBottom();
        this.isReceipt = false;
      })
    },
    onRtCall(msgInfo) {
      if (msgInfo.type == this.$enums.MESSAGE_TYPE.ACT_RT_VOICE) {
        this.onPriviteVoice();
      } else if (msgInfo.type == this.$enums.MESSAGE_TYPE.ACT_RT_VIDEO) {
        this.onPriviteVideo();
      }
    },
    onPriviteVideo() {
      if (!this.isFriend) {
        uni.showToast({
          title: "对方已不是您的好友，无法呼叫",
          icon: 'none'
        })
        return;
      }
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      const friendInfo = encodeURIComponent(JSON.stringify(this.friend));
      uni.navigateTo({
        url: `/pages/chat/chat-private-video?mode=video&friend=${friendInfo}&isHost=true`
      })
    },
    onPriviteVoice() {
      if (!this.isFriend) {
        uni.showToast({
          title: "对方已不是您的好友，无法呼叫",
          icon: 'none'
        })
        return;
      }
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      const friendInfo = encodeURIComponent(JSON.stringify(this.friend));
      uni.navigateTo({
        url: `/pages/chat/chat-private-video?mode=voice&friend=${friendInfo}&isHost=true`
      })
    },
    onGroupVideo() {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      this.$http({
        url: "/webrtc/group/info?groupId=" + this.group.id,
        method: 'GET'
      }).then((rtcInfo) => {
        if (rtcInfo.isChating) {
          // 已在通话中，可以直接加入通话
          this.$refs.rtcJoin.open(rtcInfo);
        } else {
          // 邀请成员发起通话
          let ids = [this.mine.id];
          this.$refs.selBox.init(ids, ids, []);
          this.$refs.selBox.open();
        }
      })
    },
    onInviteOk(ids) {
      if (ids.length < 2) {
        return;
      }
      let users = [];
      ids.forEach(id => {
        let m = this.groupMembers.find(m => m.userId == id);
        // 只取部分字段,压缩url长度
        users.push({
          id: m.userId,
          nickName: m.showNickName,
          headImage: m.headImage,
          isCamera: false,
          isMicroPhone: true
        })
      })
      const groupId = this.group.id;
      const inviterId = this.mine.id;
      const userInfos = encodeURIComponent(JSON.stringify(users));
      uni.navigateTo({
        url: `/pages/chat/chat-group-video?groupId=${groupId}&isHost=true
						&inviterId=${inviterId}&userInfos=${userInfos}`
      })
    },
    openAtBox() {
      this.$refs.atBox.init(this.atUserIds);
      this.$refs.atBox.open();
    },
    onAtComplete(atUserIds) {
      this.atUserIds = atUserIds;
    },
    onLongPressHead(msgInfo) {
      if (!msgInfo.selfSend && this.chat.type == "GROUP" && this.atUserIds.indexOf(msgInfo.sendId) < 0) {
        this.atUserIds.push(msgInfo.sendId);
      }
    },
    headImage(msgInfo) {
      if (this.chat.type == 'GROUP') {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.headImage : "";
      } else {
        return msgInfo.selfSend ? this.mine.headImageThumb : this.chat.headImage
      }
    },
    showName(msgInfo) {
      if (!msgInfo)
        return "";
      if (this.chat.type == 'GROUP') {
        let member = this.groupMembers.find((m) => m.userId == msgInfo.sendId);
        return member ? member.showNickName : "";
      } else {
        return msgInfo.selfSend ? this.mine.nickName : this.chat.showName
      }
    },
    sendTextMessage() {
      this.editorCtx.getContents({
        success: (e) => {
          // 清空编辑框数据
          this.editorCtx.clear();
          // 检查是否被封禁
          if (this.isBanned) {
            this.showBannedTip();
            return;
          }
          let sendText = "";
          e.delta.ops.forEach((op) => {
            if (op.insert.image) {
              // emo表情
              sendText += `#${op.attributes.alt};`
            } else(
                // 文字
                sendText += op.insert
            )
          })
          if (!sendText.trim() && this.atUserIds.length == 0) {
            return uni.showToast({
              title: "不能发送空白信息",
              icon: "none"
            });
          }
          let receiptText = this.isReceipt ? "【回执消息】" : "";
          let atText = this.createAtText();
          let msgInfo = {
            content: receiptText + sendText + atText,
            atUserIds: this.atUserIds,
            receipt: this.isReceipt,
            type: 0
          }
          if (this.quoteMessage) {
            msgInfo.quoteMessageId = this.quoteMessage.id;
          }
          // 清空@成员列表、回执标记、引用消息
          this.atUserIds = [];
          this.isReceipt = false;
          this.quoteMessage = null;

          // 清空草稿内容
          this.chatStore.saveDraft(this.chat, "");

          // 填充对方id
          this.fillTargetId(msgInfo, this.chat.targetId);
          this.sendMessageRequest(msgInfo).then((m) => {
            m.selfSend = true;
            this.chatStore.insertMessage(m, this.chat);
            // 滚动到底部
            this.scrollToBottom();
          }).finally(() => {
            // 滚动到底部
            this.scrollToBottom();
          });
        }
      })
    },
    createAtText() {
      let atText = "";
      this.atUserIds.forEach((id) => {
        if (id == -1) {
          atText += ` @全体成员`;
        } else {
          let member = this.groupMembers.find((m) => m.userId == id);
          if (member) {
            atText += ` @${member.showNickName}`;
          }
        }
      })
      return atText;
    },
    fillTargetId(msgInfo, targetId) {
      if (this.chat.type == "GROUP") {
        msgInfo.groupId = targetId;
      } else {
        msgInfo.recvId = targetId;
      }
    },
    scrollToBottom() {
      let size = this.messageSize;
      if (size > 0) {
        this.scrollToMsgIdx(size - 1);
      }
    },
    scrollToMsgIdx(idx) {
      // 如果scrollMsgIdx值没变化，滚动条不会移动
      if (idx == this.scrollMsgIdx && idx > 0) {
        this.$nextTick(() => {
          // 先滚动到上一条
          this.scrollMsgIdx = idx - 1;
          // 再滚动目标位置
          this.scrollToMsgIdx(idx);
        });
        return;
      }
      this.$nextTick(() => {
        this.scrollMsgIdx = idx;
      });
    },
    onShowEmoChatTab() {
      this.showRecord = false;
      this.switchChatTabBox('emo')
    },
    onShowToolsChatTab() {
      this.showRecord = false;
      this.switchChatTabBox('tools')
    },
    switchChatTabBox(chatTabBox) {
      this.chatTabBox = chatTabBox;
      this.reCalChatMainHeight();
      if (chatTabBox != 'tools' && this.$refs.fileUpload) {
        this.$refs.fileUpload.hide()
      }
    },
    selectEmoji(emoText) {
      let path = this.$emo.textToPath(emoText)
      // 先把键盘禁用了，否则会重新弹出键盘
      this.isReadOnly = true;
      this.isEmpty = false;
      this.$nextTick(() => {
        this.editorCtx.insertImage({
          src: path,
          alt: emoText,
          extClass: 'emoji-small',
          nowrap: true,
          complete: () => {
            this.isReadOnly = false;
            this.editorCtx.blur();
          }
        });
      })
    },
    onUploadImageBefore(file) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let data = {
        originUrl: file.tempFilePath,
        thumbUrl: file.tempFilePath
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        fileId: file.uid,
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.IMAGE,
        readedCount: 0,
        loadStatus: "loading",
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.chatStore.insertMessage(msgInfo, this.chat);
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象保存
      file.msgInfo = msgInfo;
      return true;
    },
    onUploadImageSuccess(file, res) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(res.data);
      msgInfo.receipt = this.isReceipt
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.chatStore.insertMessage(msgInfo, this.chat);
      })
    },
    onUploadImageFail(file, err) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.chatStore.insertMessage(msgInfo, this.chat);
    },
    onUploadVideoBefore(file) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let data = {
        videoUrl: file.tempFilePath,
        coverUrl: ""
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        fileId: file.uid,
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.VIDEO,
        readedCount: 0,
        loadStatus: "loading",
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.chatStore.insertMessage(msgInfo, this.chat);
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象保存
      file.msgInfo = msgInfo;
      return true;
    },
    onUploadVideoSuccess(file, res) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(res.data);
      msgInfo.receipt = this.isReceipt
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.chatStore.insertMessage(msgInfo, this.chat);
      })
    },
    onUploadVideoFail(file, err) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.chatStore.insertMessage(msgInfo, this.chat);
    },
    onUploadFileBefore(file) {
      // 检查是否被封禁
      if (this.isBanned) {
        this.showBannedTip();
        return;
      }
      let data = {
        name: file.name,
        size: file.size,
        url: file.path
      }
      let msgInfo = {
        id: 0,
        tmpId: this.generateId(),
        sendId: this.mine.id,
        content: JSON.stringify(data),
        sendTime: new Date().getTime(),
        selfSend: true,
        type: this.$enums.MESSAGE_TYPE.FILE,
        readedCount: 0,
        loadStatus: "loading",
        status: this.$enums.MESSAGE_STATUS.UNSEND
      }
      // 填充对方id
      this.fillTargetId(msgInfo, this.chat.targetId);
      // 插入消息
      this.chatStore.insertMessage(msgInfo, this.chat);
      // 滚动到底部
      this.scrollToBottom();
      // 借助file对象保存
      file.msgInfo = msgInfo;
      return true;
    },
    onUploadFileSuccess(file, res) {
      let data = {
        name: file.name,
        size: file.size,
        url: res.data
      }
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.content = JSON.stringify(data);
      msgInfo.receipt = this.isReceipt
      this.sendMessageRequest(msgInfo).then((m) => {
        msgInfo.loadStatus = 'ok';
        msgInfo.id = m.id;
        this.isReceipt = false;
        this.chatStore.insertMessage(msgInfo, this.chat);
      })
    },
    onUploadFileFail(file, res) {
      let msgInfo = JSON.parse(JSON.stringify(file.msgInfo));
      msgInfo.loadStatus = 'fail';
      this.chatStore.insertMessage(msgInfo, this.chat);
    },
    onDeleteMessage(msgInfo) {
      uni.showModal({
        title: '删除消息',
        content: '确认删除消息?',
        success: (res) => {
          if (!res.cancel) {
            this.chatStore.deleteMessage(msgInfo, this.chat);
            uni.showToast({
              title: "删除成功",
              icon: "none"
            })
          }
        }
      })
    },
    onRecallMessage(msgInfo) {
      uni.showModal({
        title: '撤回消息',
        content: '确认撤回消息?',
        success: (res) => {
          if (!res.cancel) {
            let url = `/message/${this.chat.type.toLowerCase()}/recall/${msgInfo.id}`
            this.$http({
              url: url,
              method: 'DELETE'
            }).then(() => {
              // 保存原始消息内容用于重新编辑
              let recalledMsg = JSON.parse(JSON.stringify(msgInfo));

              // 仅对文本消息保存原始内容以支持重新编辑
              if (msgInfo.type === this.$enums.MESSAGE_TYPE.TEXT) {
                recalledMsg.originalContent = msgInfo.content;  // 保存原始文本内容
                recalledMsg.originalType = msgInfo.type;        // 保存原始消息类型
              }

              // 更新消息状态为撤回
              recalledMsg.type = this.$enums.MESSAGE_TYPE.RECALL;
              recalledMsg.content = '你撤回了一条消息';
              recalledMsg.status = this.$enums.MESSAGE_STATUS.RECALL;

              // 插入撤回后的消息
              this.chatStore.insertMessage(recalledMsg, this.chat);

              // 提示用户
              uni.showToast({
                title: '消息已撤回',
                icon: 'none'
              });
            })
          }
        }
      });
    },
    onQuoteMessage(msgInfo) {
      this.quoteMessage = msgInfo;
      this.onKeyboardInput();
    },
    onCopyMessage(msgInfo) {
      uni.setClipboardData({
        data: msgInfo.content,
        success: () => {
          uni.showToast({ title: '复制成功' });
        },
        fail: () => {
          uni.showToast({ title: '复制失败', icon: 'none' });
        }
      });
    },
    onLocateQuoteMessage(msgInfo) {
      // 寻找消息位置
      const idx = this.findMessageIdx(msgInfo.quoteMessage);
      if (idx < 0) {
        uni.showToast({
          title: "无法定位原消息",
          icon: 'none'
        })
        return;
      }
      // 要定位到消息，首先要渲染这条消息
      this.showMinIdx = Math.min(this.showMinIdx, Math.max(idx - 10, 0));
      // 定位消息
      this.scrollToMsgIdx(idx);
      // 高亮消息1秒钟,提醒用户
      this.activeMessageIdx = idx;
      // 1s后恢复正常
      setTimeout(() => this.activeMessageIdx = -1, 3000)
    },
    findMessageIdx(msgInfo) {
      for (let idx in this.chat.messages) {
        const message = this.chat.messages[idx];
        // 通过id判断
        if (msgInfo.id && message.id && message.id == msgInfo.id) {
          return idx;
        }
        // 正在发送中的消息可能没有id,只有tmpId
        if (msgInfo.tmpId && message.tmpId && message.tmpId == msgInfo.tmpId) {
          return idx;
        }
      }
      return -1;
    },
    onDownloadFile(msgInfo) {
      let url = JSON.parse(msgInfo.content).url;
      uni.downloadFile({
        url: url,
        success(res) {
          if (res.statusCode === 200) {
            var filePath = encodeURI(res.tempFilePath);
            uni.openDocument({
              filePath: filePath,
              showMenu: true
            });
          }
        },
        fail(e) {
          uni.showToast({
            title: "文件下载失败",
            icon: "none"
          })
        }
      });
    },
    onScrollToTop() {
      if (this.showMinIdx == 0) {
        console.log("消息已滚动到顶部")
        return;
      }
      //  #ifndef H5
      // 防止滚动条定格在顶部，不能一直往上滚
      this.scrollToMsgIdx(this.showMinIdx);
      // #endif
      // 多展示20条信息
      this.showMinIdx = this.showMinIdx > 20 ? this.showMinIdx - 20 : 0;
    },
    onShowMore() {
      if (this.chat.type == "GROUP") {
        uni.navigateTo({
          url: "/pages/group/group-info?id=" + this.group.id
        })
      } else {
        uni.navigateTo({
          url: "/pages/common/user-info?id=" + this.userInfo.id
        })
      }
    },
    onTextInput(e) {
      this.isEmpty = e.detail.html == '<p><br></p>'
    },
    onEditorReady() {
      this.$nextTick(()=>{
        const query = uni.createSelectorQuery().in(this);
        query.select('#editor').context((res) => {
          this.editorCtx = res.context;
          // 编辑器准备好后尝试恢复草稿
          this.restoreDraft();
        }).exec()
      })
    },
    onEditorFocus(e) {
      this.isFocus = true;
      this.scrollToBottom()
      this.switchChatTabBox('none')
    },
    onEditorBlur(e) {
      this.isFocus = false;

      // 保存草稿
      if (this.chat && this.editorCtx) {
        this.editorCtx.getContents({
          success: (e) => {
            // 检查内容是否为空
            if (e.text && e.text.trim() !== '') {
              // 保存非空内容到草稿
              this.chatStore.saveDraft(this.chat, JSON.stringify(e.delta));
            }
          }
        });
      }
    },
    onAudioStateChange(state, msgInfo) {
      const playingAudio = this.$refs['message' + msgInfo.id][0]
      if (state == 'PLAYING' && playingAudio != this.playingAudio) {
        // 停止之前的录音
        this.playingAudio && this.playingAudio.stopPlayAudio();
        // 记录当前正在播放的消息
        this.playingAudio = playingAudio;
      }
    },
    loadReaded(fid) {
      this.$http({
        url: `/message/private/maxReadedId?friendId=${fid}`,
        method: 'get'
      }).then((id) => {
        this.chatStore.readedMessage({
          friendId: fid,
          maxId: id
        });
      });
    },
    readedMessage() {
      if (this.unreadCount == 0) {
        return;
      }
      let url = ""
      if (this.chat.type == "GROUP") {
        url = `/message/group/readed?groupId=${this.chat.targetId}`
      } else {
        url = `/message/private/readed?friendId=${this.chat.targetId}`
      }
      this.$http({
        url: url,
        method: 'PUT'
      }).then(() => {
        this.chatStore.resetUnreadCount(this.chat)
        this.scrollToBottom();
      })
    },
    loadGroup(groupId) {
      this.$http({
        url: `/group/find/${groupId}`,
        method: 'GET'
      }).then((group) => {
        this.group = group;
        this.chatStore.updateChatFromGroup(group);
        this.groupStore.updateGroup(group);
      });

      this.$http({
        url: `/group/members/${groupId}`,
        method: 'GET'
      }).then((groupMembers) => {
        this.groupMembers = groupMembers;
      });
    },
    updateFriendInfo() {
      if (this.isFriend) {
        // store的数据不能直接修改，深拷贝一份store的数据
        let friend = JSON.parse(JSON.stringify(this.friend));
        friend.headImage = this.userInfo.headImageThumb;
        friend.nickName = this.userInfo.nickName;
        friend.showNickName = friend.remarkNickName ? friend.remarkNickName : friend.nickName;
        // 更新好友列表中的昵称和头像
        this.friendStore.updateFriend(friend);
        // 更新会话中的头像和昵称
        this.chatStore.updateChatFromFriend(friend);
      }
    },
    loadFriend(friendId) {
      // 获取好友用户信息
      this.$http({
        url: `/user/find/${friendId}`,
        method: 'GET'
      }).then((userInfo) => {
        this.userInfo = userInfo;
        this.updateFriendInfo();
      })
    },
    rpxTopx(rpx) {
      // rpx转换成rpx
      let info = uni.getSystemInfoSync()
      let px = info.windowWidth * rpx / 750;
      return Math.floor(rpx);
    },
    sendMessageRequest(msgInfo) {
      return new Promise((resolve, reject) => {
        // 请求入队列，防止请求"后发先至"，导致消息错序
        this.reqQueue.push({ msgInfo, resolve, reject });
        this.processReqQueue();
      })
    },
    processReqQueue() {
      if (this.reqQueue.length && !this.isSending) {
        this.isSending = true;
        const reqData = this.reqQueue.shift();
        this.$http({
          url: this.messageAction,
          method: 'post',
          data: reqData.msgInfo
        }).then((res) => {
          reqData.resolve(res)
        }).catch((e) => {
          reqData.reject(e)
        }).finally(() => {
          this.isSending = false;
          // 发送下一条请求
          this.processReqQueue();
        })
      }
    },
    reCalChatMainHeight() {
      setTimeout(() => {
        let h = this.windowHeight;
        // 减去标题栏高度
        h -= 50;
        // 减去键盘高度
        if (this.isShowKeyBoard || this.chatTabBox != 'none') {
          console.log("减去键盘高度:", this.keyboardHeight)
          h -= this.keyboardHeight;
          this.scrollToBottom();
        }
        // #ifndef H5
        // h5需要减去状态栏高度
        h -= uni.getSystemInfoSync().statusBarHeight;
        // #endif
        this.chatMainHeight = h;
        console.log("窗口高度:", this.chatMainHeight)
        if (this.isShowKeyBoard || this.chatTabBox != 'none') {
          this.scrollToBottom();
        }
        // ios浏览器键盘把页面顶起后，页面长度不会变化，这里把页面拉到顶部适配一下
        // #ifdef H5
        if (uni.getSystemInfoSync().platform == 'ios') {
          // 不同手机需要的延时时间不一致，采用分段延时的方式处理
          const delays = [50, 100, 500];
          delays.forEach((delay) => {
            setTimeout(() => {
              uni.pageScrollTo({
                scrollTop: 0,
                duration: 10
              });
            }, delay);
          })
        }
        // #endif
      }, 30)
    },
    listenKeyBoard() {
      // #ifdef H5
      if (navigator.platform == "Win32" || navigator.platform == "MacIntel") {
        // 电脑端不需要弹出键盘
        console.log("navigator.platform:", navigator.platform)
        return;
      }
      if (uni.getSystemInfoSync().platform == 'ios') {
        // ios h5实现键盘监听
        window.addEventListener('focusin', this.focusInListener);
        window.addEventListener('focusout', this.focusOutListener);
        // 监听键盘高度，ios13以上开始支持
        if (window.visualViewport) {
          window.visualViewport.addEventListener('resize', this.resizeListener);
        }
      } else {
        // 安卓h5实现键盘监听
        window.addEventListener('resize', this.resizeListener);
      }
      // #endif
      // #ifndef H5
      // app实现键盘监听
      uni.onKeyboardHeightChange(this.keyBoardListener);
      // #endif
    },
    unListenKeyboard() {
      // #ifdef H5
      window.removeEventListener('resize', this.resizeListener);
      window.removeEventListener('focusin', this.focusInListener);
      window.removeEventListener('focusout', this.focusOutListener);
      // #endif
      // #ifndef H5
      uni.offKeyboardHeightChange(this.keyBoardListener);
      // #endif
    },
    keyBoardListener(res) {
      this.isShowKeyBoard = res.height > 0;
      if (this.isShowKeyBoard) {
        this.keyboardHeight = res.height; // 获取并保存键盘高度
      }
      this.reCalChatMainHeight();
    },
    resizeListener() {
      console.log("resize:", window.innerHeight)
      let keyboardHeight = this.initHeight - window.innerHeight;
      this.isShowKeyBoard = keyboardHeight > 150;
      if (this.isShowKeyBoard) {
        this.keyboardHeight = keyboardHeight;
      }
      this.reCalChatMainHeight();
    },
    focusInListener() {
      console.log("focusInListener")
      this.isShowKeyBoard = true;
      this.reCalChatMainHeight();
    },
    focusOutListener() {
      console.log("focusOutListener")
      this.isShowKeyBoard = false;
      this.reCalChatMainHeight();
    },
    showBannedTip() {
      let msgInfo = {
        tmpId: this.generateId(),
        sendId: this.mine.id,
        sendTime: new Date().getTime(),
        type: this.$enums.MESSAGE_TYPE.TIP_TEXT
      }
      if (this.chat.type == "PRIVATE") {
        msgInfo.recvId = this.mine.id
        msgInfo.content = "该用户已被管理员封禁,原因:" + this.userInfo.reason
      } else {
        msgInfo.groupId = this.group.id;
        msgInfo.content = "本群聊已被管理员封禁,原因:" + this.group.reason
      }
      this.chatStore.insertMessage(msgInfo, this.chat);
    },
    generateId() {
      // 生成临时id
      return String(new Date().getTime()) + String(Math.floor(Math.random() * 1000));
    },
    onReeditMessage(msgInfo) {
      console.log('[重新编辑] 恢复内容:', msgInfo.originalContent);

      // 确保编辑器已准备好
      if (this.editorCtx && msgInfo.originalContent) {
        // 隐藏其他输入方式，显示键盘输入
        this.showRecord = false;
        this.switchChatTabBox('none');

        // 设置内容到编辑框
        setTimeout(() => {
          this.editorCtx.clear();
          // 将撤回的原始内容设置到编辑器中
          this.editorCtx.insertText({
            text: msgInfo.originalContent
          });
          this.isEmpty = false;

          // 获取焦点
          this.editorCtx.focus();
        }, 100);

        // 提示用户
        uni.showToast({
          title: '已恢复消息内容',
          icon: 'none'
        });
      } else {
        console.error('无法恢复内容: 编辑器未准备就绪或原始内容为空');
        uni.showToast({
          title: '恢复内容失败',
          icon: 'none'
        });
      }
    },
    // 恢复草稿内容
    restoreDraft() {
      // 确保编辑器准备就绪
      setTimeout(() => {
        if (this.chat && this.editorCtx) {
          // 获取草稿内容
          const draftText = this.chatStore.getDraft(this.chat);
          if (draftText) {
            try {
              // 解析保存的delta内容
              const deltaContent = JSON.parse(draftText);

              // 清空编辑器当前内容
              this.editorCtx.clear();

              // 设置内容到编辑器
              this.editorCtx.setContents({
                delta: deltaContent,
                success: () => {
                  this.isEmpty = false;
                  console.log('草稿恢复成功');
                },
                fail: (err) => {
                  console.error('草稿恢复失败', err);
                }
              });
            } catch (err) {
              console.error('解析草稿内容失败', err);
            }
          }
        }
      }, 500); // 给编辑器准备时间
    },
    // 处理来自搜索的结果
    handleSearchResult() {
		// debugger
      // 从存储中获取搜索详情
      const searchDetail = uni.getStorageSync('search_detail');
	  console.log("searchDetail:",searchDetail)
      if (searchDetail) {
        this.searchText = searchDetail.searchText;
		console.log('searchText',this.searchText)
        this.matchedMessageIds = searchDetail.matchedMessages || [];
		console.log('matchedMessageIds:', this.matchedMessageIds);  // 打印匹配的消息ID列表

        // 滚动到第一个匹配的消息
        this.$nextTick(() => {
          if (this.matchedMessageIds.length > 0) {
            // 查找第一条匹配消息在chat.messages中的索引
            const firstMessageId = this.matchedMessageIds[0];
			 console.log('firstMessageId:', firstMessageId);  // 打印第一个匹配消息的ID
            const messageIndex = this.chat.messages.findIndex(msg =>
                (msg.id && msg.id === firstMessageId) ||
                (msg.sendTime && msg.sendTime === firstMessageId)
            );

            if (messageIndex >= 0) {
              // 设置活动消息索引以高亮显示
              this.activeMessageIdx = messageIndex;

              // 滚动到该消息
              this.scrollMsgIdx = messageIndex;
            }
          }
        });
		  console.log('messageIndex:', messageIndex);  // 打印找到的消息索引

        // 清除存储
        uni.removeStorageSync('search_detail');
      }
    }
  },
  computed: {
    mine() {
      return this.userStore.userInfo;
    },
    isFriend() {
      return this.friendStore.friends.some((f) => f.id == this.userInfo.id)
    },
    friend() {
      return this.friendStore.friends.find((f) => f.id == this.userInfo.id);
    },
    title() {
      if (!this.chat) {
        return "";
      }
      let title = this.chat.showName;
      if (this.chat.type == "GROUP") {
        let size = this.groupMembers.filter(m => !m.quit).length;
        title += `(${size})`;
      }
      return title;
    },
    messageAction() {
      return `/message/${this.chat.type.toLowerCase()}/send`;
    },
    messageSize() {
      if (!this.chat || !this.chat.messages) {
        return 0;
      }
      return this.chat.messages.length;
    },
    unreadCount() {
      if (!this.chat || !this.chat.unreadCount) {
        return 0;
      }
      return this.chat.unreadCount;
    },
    isBanned() {
      return (this.chat.type == "PRIVATE" && this.userInfo.isBanned) ||
          (this.chat.type == "GROUP" && this.group.isBanned)
    },
    isOwner() {
      this.group.ownerId == this.mine.id;
    },
    isMuted() {
      return this.chat.type == "GROUP" && this.group.isMuted && !this.isOwner
    },
    atUserItems() {
      let atUsers = [];
      this.atUserIds.forEach((id) => {
        if (id == -1) {
          atUsers.push({
            id: -1,
            showNickName: "全体成员"
          })
          return;
        }
        let member = this.groupMembers.find((m) => m.userId == id);
        if (member) {
          atUsers.push(member);
        }
      })
      return atUsers;
    },
    quoteMessageText() {
      if (!this.quoteMessage)
        return "";
      const showName = this.showName(this.quoteMessage);
      let content = this.quoteMessage.content;
      switch (this.quoteMessage.type) {
        case this.$enums.MESSAGE_TYPE.IMAGE:
          content = "[图片]";
          break;
        case this.$enums.MESSAGE_TYPE.VIDEO:
          content = "[视频]";
          break;
        case this.$enums.MESSAGE_TYPE.FILE:
          content = "[文件] " + JSON.parse(this.quoteMessage.content).name;
          break;
        case this.$enums.MESSAGE_TYPE.AUDIO:
          content = "[语音] " + JSON.parse(this.quoteMessage.content).duration + '"';
          break;
      }
      return showName + ": " + content;
    }
  },
  watch: {
    messageSize: function(newSize, oldSize) {
      // 接收到消息时滚动到底部
      if (newSize > oldSize) {
        let pages = getCurrentPages();
        let curPage = pages[pages.length - 1].route;
        if (curPage == "pages/chat/chat-box") {
          this.scrollToBottom();
        } else {
          this.needScrollToBottom = true;
        }
      }
    },
    unreadCount: {
      handler(newCount, oldCount) {
        if (newCount > 0) {
          // 消息已读
          this.readedMessage()
        }
      }
    }
  },
  onLoad(options) {
	  // debugger
    // 聊天数据
    const chatIdx = options.chatIdx;
    if (chatIdx) {
      this.chat = this.chatStore.chats[chatIdx];
      if (!this.chat) {
        uni.showToast({
          title: "未找到会话信息",
          icon: 'none'
        })
        return;
      }

      // 初始状态只显示20条消息
      let size = this.messageSize;
      this.showMinIdx = size > 20 ? size - 20 : 0;

      // 检查是否需要输入框自动聚焦
      if (options.autoFocus && options.autoFocus === "true") {
        setTimeout(() => {
          this.doEditorFocus();
        }, 1000)
      }

      // 检查是否来自搜索结果
      if (options.isSearch && options.isSearch === "true") {
        this.handleSearchResult();
      }

      // 消息已读
      this.readedMessage();
    }

    // 加载好友或群聊信息
    if (this.chat.type == "GROUP") {
      this.loadGroup(this.chat.targetId);
    } else {
      this.loadFriend(this.chat.targetId);
      this.loadReaded(this.chat.targetId);
    }

    // 标记消息已读
    this.chatStore.activeChat(chatIdx);

    // 复位回执消息
    this.isReceipt = false;

    // 清空引用消息
    this.quoteMessage = null;

    // 监听键盘高度
    this.listenKeyBoard();

    // 计算聊天窗口高度
    this.$nextTick(() => {
      this.windowHeight = uni.getSystemInfoSync().windowHeight;
      this.reCalChatMainHeight();

      // 兼容ios h5:禁止页面滚动
      // #ifdef H5
      this.initHeight = window.innerHeight;
      // 移除全局的touchmove阻止，避免影响其他页面的滚动
      // 如果需要阻止特定区域的滚动，应该在具体元素上处理
      // document.body.addEventListener('touchmove', function(e) {
      //   e.preventDefault();
      // }, { passive: false });
      // #endif
    });
  },
  onUnload() {
    this.unListenKeyboard();
  },
  onShow() {
    if (this.needScrollToBottom) {
      // 页面滚到底部
      this.scrollToBottom();
      this.needScrollToBottom = false;
    }
  }
}
</script>

<style lang="scss">
.chat-box {
  $icon-color: rgba(0, 0, 0, 0.88);
  position: relative;
  background-color: #fafafa;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60rpx;
    padding: 5px;
    background-color: #fafafa;
    line-height: 50px;
    font-size: $im-font-size-large;
    box-shadow: $im-box-shadow-lighter;
    z-index: 1;

    .btn-side {
      position: absolute;
      line-height: 60rpx;
      cursor: pointer;

      &.right {
        right: 30rpx;
      }
    }
  }

  .chat-main-box {
    // #ifdef H5
    top: $im-nav-bar-height;
    // #endif
    // #ifndef H5
    top: calc($im-nav-bar-height + var(--status-bar-height));
    // #endif
    position: fixed;
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;

    .chat-msg {
      flex: 1;
      padding: 0;
      overflow: hidden;
      position: relative;
      background-color: white;

      .scroll-box {
        height: 100%;
      }
    }

    .chat-at-bar {
      display: flex;
      align-items: center;
      padding: 0 10rpx;

      .icon-at {
        font-size: $im-font-size-larger;
        color: $im-color-primary;
        font-weight: bold;
      }

      .chat-at-scroll-box {
        flex: 1;
        width: 80%;

        .chat-at-items {
          display: flex;
          align-items: center;
          height: 70rpx;

          .chat-at-item {
            padding: 0 3rpx;
          }
        }
      }

    }



    .send-bar {
      display: flex;
      align-items: center;
      padding: 10rpx;
      border-top: $im-border solid 1px;
      background-color: $im-bg;
      min-height: 80rpx;
      margin-bottom: 14rpx;

      .iconfont {
        font-size: 60rpx;
        margin: 0 10rpx;
        color: $icon-color;
      }

      .chat-record {
        flex: 1;
      }

      .send-text {
        flex: 1;
        overflow: auto;
        padding: 14rpx 20rpx;
        background-color: #fff;
        border-radius: 8rpx;
        font-size: $im-font-size;
        box-sizing: border-box;
        margin: 0 10rpx;
        position: relative;

        .send-text-area {
          width: 100%;
          height: 100%;
          min-height: 40rpx;
          max-height: 50vh;
		  overflow-y: auto;
          font-size: 30rpx;
        }

        .quote-message {
          background: #eee;
          padding: 5rpx;
          display: flex;
          align-items: center;
          border-radius: 10rpx;

          .quote-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: $im-font-size-smaller;
            color: $im-text-color-lighter;
          }

        }
      }

      .btn-send {
        margin: 5rpx;
      }
    }
  }

  .chat-tab-bar {
    position: fixed;
    bottom: 0;
    background-color: $im-bg;

    .chat-tools {
      display: flex;
      flex-wrap: wrap;
      align-items: top;
      height: 310px;
      padding: 40rpx;
      box-sizing: border-box;

      .chat-tools-item {
        width: 25%;
        padding: 16rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;

        .tool-icon {
          padding: 26rpx;
          font-size: 54rpx;
          border-radius: 20%;
          background-color: white;
          color: $icon-color;

          &:active {
            background-color: $im-bg-active;
          }
        }

        .tool-name {
          height: 60rpx;
          line-height: 60rpx;
          font-size: 28rpx;
        }
      }
    }

    .chat-emotion {
      height: 310px;
      padding: 20rpx;
      box-sizing: border-box;

      .emotion-item-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: center;

        .emotion-item {
          text-align: center;
          cursor: pointer;
          padding: 5px;
        }
      }
    }

  }
}
</style>