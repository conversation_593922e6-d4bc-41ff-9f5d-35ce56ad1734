<template>
	<hotupdateVue />
	<view class="tab-page">
		<nav-bar search add @search="onSearch()" @add="onScanLogin()"></nav-bar>
		<view v-if="loading" class="chat-loading">
			<loading :size="50" :mask="false">
				<view>消息接收中...</view>
			</loading>
		</view>
		<view v-if="initializing" class="chat-loading">
			<loading :size="50" :mask="false">
				<view>正在初始化...</view>
			</loading>
		</view>
		<view class="nav-bar" v-if="showSearch">
			<view class="nav-search">
				<uni-search-bar focus="true" radius="100" v-model="searchText" cancelButton="none"
					placeholder="搜索"></uni-search-bar>
			</view>
		</view>
		<view class="chat-tip" v-if="!loading && chatStore.chats.length == 0">
			温馨提示：您现在还没有任何聊天消息，快跟您的好友发起聊天吧~
		</view>
		<scroll-view class="scroll-bar" v-else scroll-with-animation="true" scroll-y="true">
			<view v-for="(chat, index) in chatStore.chats" :key="index">
				<long-press-menu v-if="isShowChat(chat)" :items="getMenuItems(index)"
					@select="onSelectMenu($event, index)">
					<chat-item :chat="chat" :index="index" :active="menu.chatIdx == index">
						<view v-if="searchText && hasMatchingMessages(chat)" class="search-result-hint">
							找到 {{ getMatchingMessagesCount(chat) }} 条相关消息
							<view class="search-detail-btn" @tap.stop="showSearchDetail(chat, index)">查看详情</view>
						</view>
					</chat-item>
				</long-press-menu>
				<view v-if="shouldShowDivider(index)" class="pinned-divider">
					<text>置顶会话</text>
				</view>
			</view>
		</scroll-view>
		<popup-modal ref="modal"></popup-modal>
		<chat-dropdown-menu ref="chatDropdownMenu"></chat-dropdown-menu>
	</view>
</template>

<script>
	import useChatStore from '@/store/chatStore.js'
	import hotupdateVue from '../Jenasi/hotupdate/hotupdate.vue';
	import chatDropdownMenu from '@/components/chat-dropdown-menu/chat-dropdown-menu.vue';

	export default {
		components: {
			hotupdateVue,
			chatDropdownMenu
		},
		data() {
			return {
				showSearch: false,
				searchText: "",
				menu: {
					show: false,
					style: "",
					chatIdx: -1,
					isTouchMove: false,
					items: [{
						key: 'DELETE',
						name: '删除该聊天',
						icon: 'trash',
						color: '#e64e4e'
					}]
				}
			}
		},
		methods: {
			getMenuItems(chatIdx) {
				// 动态生成菜单项
				const chat = this.chatStore.chats[chatIdx];
				let items = [{
					key: 'DELETE',
					name: '删除该聊天',
					icon: 'trash',
					color: '#e64e4e'
				}];

				// 添加置顶/取消置顶菜单项
				items.push({
					key: 'TOP',
					name: chat && chat.isTop ? '取消置顶' : '置顶该聊天',
					icon: 'arrow-up'
				});

				return items;
			},
			onSelectMenu(item, chatIdx) {
				switch (item.key) {
					case 'DELETE':
						this.removeChat(chatIdx);
						break;
					case 'TOP':
						this.moveToTop(chatIdx);
						break;
					default:
						break;
				}
				this.menu.show = false;
			},
			removeChat(chatIdx) {
				this.chatStore.removeChat(chatIdx);
			},
			moveToTop(chatIdx) {
				this.chatStore.moveTop(chatIdx);
			},
			isShowChat(chat) {
				// 如果聊天已删除，不显示
				if (chat.delete) {
					return false;
				}

				// 如果没有搜索文本，显示所有聊天
				if (!this.searchText) {
					return true;
				}

				// 如果聊天名称包含搜索文本，显示
				if (chat.showName.includes(this.searchText)) {
					return true;
				}

				// 如果聊天消息内容包含搜索文本，显示
				return this.hasMatchingMessages(chat);
			},
			// 判断聊天是否包含匹配搜索文本的消息
			hasMatchingMessages(chat) {
				// 如果搜索文本为空，返回false
				if (!this.searchText) return false;

				// 遍历聊天中的所有消息
				return this.getMatchingMessages(chat).length > 0;
			},
			// 获取匹配搜索文本的消息
			getMatchingMessages(chat) {
				// 如果搜索文本为空，返回空数组
				if (!this.searchText || !chat.messages) return [];

				const searchText = this.searchText.toLowerCase(); // 转为小写进行不区分大小写搜索

				// 过滤出包含搜索文本的消息
				return chat.messages.filter(message => {
					// 不搜索时间提示类型消息
					if (message.type === this.$msgType.TIP_TIME) {
						return false;
					}

					// 搜索文本消息内容
					if (message.content && typeof message.content === 'string') {
						if (message.content.toLowerCase().includes(searchText)) {
							return true;
						}
					}

					// 搜索消息标题（例如系统消息）
					if (message.title && typeof message.title === 'string') {
						if (message.title.toLowerCase().includes(searchText)) {
							return true;
						}
					}

					// 搜索图片消息的文件名或描述
					if (message.type === this.$msgType.IMAGE && message.fileName) {
						if (message.fileName.toLowerCase().includes(searchText)) {
							return true;
						}
					}

					// 搜索文件消息的文件名
					if (message.type === this.$msgType.FILE && message.fileName) {
						if (message.fileName.toLowerCase().includes(searchText)) {
							return true;
						}
					}

					// 搜索视频消息的文件名
					if (message.type === this.$msgType.VIDEO && message.fileName) {
						if (message.fileName.toLowerCase().includes(searchText)) {
							return true;
						}
					}

					return false;
				});
			},
			// 获取匹配消息的数量
			getMatchingMessagesCount(chat) {
				return this.getMatchingMessages(chat).length;
			},
			// 显示搜索结果详情
			showSearchDetail(chat, index) {
				// 记录搜索结果，准备在聊天详情页显示
				console.log("进来了--------", chat)
				uni.setStorageSync('search_detail', {
					searchText: this.searchText,
					matchedMessages: this.getMatchingMessages(chat).map(msg => msg.id || msg.sendTime)
				});

				// 导航到聊天详情页
				if (chat.type == 'SYSTEM') {
					uni.navigateTo({
						url: "/pages/chat/chat-system?chatIdx=" + index + "&isSearch=true"
					});
				} else {
					uni.navigateTo({
						url: "/pages/chat/chat-box?chatIdx=" + index + "&isSearch=true"
					});
				}
			},
			shouldShowDivider(index) {
				const chats = this.chatStore.chats;
				// 如果当前会话和下一个会话存在，且当前会话是置顶的，下一个会话不是置顶的，则显示分隔线
				return index < chats.length - 1 &&
					chats[index].isTop &&
					!chats[index + 1].isTop &&
					this.isShowChat(chats[index]) &&
					this.isShowChat(chats[index + 1]);
			},
			onSearch() {
				this.showSearch = !this.showSearch;
				this.searchText = "";
			},
			refreshUnreadBadge() {
				if (this.unreadCount > 0) {
					uni.setTabBarBadge({
						index: 0,
						text: this.unreadCount + ""
					})
				} else {
					uni.removeTabBarBadge({
						index: 0,
						complete: () => {}
					})
				}
				// Android平台调用原生API设置角标
				// #ifdef APP-PLUS
				if (uni.getSystemInfoSync().platform === 'android') {
					plus.runtime.setBadgeNumber(this.unreadCount);
				}
				// #endif
			},
			checkNotifyPermisson() {
				// #ifdef APP-PLUS
				if (plus.os.name == 'Android') { // 判断是Android
					var main = plus.android.runtimeMainActivity();
					var pkName = main.getPackageName();
					var uid = main.getApplicationInfo().plusGetAttribute("uid");
					var NotificationManagerCompat = plus.android.importClass(
						"android.support.v4.app.NotificationManagerCompat");
					//android.support.v4升级为androidx
					if (NotificationManagerCompat == null) {
						NotificationManagerCompat = plus.android.importClass(
						"androidx.core.app.NotificationManagerCompat");
					}
					var areNotificationsEnabled = NotificationManagerCompat.from(main).areNotificationsEnabled();
					// 未开通'允许通知'权限，则弹窗提醒开通，并点击确认后，跳转到系统设置页面进行设置
					if (!areNotificationsEnabled) {
						this.$refs.modal.open({
							title: '系统消息',
							content: '您还没有开启通知权限，无法接受到消息通知！',
							confirmText: '去设置',
							success: () => {
								var Intent = plus.android.importClass('android.content.Intent');
								var Build = plus.android.importClass("android.os.Build");
								//android 8.0引导
								if (Build.VERSION.SDK_INT >= 26) {
									var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
								} else if (Build.VERSION.SDK_INT >= 21) { //android 5.0-7.0
									var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra("app_package", pkName);
									intent.putExtra("app_uid", uid);
								} else { //(<21)其他--跳转到该应用管理的详情页
									var Settings = plus.android.importClass("android.provider.Settings");
									''
									intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
									var uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
									intent.setData(uri);
								}
								// 跳转到该应用的系统通知设置页
								main.startActivity(intent);

							}
						});
					}
				} else if (plus.os.name == 'iOS') { // 判断是ISO
					var isOn = undefined;
					var types = 0;
					var app = plus.ios.invoke('UIApplication', 'sharedApplication');
					var settings = plus.ios.invoke(app, 'currentUserNotificationSettings');
					if (settings) {
						types = settings.plusGetAttribute('types');
						plus.ios.deleteObject(settings);
					} else {
						types = plus.ios.invoke(app, 'enabledRemoteNotificationTypes');
					}
					plus.ios.deleteObject(app);
					isOn = (0 != types);
					if (isOn == false) {
						this.$refs.modal.open({
							title: '系统消息',
							content: '您还没有开启通知权限，无法接受到消息通知！',
							confirmText: '去设置',
							success: () => {
								var app = plus.ios.invoke('UIApplication', 'sharedApplication');
								var setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');
								plus.ios.invoke(app, 'openURL:', setting);
								plus.ios.deleteObject(setting);
								plus.ios.deleteObject(app);
							}
						});
					}
				}
				// #endif
			},
			// 显示功能菜单
			onScanLogin() {
				this.$refs.chatDropdownMenu.toggle();
			}
		},
		computed: {
			unreadCount() {
				let count = 0;
				this.chatStore.chats.forEach(chat => {
					if (!chat.delete) {
						count += chat.unreadCount;
					}
				})
				return count;
			},
			loading() {
				return this.chatStore.isLoading();
			},
			initializing() {
				return !getApp().$vm.isInit;
			}
		},
		watch: {
			unreadCount(newCount, oldCount) {
				this.refreshUnreadBadge();
			}
		},
		onLoad() {
			// 检查通知权限
			setTimeout(() => this.checkNotifyPermisson(), 3000)
		},
		onShow() {
			this.refreshUnreadBadge();
		}
	}
</script>

<style lang="scss">
	.tab-page {
		position: relative;
		display: flex;
		flex-direction: column;

		.chat-tip {
			position: absolute;
			top: 400rpx;
			padding: 50rpx;
			line-height: 50rpx;
			text-align: center;
			color: $im-text-color-lighter;
		}

		.chat-loading {
			display: block;
			width: 100%;
			height: 120rpx;
			background: white;

			color: $im-text-color-lighter;

			.loading-box {
				position: relative;
			}
		}

		.scroll-bar {
			flex: 1;
			height: 100%;
		}

		.pinned-divider {
			position: relative;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			font-size: 24rpx;
			color: $im-text-color-light;
			background-color: #f7f7f7;
			border-top: 1rpx solid #e8e8e8;
			border-bottom: 1rpx solid #e8e8e8;

			text {
				display: inline-block;
				padding: 0 20rpx;
			}
		}

		.search-result-hint {
			font-size: 24rpx;
			color: $im-color-primary;
			padding: 0 0 10rpx 100rpx;
			margin-top: -10rpx;
			display: flex;
			align-items: center;

			.search-detail-btn {
				margin-left: 10rpx;
				padding: 0 10rpx;
				color: #fff;
				background-color: $im-color-primary;
				border-radius: 20rpx;
				font-size: 22rpx;
			}
		}
	}
</style>