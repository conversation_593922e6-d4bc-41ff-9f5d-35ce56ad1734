<template>
	<!-- 若更新对话框应显示，则渲染覆盖层 -->
	<view v-if="isVisible" class="update-overlay">
		<!-- 外层用于淡入淡出过渡动画的包装容器 -->
		<view :show="isVisible" mode="fade" :duration="300" class="dialog-transition-wrapper">
			
			<!-- 若检测到有新版本，显示更新提示框 -->
			<view v-if="hasUpdate" class="update-dialog">
				<view class="dialog-header">
					<text class="dialog-title">{{ currentTitle }}</text>
				</view>
				<text class="dialog-message">{{ currentMessage }}</text>

				<!-- 操作按钮组：确认更新 / 稍后再说 -->
				<view class="button-group">
					<button @click="handleConfirm" class="action-btn confirm-btn">
						{{ props.confirmText }}
					</button>
					<button @click="handleCancel" class="action-btn cancel-btn">
						{{ props.cancelText }}
					</button>
				</view>
			</view>

			<!-- 若未检测到更新，显示加载中提示 -->
			<view v-else class="loading-state">
				<text class="loading-text">{{ currentMessage }}</text>
			</view>
		</view>
	</view>
</template>


<script setup>
import { ref, onMounted } from 'vue';

import UNI_APP from '@/.env.js'


/* ------------------------- props 默认配置 ------------------------- */
const props = {
	title: '新版本可用',
	message: '为获得更好的体验，新版本已准备就绪。立即更新吗？',
	loadingText: '正在检查更新...',
	cancelText: '稍后',
	confirmText: '立即更新',
	appId: 3, // 默认的应用ID（用于接口参数）
	apiUrl: UNI_APP.HOT_UPDATE, // 默认API地址
};

/* ------------------------- 响应式状态变量 ------------------------- */
const isVisible = ref(false); // 控制弹窗是否显示
const hasUpdate = ref(false); // 是否检测到更新版本
const currentTitle = ref(''); // 弹窗标题
const currentMessage = ref(''); // 弹窗消息内容
const currentAppVersion = ref('Unknown'); // 当前应用版本
const newAppVersion = ref(''); // 检测到的新版本
const downloadUrl = ref(''); // 新版本的下载地址

/* ------------------------- 弹窗控制方法 ------------------------- */

/**
 * 显示弹窗
 * @param {boolean} showUpdateDialog - true: 显示更新框；false: 显示加载框
 * @param {string} [customTitle] - 可选自定义标题
 * @param {string} [customMessage] - 可选自定义内容
 */
const show = (showUpdateDialog, customTitle, customMessage) => {
	hasUpdate.value = showUpdateDialog;
	currentTitle.value = customTitle || props.title;
	currentMessage.value = customMessage || (showUpdateDialog ? props.message : props.loadingText);
	isVisible.value = true;
};

/** 隐藏弹窗 */
const hide = () => {
	isVisible.value = false;
};

/* ------------------------- 获取当前版本 ------------------------- */

/**
 * 获取当前应用的版本号（支持多端）
 */
const getAppVersion = () => {
	// #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
	try {
		const info = uni.getAppBaseInfo();
		currentAppVersion.value = info.appVersion || 'Unknown';
	} catch (e) {
		console.warn('获取当前应用版本失败：', e);
		currentAppVersion.value = 'Unknown';
	}
	// #endif

	// #ifndef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
	console.warn('当前平台不支持 uni.getAppBaseInfo。');
	currentAppVersion.value = 'Unknown';
	// #endif
};

/* ------------------------- 版本号比较工具 ------------------------- */

/**
 * 比较两个版本号
 * @returns {number} 返回1表示v1新于v2，-1表示旧，0表示相等
 */
const compareVersions = (v1, v2) => {
	const parts1 = v1.split('.').map(Number);
	const parts2 = v2.split('.').map(Number);
	const maxLength = Math.max(parts1.length, parts2.length);

	for (let i = 0; i < maxLength; i++) {
		const p1 = parts1[i] || 0;
		const p2 = parts2[i] || 0;
		if (p1 > p2) return 1;
		if (p1 < p2) return -1;
	}
	return 0;
};

/* ------------------------- 核心逻辑：检查更新 ------------------------- */

/**
 * 检查是否有新版本可用，并根据情况显示弹窗
 */
const checkForUpdate = async () => {
	getAppVersion(); // 先获取当前版本

	try {
		const response = await uni.request({
			url: `${props.apiUrl}/getLatestVersion/${props.appId}`,
			method: 'GET',
		});

		const resData = response.data;

		if (resData.code === '200' && resData.data) {
			const latestVersionData = resData.data;
			const latestVersion = latestVersionData.latestVersion;
			const current = currentAppVersion.value;

			if (current === 'Unknown') {
				console.warn('版本未知，跳过更新比较。');
				uni.showToast({ title: '获取更新失败', icon: 'none', duration: 2000 });
				setTimeout(() => hide(), 2000);
				return;
			}

			// 若已是最新版，直接退出
			if (compareVersions(latestVersion, current) <= 0) {
				hide();
				return;
			}

			// 判断新版本发布时间是否已到
			const publishTime = new Date(latestVersionData.publishTime);
			const currentTime = new Date();

			if (publishTime.getTime() > currentTime.getTime()) {
				const publishTimeStr = publishTime.toLocaleString();
				hide();
				uni.showToast({
					title: `新版本将于 ${publishTimeStr} 发布。敬请期待！`,
					icon: 'none',
					duration: 3000
				});
				return;
			}

			// 版本已发布，准备显示更新提示
			newAppVersion.value = latestVersion;
			downloadUrl.value = latestVersionData.downloadUrl;

			const updateMessageFromApi = latestVersionData.updateMessage || props.message;
			const fullUpdateMessage =
				`新版本 ${newAppVersion.value} 已准备就绪，带来更好的体验。立即更新吗？\n\n更新详情:\n${updateMessageFromApi}\n\n您的当前版本: ${current}`;
			show(true, props.title, fullUpdateMessage);

		} else {
			console.error('从API获取最新版本失败：', resData.message);
			uni.showToast({ title: '获取更新失败', icon: 'none', duration: 2000 });
			setTimeout(() => hide(), 2000);
		}
	} catch (err) {
		console.error('请求失败：', err);
		uni.showToast({ title: '获取更新失败', icon: 'none', duration: 2000 });
		setTimeout(() => hide(), 2000);
	}
};

/* ------------------------- 用户点击操作 ------------------------- */

/**
 * 用户点击“立即更新”按钮，开始更新流程
 */
const handleConfirm = () => {
	if (!downloadUrl.value) {
		uni.showToast({ title: '下载链接无效。', icon: 'none', duration: 2000 });
		hide();
		return;
	}

	uni.showToast({ title: '正在下载更新...', icon: 'none', duration: 2000 });
	hide();

	// 平台特定下载处理
	// #ifdef APP-PLUS
	plus.runtime.openURL(downloadUrl.value); // APP端直接调用安装包链接
	// #endif

	// #ifdef H5
	window.open(downloadUrl.value, '_blank'); // 网页端在新标签页打开
	// #endif

	// #ifndef APP-PLUS || H5
	uni.showToast({
		title: '当前平台不支持直接更新，请前往商店下载。',
		icon: 'none',
		duration: 3000
	});
	// #endif
};

/**
 * 用户点击“稍后”按钮，关闭弹窗
 */
const handleCancel = () => {
	uni.showToast({ title: '更新已取消。', icon: 'none', duration: 2000 });
	hide();
};

/* ------------------------- 生命周期钩子 ------------------------- */
onMounted(() => {
	checkForUpdate(); // 页面加载时自动检查更新
});

/* ------------------------- 暴露方法给父组件使用 ------------------------- */
defineExpose({
	show,
	hide,
	checkForUpdate,
});
</script>


<style lang="scss" scoped>
	// 定义全局样式变量（可选，如果您的项目使用 uni.scss 等）
	// $uni-color-primary: #007aff; // UniApp 默认蓝色

	.update-overlay {
		position: fixed; // 关键：相对于视口的固定定位
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		z-index: 9999; // 确保它位于其他内容的顶部
		background-color: rgba(0, 0, 0, 0.6); // 半透明的深色背景
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.dialog-transition-wrapper {
		// uni-transition 可能会生成一个包装元素；这些样式直接应用于其内容
		// 确保内容在其内部居中
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.update-dialog {
		background-color: #fff;
		padding: 50rpx; // 增加内边距
		border-radius: 24rpx; // 更圆的圆角
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15); // 更柔和的阴影
		width: 600rpx; // 固定宽度
		max-width: 90vw; // 适应小屏幕，最大为视口宽度的90%
		text-align: center;
		box-sizing: border-box; // 确保内边距不会增加元素尺寸
	}

	.dialog-header {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}

	.dialog-title {
		font-size: 38rpx; // 更大的标题字体
		font-weight: bold;
		color: #333;
		margin-left: 15rpx; // 图标和文本之间的间距
	}

	.dialog-message {
		font-size: 30rpx; // 内容字体大小
		color: #666;
		line-height: 1.6; // 改进行高
		margin-bottom: 50rpx; // 消息和按钮组之间的间距
		display: block; // 确保它占据自己的行
		white-space: pre-wrap;
		/* 允许文本换行并保留空白，包括 \n */
	}

	.button-group {
		display: flex;
		gap: 20rpx; // 按钮之间的间距，现代 flexbox
		justify-content: center; // 按钮组居中
	}

	.action-btn {
		flex: 1; // 按钮平均分配可用空间
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 45rpx; // 圆角按钮
		font-size: 32rpx;
		font-weight: 500; // 中等字体粗细
		border: none; // 移除默认边框
		outline: none; // 移除焦点时的轮廓
		margin: 0; // 移除默认按钮外边距
		padding: 0; // 移除默认按钮内边距

		// 针对 uni-app 按钮的特定处理，以移除点击状态边框
		&::after {
			border: none;
		}
	}

	.confirm-btn {
		background-color: #409EFF; // 主色
		color: #fff;
	}

	.cancel-btn {
		background-color: #E0E0E0; // 浅灰色背景
		color: #666;
	}

	.loading-state {
		background-color: #fff;
		padding: 60rpx;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 400rpx; // 加载状态对话框的宽度更小
		max-width: 80vw;
		box-sizing: border-box;
	}

	.loading-text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #999;
		text-align: center;
		/* 文本居中 */
		white-space: pre-wrap;
		/* 允许文本换行并保留空白，包括 \n */
	}
</style>