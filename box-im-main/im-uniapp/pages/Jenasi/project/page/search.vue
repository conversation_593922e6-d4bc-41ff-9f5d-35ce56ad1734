<template>
	<view class="container" style="overflow-y: auto">
		<text class="page-title">项目与任务管理</text>
		<!--		{{ test }}-->
		<!-- 筛选区域 -->
		<view class="search-filter-area">
			<!-- 部门选择器 -->
			<view class="selector-wrapper" @click="showDepartmentModal = true">
				<text class="selector-label">部门:</text>
				<view class="picker-trigger">
					<!-- 使用  防止空内容导致布局跳动 -->
					{{ selectedDepartmentName || '请选择部门' }}
					<text class="arrow-icon">▼</text>
				</view>
			</view>

			<!-- 人员选择器 - 仅在部门选中后显示 -->
			<view v-if="selectedDepartmentId" class="selector-wrapper" @click="showEmployeeModal = true">
				<text class="selector-label">人员:</text>
				<view class="picker-trigger">
					{{ selectedEmployeeName || '请选择人员' }}
					<text class="arrow-icon">▼</text>
				</view>
			</view>
		</view>

		<!-- 数据加载/错误/无数据状态 -->
		<view v-if="isLoading" class="loading-indicator">
			<text>数据加载中，请稍候...</text>
		</view>

		<view v-if="error" class="error-message">
			<text>{{ error }}</text>
			<button @click="retryFetchProjects" class="retry-button">点击重试</button>
		</view>

		<!-- 项目列表 -->
		<view class="project-list" v-if="!isLoading && !error && projects.length > 0">
			<uni-collapse v-for="(project, projectIndex) in projects" :key="project.projectId">
				<uni-collapse-item :title="project.name || '项目已删除'" :open="project.open"
					@change="handleCollapseChange(projectIndex, $event)" show-arrow class="project-item">
					<template #title>
						<view class="project-title-content">
							<text class="project-name">{{ project.name }}</text>
							<view class="project-meta">
								<text class="task-count">{{ project.tasks.length }} 任务</text>
								<text
									:class="['project-status', getProjectCssClass(project.progressStatus)]">{{ project.progressStatus }}
								</text>
							</view>
						</view>
					</template>

					<view class="task-list">
						<view v-if="project.tasks.length === 0" class="no-tasks">
							<text>暂无任务</text>
						</view>
						<view v-for="(task, taskIndex) in project.tasks" :key="task.taskId" class="task-item"
							@click="toTask(task.taskId)">
							<text class="task-name">{{ task.name }}</text>
							<view class="task-details">
								<text :class="['task-priority', getTaskCssClass(task.status)]">{{ task.status }}</text>
								<text class="task-due-date">{{ task.dueDate }}</text>
							</view>
						</view>
					</view>
				</uni-collapse-item>
			</uni-collapse>
		</view>

		<!-- 无项目数据提示 (根据是否选择人员区分提示) -->
		<view v-if="!isLoading && !error && projects.length === 0 && selectedEmployeeId" class="no-data">
			<text>当前人员暂无项目数据</text>
		</view>
		<view v-if="!isLoading && !error && projects.length === 0 && !selectedEmployeeId" class="no-data">
			<text>请选择人员以查看项目数据</text>
		</view>


		<!-- 部门选择模态框 -->
		<view v-if="showDepartmentModal" class="modal-overlay" @click.self="showDepartmentModal = false">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择部门</text>
					<text class="modal-close" @click="showDepartmentModal = false">×</text>
				</view>
				<scroll-view scroll-y class="modal-scroll-view">
					<view v-for="dept in departments" :key="dept.id" class="modal-item"
						:class="{ 'selected': dept.id === selectedDepartmentId }" @click="selectDepartment(dept)">
						{{ dept.departmentName }}
					</view>
					<view v-if="departments.length === 0" class="no-data-tip">
						暂无部门数据
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 人员选择模态框 -->
		<view v-if="showEmployeeModal" class="modal-overlay" @click.self="showEmployeeModal = false">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择人员</text>
					<text class="modal-close" @click="showEmployeeModal = false">×</text>
				</view>
				<scroll-view scroll-y class="modal-scroll-view">
					<view v-for="emp in employees" :key="emp.id" class="modal-item"
						:class="{ 'selected': emp.id === selectedEmployeeId }" @click="selectEmployee(emp)">
						{{ emp.name }}
					</view>
					<view v-if="employees.length === 0" class="no-data-tip">
						当前部门暂无人员
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		watch
	} from 'vue';
	// 引入 uni-ui 组件
	import uniCollapse from '@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue';
	import uniCollapseItem from '@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue';
	import useUserStore from '@/store/userStore.js'
	import UNI_APP from '@/.env.js'; // 假设 .env.js 中定义了 UNI_APP.PROJECT

	const userStore = useUserStore()

	const PROJECT_BASE_API = UNI_APP.PROJECT;
	const BASE_API = UNI_APP.BASE_URL;

	// --- 数据声明 ---
	const departments = ref([]); // 部门列表
	const selectedDepartmentId = ref(null); // 选择的部门id
	const selectedDepartmentName = ref(''); // 选择的部门名称

	const employees = ref([]); // 存储部门下所有员工数据
	const selectedEmployeeId = ref(null); // 选择的员工id
	const selectedEmployeeName = ref(''); // 选择的员工名称

	const showDepartmentModal = ref(false); // 控制部门选择模态框显示
	const showEmployeeModal = ref(false); // 控制人员选择模态框显示

	// 存储项目数据
	const projects = ref([]);
	// 加载状态
	const isLoading = ref(false); // 初始状态为 false，因为需要等待用户选择
	// 错误信息
	const error = ref(null);

	// const test = ref("test");




	// --- 生命周期钩子 ---
	// --- 生命周期钩子 ---
	onMounted(async () => {
		// 页面加载时，总是先获取一次部门列表，以便用户可以随时手动选择
		await fetchDepartments();

		try {
			// 步骤 1: 获取当前用户的员工信息
			const userId = userStore.userInfo.id;
			if (!userId) {
				console.log("未获取到用户ID，将等待用户手动选择。");
				return; // 结束自动查询，等待用户手动操作
			}

			const employeeId = await getEmployeeIdBySixinId(userId);
			if (!employeeId) {
				console.log("当前用户未关联员工信息，将等待用户手动选择。");
				return;
			}

			const employeeInfo = await getEmployeeInfoByEmployeeId(employeeId);
			if (!employeeInfo || !employeeInfo.id) {
				console.log("无法获取员工详细信息，将等待用户手动选择。");
				return;
			}

			// 解构 与 重命名
			const {
				id: currentEmployeeId,
				name: currentEmployeeName,
				departmentId: currentDepartmentId
			} = employeeInfo;

			// 步骤 2: 设置部门ID。这将触发部门侦听器去获取该部门的员工列表
			selectedDepartmentId.value = currentDepartmentId;

			// 步骤 3: 使用一个一次性的侦听器，等待员工列表(employees)被成功加载
			const stopWaitingForEmployees = watch(() => employees.value, (newEmployeeList) => {
				// 确认员工列表已经被填充
				if (newEmployeeList && newEmployeeList.length > 0) {
					// 现在可以安全地设置选中的员工ID，这将触发员工侦听器去获取项目数据
					selectedEmployeeId.value = currentEmployeeId;
					// 同时手动设置员工姓名，确保UI立即更新，避免因异步导致短暂显示"请选择人员"
					selectedEmployeeName.value = currentEmployeeName;

					// 任务完成，立即注销这个一次性侦听器，避免不必要的性能开销
					stopWaitingForEmployees();
				}
			}, {
				// 使用 deep: true 确保即使数组内部变化也能被监听到
				29: true
			});

			// 添加一个超时保护，以防因网络或数据问题导致员工列表一直不更新，从而使侦听器永久存在
			setTimeout(() => {
				stopWaitingForEmployees();
			}, 5000); // 5秒后自动注销

		} catch (e) {
			error.value = '自动查询用户信息失败，请手动选择。';
			console.error("初始化自动查询失败:", e);
		}
	});

	// --- 侦听器 ---
	// 侦听部门ID变化，更新部门名称并获取员工
	watch(selectedDepartmentId, (newVal, oldVal) => { // 增加了 oldVal 参数
		if (newVal) {
			const selectedDept = departments.value.find(dept => dept.id === newVal);
			if (selectedDept) {
				selectedDepartmentName.value = selectedDept.departmentName;
			}

			selectedEmployeeId.value = null;
			selectedEmployeeName.value = '';
			employees.value = [];

			// 总是清空当前员工列表并获取新的列表
			employees.value = [];
			fetchEmployeesByDepartmentId(newVal);
		} else {
			// 清除部门选择时，也清除人员相关信息和项目列表
			selectedDepartmentName.value = '';
			employees.value = [];
			selectedEmployeeId.value = null;
			selectedEmployeeName.value = '';
			showEmployeeModal.value = false;
			projects.value = [];
		}
	}, {
		immediate: false
	});

	// 侦听员工ID变化，更新员工名称并获取项目
	watch(selectedEmployeeId, (newVal) => {
		if (newVal) {
			const selectedEmp = employees.value.find(emp => emp.id === newVal); // 根据员工ID查找员工对象
			if (selectedEmp) {
				selectedEmployeeName.value = selectedEmp.name;
				selectTaskTreeByEmployeeId(newVal); // 根据选中人员ID获取项目数据
			}
		} else {
			selectedEmployeeName.value = '';
			projects.value = []; // 清空项目列表
			isLoading.value = false; // 确保加载状态关闭
			error.value = null; // 清除错误信息
		}
	}, {
		immediate: false
	}); // 不立即执行

	// --- API 请求 ---


	/**
	 * 根据思信id获取员工id
	 */
	const getEmployeeIdBySixinId = async (sixinId) => {
		console.log("开始请求")
		try {
			const res = await uni.request({
				url: `${BASE_API}/api/util/employeeId/${sixinId}`,
				method: 'GET',
			});
			return res.data;

		} catch (e) {
			console.log(e)
		}
	}


	/**
	 * 根据员工id 获取 员工姓名 部门id  部门名称
	 */
	const getEmployeeInfoByEmployeeId = async (employeeId) => {
		try {
			const res = await uni.request({
				url: `${PROJECT_BASE_API}/employees/selectEmployeesDepartmentNameById/${employeeId}`,
				method: 'GET'
			});

			if (res.data.code === "200") {
				return res.data.data;
			} else {
				uni.showToast({
					title: '获取员工信息失败',
					icon: 'none',
				});
			}
		} catch (e) {
			console.log(e)
			uni.showToast({
				title: '获取员工信息失败',
				icon: 'none',
			});
		}
	}

	/**
	 * 从API获取所有部门数据
	 */
	const fetchDepartments = async () => {
		try {
			const res = await uni.request({
				url: `${PROJECT_BASE_API}/departments/selectAll`,
				method: 'GET',
			});
			if (res.statusCode === 200 && res.data.code === '200') {
				departments.value = res.data.data;
				// 如果只有一个部门，则自动选中
				if (departments.value.length === 1) {
					selectDepartment(departments.value[0]);
				}
			} else {
				console.error('Failed to fetch departments:', res.data.message);
				uni.showToast({
					title: '获取部门失败',
					icon: 'none',
				});
			}
		} catch (error) {
			console.error('Network error fetching departments:', error);
			uni.showToast({
				title: '网络错误，请稍后再试',
				icon: 'none',
			});
		}
	};

	/**
	 * 根据部门ID获取员工数据
	 * @param {number} departmentId - 部门ID
	 */
	const fetchEmployeesByDepartmentId = async (departmentId) => {
		try {
			const res = await uni.request({
				url: `${PROJECT_BASE_API}/employees/selectAllByDepartmentId/${departmentId}`,
				method: 'GET',
			});
			if (res.statusCode === 200 && res.data.code === '200') {
				employees.value = res.data.data;
				// 如果获取到员工数据，且人员弹窗未打开，则自动显示人员弹窗
				if (employees.value.length > 0) {
					if (!showEmployeeModal.value) {
						showEmployeeModal.value = true;
					}
				} else {
					uni.showToast({
						title: '当前部门暂无人员',
						icon: 'none',
						duration: 1500
					});
					showEmployeeModal.value = false; // 确保没有人员时弹窗不显示
				}
			} else {
				console.error('Failed to fetch employees:', res.data.message);
				uni.showToast({
					title: '获取人员失败',
					icon: 'none',
				});
				employees.value = []; // 清空人员列表
				showEmployeeModal.value = false; // 关闭人员弹窗
			}
		} catch (error) {
			console.error('Network error fetching employees:', error);
			uni.showToast({
				title: '网络错误，请稍后再试',
				icon: 'none',
			});
			employees.value = []; // 清空人员列表
			showEmployeeModal.value = false; // 关闭人员弹窗
		}
	};


	/**
	 * 根据员工ID获取项目和任务数据
	 * @param {number} employeeId - 员工ID
	 */
	const selectTaskTreeByEmployeeId = async (employeeId) => {
		showEmployeeModal.value = false;
		console.log("开始查询")
		if (!employeeId) {
			projects.value = [];
			isLoading.value = false;
			error.value = null;
			return;
		}

		isLoading.value = true;
		error.value = null; // 重置错误信息
		try {
			const response = await uni.request({
				url: `${PROJECT_BASE_API}/project/selectTaskTreeByEmployeeId/${employeeId}`,
				method: 'GET'
			});

			if (response.statusCode === 200 && response.data && response.data.code === "200") {
				projects.value = mapApiDataToProjects(response.data.data);
			} else {
				error.value = response.data?.message || '获取数据失败，请稍后重试。';
				console.error("API Error:", response.data);
			}
		} catch (e) {
			error.value = '网络请求失败，请检查网络连接或服务器状态。';
			console.error("Network Error:", e);
		} finally {
			isLoading.value = false;
		}
	};

	/**
	 * 错误状态下点击重试按钮的逻辑
	 */
	const retryFetchProjects = () => {
		if (selectedEmployeeId.value) {
			selectTaskTreeByEmployeeId(selectedEmployeeId.value);
		} else if (selectedDepartmentId.value) {
			fetchEmployeesByDepartmentId(selectedDepartmentId.value);
		} else {
			fetchDepartments();
		}
	};

	/**
	 * 格式化日期字符串，只保留年月日
	 * (从第一个组件复制)
	 */
	const formatDate = (dateString) => {
		if (!dateString) return '';
		try {
			const date = new Date(dateString);
			if (!isNaN(date.getTime())) {
				return date.toISOString().split('T')[0];
			}
			const parts = dateString.split('T')[0].split('-');
			if (parts.length === 3) {
				return `${parts[0]}-${parts[1]}-${parts[2]}`;
			}
			return dateString;
		} catch (e) {
			console.error("日期格式化失败:", e);
			return dateString;
		}
	};

	/**
	 * 将API数据映射到前端所需的格式
	 * (从第一个组件复制)
	 */
	const mapApiDataToProjects = (apiData) => {
		return apiData.map(project => ({
			projectId: project.projectId,
			name: project.projectName,
			progressStatus: project.progressStatus,
			open: false,
			tasks: project.tacksTreeVoList ? project.tacksTreeVoList.map(task => ({
				taskId: task.taskId,
				name: task.taskName,
				status: task.status,
				dueDate: formatDate(task.updatedAt),
			})) : [],
		}));
	};

	/**
	 * 根据项目状态返回对应的 CSS 类名。
	 * (从第一个组件复制)
	 */
	const getProjectCssClass = (status) => {
		switch (status) {
			case '未开始':
				return 'pending';
			case '进行中':
			case '已暂停':
				return 'in_progress';
			case '延期':
				return 'delayed';
			case '已完成':
			case '正常':
				return 'completed';
			case '已取消':
				return 'cancelled';
			default:
				return '';
		}
	};

	/**
	 * 根据任务状态返回对应的 CSS 类名。
	 * (从第一个组件复制)
	 */
	const getTaskCssClass = (status) => {
		switch (status) {
			case '草稿':
			case '待处理':
				return 'pending';
			case '进行中':
				return 'in_progress';
			case '暂停/挂起':
				return 'paused';
			case '已归档':
			case '已完成':
				return 'completed';
			case '失败':
			case '已取消':
			case '已拒绝':
				return 'failed';
			default:
				return '';
		}
	};

	// --- 模态框选择逻辑 ---

	/**
	 * 从模态框中选择部门。
	 * @param {Object} dept - 选中的部门对象
	 */
	const selectDepartment = (dept) => {
		selectedDepartmentId.value = dept.id;
		showDepartmentModal.value = false; // 关闭部门模态框
		// 人员模态框的显示逻辑已在 fetchEmployeesByDepartmentId 中处理
	};

	/**
	 * 从模态框中选择员工。
	 * @param {Object} emp - 选中的员工对象
	 */
	const selectEmployee = (emp) => {
		selectedEmployeeId.value = emp.id;
		showEmployeeModal.value = false; // 关闭人员模态框
		// 项目获取逻辑已由 selectedEmployeeId 侦听器处理
	};

	/**
	 * 处理折叠面板的展开/收起事件。
	 * (从第一个组件复制)
	 */
	const handleCollapseChange = (index, isOpen) => {
		if (projects.value[index]) {
			projects.value[index].open = isOpen;
		}
	};

	/**
	 * 跳转到任务详情页。
	 * (从第一个组件复制)
	 */
	const toTask = (taskId) => {
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/plan/plan?id=${taskId}`
		});
	};
</script>

<style lang="scss" scoped>
	// 定义颜色变量 (从第一个组件复制)
	$primary-color: #42b983; // 绿色，常用于积极、普通优先级
	$success-color: #28a745; // 成功绿色
	$warning-color: #ffc107; // 警告黄色
	$danger-color: #dc3545; // 危险红色，常用于高优先级
	$info-color: #17a2b8; // 信息蓝色，常用于进行中
	$purple-color: #6f42c1; // 紫色，用于暂停/挂起
	$orange-red-color: #fd7e14; // 橙红色，用于延期

	$text-color-dark: #34495e; // 深色文本
	$text-color-light: #6c757d; // 浅色文本
	$border-color: rgba(0, 0, 0, 0.3); // 边框颜色
	$background-light: #f5f7fa; // 页面浅背景
	$card-background: #ffffff; // 卡片背景
	$shadow-light: rgba(0, 0, 0, 0.08); // 轻微阴影
	$shadow-modal: rgba(0, 0, 0, 0.2); // 模态框阴影

	.container {
		margin-top: 5vh;
		padding: 30rpx;
		min-width: 90vw;
		background-color: $background-light;
		min-height: 100vh;
		box-sizing: border-box;
	}

	.page-title {
		font-size: 48rpx;
		font-weight: bold;
		color: $text-color-dark;
		margin-bottom: 30rpx;
		text-align: center;
		display: block;
		letter-spacing: 2rpx;
	}

	.loading-indicator,
	.error-message,
	.no-data {
		text-align: center;
		padding: 40rpx;
		color: $text-color-light;
		font-size: 30rpx;
	}

	.error-message {
		color: $danger-color;

		.retry-button {
			margin-top: 20rpx;
			background-color: $primary-color;
			color: #fff;
			font-size: 28rpx;
			padding: 10rpx 30rpx;
			border-radius: 10rpx;
			border: none;
		}
	}

	/* --- 筛选区域样式 --- */
	.search-filter-area {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		width: 100%;
		padding: 20rpx 0;
		margin-bottom: 30rpx;
		gap: 20rpx;
	}

	.selector-wrapper {
		display: flex;
		align-items: center;
		flex-basis: calc(50% - 10rpx); // 每行两个，中间有 20rpx 的间隔
		max-width: 320rpx; // 限制最大宽度，在宽屏上不会过长
		box-sizing: border-box;

		@media (max-width: 600px) {
			// 小屏幕上单列布局
			flex-basis: 100%;
			max-width: none;
		}
	}

	.selector-label {
		font-size: 28rpx;
		color: $text-color-light;
		margin-right: 15rpx;
		white-space: nowrap; // 防止标签换行
	}

	.picker-trigger {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx; // 增加高度以便更好的点击体验
		padding: 0 30rpx;
		border: 1rpx solid $border-color;
		border-radius: 16rpx; // 与卡片风格统一的圆角
		font-size: 30rpx;
		background-color: $card-background;
		color: $text-color-dark;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		box-shadow: 0 4rpx 16rpx $shadow-light; // 柔和阴影
	}

	.arrow-icon {
		font-size: 24rpx;
		color: $text-color-light;
		margin-left: 10rpx;
		transform: rotate(90deg); // 默认向右的箭头
	}

	/* --- 模态框样式 --- */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.modal-content {
		background-color: $card-background;
		border-radius: 16rpx; // 与卡片风格统一的圆角
		width: 80%;
		max-height: 70%;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx $shadow-modal; // 模态框更强的阴影
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 1rpx solid $border-color;
	}

	.modal-title {
		font-size: 38rpx;
		font-weight: bold;
		color: $text-color-dark;
	}

	.modal-close {
		font-size: 48rpx; // 放大关闭按钮以便更好点击
		color: $text-color-light;
		padding: 0 10rpx;
	}

	.modal-scroll-view {
		flex: 1;
		padding: 20rpx 0;
		box-sizing: border-box;
    overflow: auto;
	}

	.modal-item {
		padding: 24rpx 40rpx;
		font-size: 30rpx;
		color: $text-color-dark;
		border-bottom: 1rpx solid lighten($border-color, 5%);
	}

	.modal-item:last-child {
		border-bottom: none;
	}

	.modal-item.selected {
		background-color: lighten($primary-color, 45%); // 选中项的浅色主色背景
		color: $primary-color; // 选中项的主色文本
		font-weight: 500;
	}

	.no-data-tip {
		padding: 40rpx; // 与其他无数据提示统一
		text-align: center;
		color: $text-color-light;
		font-size: 30rpx;
	}

	/* --- 项目列表样式 (从第一个组件复制) --- */
	.project-list {
		.project-item {
			margin-bottom: 20rpx;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 16rpx $shadow-light;

			/* 覆盖 uni-collapse-item 的内部样式 */
			::v-deep .uni-collapse-item__wrap {
				padding: 0 !important;
			}

			::v-deep .uni-collapse-item__wrap-content {
				padding: 0 !important;
			}

			::v-deep .uni-collapse-item__title {
				padding: 30rpx 40rpx;
				background-color: $card-background;
				border-bottom: none !important;
				display: flex;
				align-items: center;
			}

			::v-deep .uni-collapse-item__arrow {
				color: $text-color-light !important;
				font-size: 38rpx !important;
				margin-left: 20rpx;
			}
		}
	}

	.project-title-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex: 1;
		margin-right: 20rpx;
	}

	.project-name {
		font-size: 34rpx;
		font-weight: bold;
		color: $text-color-dark;
		flex: 1;
		margin-right: 20rpx;
		word-break: break-all;
	}

	.project-meta {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.task-count {
		font-size: 26rpx;
		color: $text-color-light;
		background-color: #eef2f7;
		padding: 8rpx 14rpx;
		border-radius: 14rpx;
		font-weight: 500;
	}

	.project-status {
		font-size: 26rpx;
		padding: 8rpx 14rpx;
		border-radius: 14rpx;
		font-weight: 500;
		min-width: 100rpx;
		text-align: center;
		white-space: nowrap;

		&.pending {
			background-color: lighten($warning-color, 38%);
			color: darken($warning-color, 18%);
		}

		&.in_progress {
			background-color: lighten($info-color, 38%);
			color: darken($info-color, 18%);
		}

		&.delayed {
			background-color: lighten($orange-red-color, 38%);
			color: darken($orange-red-color, 18%);
		}

		&.completed {
			background-color: lighten($success-color, 38%);
			color: darken($success-color, 18%);
		}

		&.cancelled {
			background-color: lighten($danger-color, 38%);
			color: darken($danger-color, 18%);
		}
	}

	.task-list {
		padding: 0 40rpx 20rpx;
		background-color: #fcfcfc;
		border-top: 1rpx solid lighten($border-color, 5%);
	}

	.no-tasks {
		padding: 30rpx 0;
		text-align: center;
		color: $text-color-light;
		font-size: 28rpx;
	}

	.task-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx dashed lighten($border-color, 10%);

		&:last-child {
			border-bottom: none;
		}
	}

	.task-name {
		font-size: 30rpx;
		color: $text-color-dark;
		flex: 1;
		margin-right: 20rpx;
		line-height: 1.4;
	}

	.task-details {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.task-priority {
		font-size: 24rpx;
		padding: 6rpx 10rpx;
		border-radius: 10rpx;
		font-weight: 500;
		min-width: 60rpx;
		text-align: center;
		white-space: nowrap;

		&.pending {
			background-color: lighten($warning-color, 38%);
			color: darken($warning-color, 18%);
		}

		&.in_progress {
			background-color: lighten($info-color, 38%);
			color: darken($info-color, 18%);
		}

		&.paused {
			background-color: lighten($purple-color, 38%);
			color: darken($purple-color, 18%);
		}

		&.completed {
			background-color: lighten($success-color, 38%);
			color: darken($success-color, 18%);
		}

		&.failed {
			background-color: lighten($danger-color, 38%);
			color: darken($danger-color, 18%);
		}
	}

	.task-due-date {
		font-size: 24rpx;
		color: $text-color-light;
		white-space: nowrap;
	}

	/* 箭头旋转动画 (从第一个组件复制) */
	::v-deep .uni-collapse-item__wrap-content {
		transition: max-height 0.3s ease-out, padding 0.3s ease-out;
		overflow: hidden;
	}

	::v-deep .uni-collapse-item__arrow {
		transition: transform 0.3s ease;

		&.uni-collapse-item__arrow-active {
			transform: rotate(90deg);
		}
	}
</style>