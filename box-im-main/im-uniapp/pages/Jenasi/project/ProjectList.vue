<template>
	<view class="container">

		<!-- {{test}} -->
		<!-- 项目列表 -->
		<view class="header">
			项目列表
		</view>

		<!-- 将滚动区域设置为 flex: 1 让其填充剩余空间 -->
		<scroll-view scroll-y="true" class="project-list-scroll" @touchstart="onClosePopup()">
			<view v-if="projectList.length == 0" class="empty-list">
				<text>暂无项目数据</text>
			</view>
			<view v-for="project in projectList" :key="project.projectId" class="project-card"
				@longpress.stop="onLongPress(project)">
				<view @click="goToProjectDetail(project.projectId)">
					<view class="card-header">
						<text class="project-name">{{ project.projectName }}</text>
						<!-- 根据进度状态显示不同样式 -->
						<text class="project-status" :class="getStatusClass(project.progressStatus)">
							{{ project.progressStatus || '未知状态' }}
						</text>
					</view>
					<view class="card-body">
						<text class="project-desc">{{ project.projectDesc }}</text>
						<view class="date-info">
							<text>开始: {{ formatDate(project.startDate, 'YYYY-MM-DD') }}</text>
							<!-- 结束日期只显示年月日 -->
							<text>结束: {{ formatDate(project.endDate, 'YYYY-MM-DD') }}</text>
						</view>
						<view class="progress-info">
							<text>进度: {{ project.currentProgress || '0%' }}</text>
							<text>负责人: {{ project.managerName || '未知' }}</text>
						</view>
						<!-- 如果需要，可以在这里添加一个简单的进度条 -->
						<!-- 添加 class 用于 CSS 美化 -->
						<progress class="project-progress" :percent="parseFloat(project.currentProgress)" active
							stroke-width="6" />

					</view>
					<!-- 长按弹出的小框 -->


					<!-- 长按-->
					<view v-show="popup.show && project==popup.data">
						<button @click.stop="onPopupSelect(project)" style="background: red">
							删除
						</button>
					</view>

				</view>
				<!-- 可以在 footer 添加更多信息 -->
				<!-- <view class="card-footer">
             <text>创建于: {{ formatDate(project.createdAt, 'YYYY-MM-DD HH:mm') }}</text>
        </view> -->
			</view>
		</scroll-view>

		<!-- 新增项目入口图标 (浮动按钮) -->
		<view class="add-button" @click="goToAddProject">
			<text class="add-icon">+</text>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue'; // 从 vue 中导入 ref 用于创建响应式数据
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app'; // 从 uni-app 中导入 onLoad 生命周期钩子

	import UNI_APP from '@/.env.js'

	const BASE_API = UNI_APP.PROJECT



	const test = ref("test")

	// 1. 响应式数据 (对应 Options API 的 data)
	// 使用 ref 创建一个响应式数组
	const projectList = ref([]);


	// 长按
	const popup = ref({
		show: false,
		data: null // 当前长按的项目
	})
	// 显示
	function onLongPress(project) {
		popup.value = {
			show: true,
			data: project
		}
	}
	// 关闭  判断事件来源是否是按钮内部
	const onClosePopup = () => {
		if (popup.value.show = true) {
			setTimeout(() => {
				popup.value = {
					show: false,
					data: null
				}
			}, 150)
		}

	}

	//删除
	const onPopupSelect = (project) => {
		uni.showModal({
			title: '确认删除',
			content: `确定要删除项目「${project.projectName}」吗？`,
			confirmText: '删除',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定，执行删除逻辑
					// test.value = project
					deleteProject(project.projectId);
				} else {
					// 用户点击取消，不执行操作
				}
			}
		});
	}

const deleteProject = (id) => {
  uni.request({
    url: `${BASE_API}/project/delete/${id}`,
    success: (res) => {
      const result = res.data;
      if (result.code === 200) {
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        uni.showToast({
          title: result.message || '删除失败',
          icon: 'none'
        });
      }
    },
    fail: (err) => {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    },
    complete: () => {
      fetchProjectList(); // 重新拉取数据
    }
  });
};



	// 2. 方法 (对应 Options API 的 methods)
	// 将方法声明为常规的 JavaScript 函数
	const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm') => {
		if (!dateString) return '待定';
		try {
			const date = new Date(dateString);
			// 检查日期是否有效
			if (isNaN(date.getTime())) return '无效日期';

			const year = date.getFullYear();
			const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份从 0 开始
			const day = ('0' + date.getDate()).slice(-2);
			const hours = ('0' + date.getHours()).slice(-2);
			const minutes = ('0' + date.getMinutes()).slice(-2);

			if (format == 'YYYY-MM-DD') {
				return `${year}-${month}-${day}`;
			} else if (format == 'YYYY-MM-DD HH:mm') {
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}

			// 如果有其他格式需求可以继续添加else if
			return dateString; // 默认返回原始字符串或根据需要返回特定格式
		} catch (e) {
			console.error("格式化出错:", e);
			return '格式化失败';
		}
	};

	const getStatusClass = (status) => {
		switch (status) {
			case '进行中':
			case '正常':
				return 'status-normal'; // 绿色
			case '计划延迟':
			case '延期':
			case '异常': // 增加异常状态
				return 'status-delayed'; // 红色
			case '已完成':
			case '关闭': // 增加关闭状态
				return 'status-completed'; // 灰色
			case '未开始':
				return 'status-not-started'; // 黄色
			default:
				// 对于未知状态，可以使用默认样式或一个特定的未知状态样式
				return 'status-unknown'; // 例如，浅灰色背景，深色文字
		}
	};

	// 点击项目卡片跳转到详情页
	const goToProjectDetail = (projectId) => {
		// console.log('点击了项目:', projectId);
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/ProjectDetail?id=${projectId}`
		});
	};

	// 点击新增按钮跳转到新增项目页面
	const goToAddProject = () => {
		console.log('点击了新增项目按钮');
		uni.navigateTo({
			url: '/pages/Jenasi/project/add/add'
		});
	};

	// 获取项目列表数据
	const fetchProjectList = () => {
		console.log('正在获取项目列表数据...');
		uni.request({
			url: `${UNI_APP.PROJECT}/project/selectAll`, // 请确保这个URL是可访问的
			success(res) {
				console.log('获取项目列表成功:', res);
				const result = res.data; // 假设返回的数据结构包含 code, message, data
				if (result.code == 200) {
					projectList.value = result.data;
				} else {
					// 处理接口返回错误的情况
					projectList.value = []; // 清空列表
					uni.showToast({
						title: result.message || '获取项目列表失败',
						icon: 'none'
					});
				}
			},
			fail(err) {
				// 处理网络请求失败的情况
				console.error('请求项目列表失败:', err);
				projectList.value = []; // 清空列表
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				});
			}
		});
	};

	// 3. 生命周期钩子 (对应 Options API 的 onLoad/onShow)
	// 使用导入的 onLoad 和 onShow 函数
	onLoad(() => {
		// 页面加载时调用获取数据的函数
		fetchProjectList();
	});

	onShow(() => {
		// 页面显示时也刷新数据，确保从详情页返回后看到最新状态
		fetchProjectList();
	});

	// 在 <script setup> 中，你不需要手动导出 projectList 或方法，它们会自动暴露给模板使用。
</script>

<style scoped>
	/* 使用rpx进行尺寸适配 */

	/* 页面容器样式，设置为 flex 布局 */
	.container {
		display: flex;
		flex-direction: column;
		/* 垂直布局 */
		min-height: 100vh;
		/* 确保容器至少占满整个视口高度 */
		background-color: #f9f9f9;
		/* 柔和的背景色 */
		box-sizing: border-box;
		/* 内边距和边框不会增加元素总宽高 */
		padding: 0 20rpx 20rpx;
		/* 容器左右和底部内边距，顶部由 header 负责 */
	}

	/* 顶部标题区域 */
	.header {
		display: flex;
		justify-content: center;
		align-items: flex-end;
		height: 6vh;
		padding-bottom: 10rpx;
		font-size: 36rpx;
		/* 标题字号 */
		font-weight: bold;
		color: #333;
		/* 标题颜色 */
		flex-shrink: 0;
		/* 不允许收缩 */
	}

	/* 项目列表滚动区域 */
	.project-list-scroll {
		flex: 1;
		/* 让滚动区域填充父容器（.container）的剩余空间 */
		overflow-y: auto;
		/* 启用垂直滚动 */
		/* padding-top: 10rpx; */
		/* 如果需要顶部内边距 */
		padding-bottom: 120rpx;
		/* 增加底部内边距，避免列表内容被浮动按钮遮挡 */
	}

	/* 空项目提示样式 */
	.empty-list {
		text-align: center;
		padding-top: 100rpx;
		color: #aaa;
		font-size: 28rpx;
		font-weight: bold;
	}

	/* 项目卡片样式 */
	.project-card {
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		/* 更柔和的阴影 */
		display: flex;
		flex-direction: column;
		overflow: hidden;
		/* 确保内部元素不会溢出圆角 */
	}

	/* 卡片头部样式 */
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
		padding-bottom: 10rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.project-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		flex-grow: 1;
		/* 项目名称可以占据更多空间 */
		margin-right: 20rpx;
		word-break: break-word;
	}

	.project-status {
		font-size: 24rpx;
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
		color: #fff;
		/* 默认渐变背景 */
		text-align: center;
		white-space: nowrap;
		flex-shrink: 0;
		/* 状态标签不收缩 */
	}

	/* 进度状态颜色 */
	.status-normal {
		background-color: #28a745;
		/* 绿色 */
		color: #fff;
	}

	.status-delayed {
		background-color: #dc3545;
		/* 红色 */
		color: #fff;
	}

	.status-completed {
		background-color: #6c757d;
		/* 灰色 */
		color: #fff;
	}

	.status-not-started {
		background-color: #ffc107;
		/* 黄色 */
		color: #333;
		/* 深色字体提高可读性 */
	}

	.status-unknown {
		background-color: #e9ecef;
		/* 浅灰色 */
		color: #555;
		/* 深一些的字体 */
	}


	/* 卡片主体内容 */
	.card-body {
		font-size: 28rpx;
		color: #555;
		line-height: 1.6;
	}

	/* 项目描述 */
	.project-desc {
		margin-bottom: 15rpx;
		color: #666;
		line-height: 1.6;
		display: -webkit-box;
		-webkit-line-clamp: 3;
		/* 增加文本显示行数 */
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	/* 日期信息和进度信息 */
	.date-info,
	.progress-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;
		font-size: 26rpx;
		/* 稍微小一点的字号 */
		color: #777;
	}

	.date-info text,
	.progress-info text {
		width: 49%;
		/* 微调宽度避免换行 */
		text-align: left;
		word-break: break-word;
		overflow: hidden;
		/* 确保文本不会超出分配的宽度 */
		text-overflow: ellipsis;
		white-space: nowrap;
		/* 尝试不换行，结合 ellipsis */
	}

	/* == 美化进度条样式 == */
	.project-progress {
		/* 添加圆角效果 */
		border-radius: 30rpx;
		/* 调整数值以改变圆角大小 */
		overflow: hidden;
		/* 确保内部元素（进度条填充）的圆角也能正确显示，尽管效果可能因平台而异 */
		margin-top: 15rpx;
		/* 在进度条上方添加一些间距，使其与上方文字分开 */
		margin-bottom: 5rpx;
		/* 在进度条下方添加一些间距 */

		/* 注意：直接通过 CSS 修改轨道颜色和填充颜色通常不可靠，
		   uni-app推荐使用 activeColor 和 backgroundColor 属性。
		   以下 CSS 规则可能在某些环境下有效，但在其他环境下无效。
		*/
		/*
		background-color: #e0e0e0 !important; // 尝试设置轨道背景色
		*/
	}

	/*
	 * == 进一步美化（可能需要 deep selectors，效果不保证）==
	 * 如果需要更细致的控制，可能需要使用深度选择器，但这通常不推荐
	 * 或依赖于组件的内部 DOM 结构，且兼容性有限。
	 * 以下代码仅为示例，实际项目中需谨慎使用并测试。
	 *
	 * .project-progress /deep/ .uni-progress-bar {
	 *     border-radius: 30rpx; // 应用圆角到轨道容器
	 * }
	 *
	 * .project-progress /deep/ .uni-progress-inner-bar {
	 *      border-radius: 30rpx; // 应用圆角到填充条
	 *      background-color: #4CAF50 !important; // 尝试设置填充颜色 (示例绿色)
	 * }
	 *
	 * 对于微信小程序，可能需要使用 ::v-deep 或其他方式。
	 *
	 * ::v-deep .project-progress .uni-progress-bar {
	 *     border-radius: 30rpx;
	 * }
	 *
	 * ::v-deep .project-progress .uni-progress-inner-bar {
	 *      border-radius: 30rpx;
	 *      background-color: #4CAF50 !important;
	 * }
	 */
	/* == 美化进度条样式结束 == */


	/* 浮动按钮样式 */
	.add-button {
		position: fixed;
		bottom: 20vh;
		right: 60rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: #28a745;
		/* 绿色背景 */
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
		z-index: 100;
		cursor: pointer;
		transition: transform 0.3s ease;
		/* 添加平滑过渡效果 */
	}

	.add-button:active {
		/* 点击时的效果 */
		transform: scale(0.95);
		/* 添加点击缩小效果 */
		opacity: 0.9;
	}


	.add-icon {
		font-size: 60rpx;
		color: #fff;
		line-height: 1;
	}
</style>