<template>
	<view class="page-container" @touchstart="onClosePopup()">

		<view
			style="display: flex; justify-content: center; align-items: flex-end; height: 7vh; padding-bottom: 10rpx;">
			项目详情
		</view>

		<!-- 加载中状态 -->
		<view v-if="loading" class="loading-state">
			<uni-icons type="spinner-cycle" size="30" color="#007aff"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>


		<!-- 项目详情内容 -->
		<scroll-view v-else-if="projectDetail" scroll-y="true" class="detail-scroll-view">
			<!-- 头部信息区 & 修改入口 -->
			<view class="card header-card">
				<view class="project-title-row">
					<text class="project-name">{{ projectDetail.projectName || '未知项目' }}</text>
					<!-- 修改图标入口 -->
					<view class="action-icon edit-icon" @click="editProject(projectDetail.projectId)">
						<uni-icons type="compose" size="24" color="#1890ff"></uni-icons> <!-- 统一为主要蓝色 -->
					</view>
				</view>
				<!-- 状态放在标题下方，更突出描述 -->
				<view class="project-status-row">
					<!-- 使用 getStatusInfo 获取 class 和 text -->
					<view :class="['status-badge', getStatusInfo(projectDetail.progressStatus).class]">
						<text>{{ getStatusInfo(projectDetail.progressStatus).text }}</text>
					</view>
				</view>
				<text class="project-desc">{{ projectDetail.projectDesc || '暂无项目描述' }}</text>
			</view>

			<!-- 关键信息区 -->
			<view class="card info-card">
				<text class="card-title">关键信息</text>
				<view class="info-row">
					<uni-icons type="calendar" size="18" color="#666"></uni-icons>
					<text class="label">时间范围:</text>
					<text class="value">{{ formatDate(projectDetail.startDate,'YYYY-MM-DD') }} 至
						{{ formatDate(projectDetail.endDate,'YYYY-MM-DD') }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="person-filled" size="18" color="#666"></uni-icons>
					<text class="label">管理员:</text>
					<text class="value">{{ projectDetail.managerName || '未知' }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="chatbubble-filled" size="18" color="#666"></uni-icons>
					<text class="label">管理员微信:</text>
					<text class="value">{{ projectDetail.managerWechat || '未提供' }}</text>
					<button v-if="projectDetail.managerWechat" size="mini" type="primary"
						@click="copyWechat(projectDetail.managerWechat)" class="copy-btn">复制</button>
				</view>
			</view>


			<!-- 项目进度信息区 -->
			<view class="card progress-card">
				<text class="card-title">项目进度</text>
				<view class="progress-item">
					<text class="label">当前进度:</text>
					<text class="value progress-percentage">{{ projectDetail.currentProgress || '0' }}%</text>
				</view>
				<!-- 进度条 - 使用 getStatusInfo 获取 progressColor -->
				<view class="progress-bar-container">
					<progress class="progress-bar" :percent="parseFloat(projectDetail.currentProgress) || 0" show-info
						stroke-width="3" :activeColor="getStatusInfo(projectDetail.progressStatus).progressColor"
						border-radius="40"></progress>
				</view>
				<view class="info-row progress-info-row">
					<uni-icons type="calendar" size="18" color="#666"></uni-icons>
					<text class="label">计划进度:</text>
					<text class="value">{{ projectDetail.plannedProgress || '0' }}%</text>
				</view>
				<view class="separator"></view>
				<view class="info-row progress-info-row">
					<uni-icons type="notification" size="18" color="#666"></uni-icons>
					<text class="label">进度说明:</text>
					<text class="value">{{ projectDetail.progressDesc || '暂无说明' }}</text>
				</view>
				<view class="separator"></view>
				<view class="info-row timestamp-row">
					<uni-icons type="loop" size="16" color="#999"></uni-icons>
					<text class="label">最后更新:</text>
					<text class="value timestamp">{{ formatDate(projectDetail.lastUpdateProgress) }}</text>
				</view>
			</view>

			<!-- 计划任务列表区 -->
			<view class="card tasks-card">
				<view class="task-title-row">
					<text class="card-title">计划任务列表</text>
					<!-- Added Add Task Icon -->
					<view class="action-icon add-task-icon" @click="addTask">
						<uni-icons type="plus" size="22" color="#1890ff"></uni-icons> <!-- 统一为主要蓝色 -->
					</view>
				</view>

				<view v-if="taskList && taskList.length > 0">
					<view v-for="(task, index) in taskList" :key="task.taskId" class="task-item"
						@click="viewTaskDetail(task.taskId)" @longpress.stop="onLongPress(task)">
						<view class="task-header">
							<!-- 任务状态图标颜色 - 使用 getStatusInfo 获取 taskColor -->
							<uni-icons type="smallcircle" size="16"
								:color="getStatusInfo(task.status).taskColor"></uni-icons>
							<text class="task-name">{{ task.taskName || '未知任务' }}</text>
							<!-- 任务状态徽章 - 使用 getStatusInfo 获取 class 和 text -->
							<view :class="['task-status-badge', getStatusInfo(task.status).class]">
								<text>{{ getStatusInfo(task.status).text }}</text>
							</view>
						</view>
						<view class="task-info-grid">
							<view class="task-info-item">
								<uni-icons type="person" size="16" color="#666"></uni-icons>
								<text class="task-label">负责人:</text>
								<text class="task-value">{{ task.ownerName || '未知' }}</text>
							</view>
							<view class="task-info-item">
								<uni-icons type="calendar" size="16" color="#666"></uni-icons>
								<text class="task-label">计划:</text>
								<text class="task-value">{{ formatDate(task.plannedStartDate,'YYYY-MM-DD') }} 至
									{{ formatDate(task.plannedEndDate,'YYYY-MM-DD') }}</text>
							</view>
							<view class="task-info-item task-progress-item">
								<uni-icons type="circle" size="16" color="#666"></uni-icons>
								<text class="task-label">进度:</text>
								<text class="task-value task-percentage">{{ task.progress || '0' }}%</text>
							</view>
						</view>
						<!-- Task Progress Bar - 使用 getStatusInfo 获取 taskColor -->
						<view class="task-progress-bar-wrapper">
							<progress class="task-progress-bar" :percent="parseFloat(task.progress) || 0" show-info
								stroke-width="6" :activeColor="getStatusInfo(task.status).taskColor"
								border-radius="3"></progress>
						</view>

						<view v-show="onLongPressTask.show && onLongPressTask.data == task">
							<button style="background-color: red;" @click.stop="deleteTask(task.taskId)">删除</button>
						</view>
					</view>
				</view>
				<view v-else class="no-data">
					<uni-icons type="info-filled" size="24" color="#ccc"></uni-icons>
					<text class="empty-text">暂无计划任务</text>
				</view>
			</view>


			<!-- 财务信息区 -->
			<view class="card finance-card">
				<text class="card-title">财务概览</text>
				<view class="info-row">
					<uni-icons type="wallet" size="18" color="#666"></uni-icons>
					<text class="label">总价值:</text>
					<text
						class="value value-currency value-value">{{ formatMoney(projectDetail.totalValue) || 'N/A' }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="wallet-filled" size="18" color="#666"></uni-icons>
					<text class="label">总成本:</text>
					<text
						class="value value-currency value-cost">{{ formatMoney(projectDetail.totalCost) || 'N/A' }}</text>
				</view>
			</view>

			<!-- 团队信息区 -->
			<view class="card team-card">
				<text class="card-title">团队成员</text>
				<view class="info-row participants-row">
					<view class="value participants-list">
						<text v-if="fetchParticipantsList.length == 0" class="no-data-text">
							暂无参与人员
						</text>
						<view v-else>
							<view v-for="(participant, index) in fetchParticipantsList" :key="index"
								class="participant-item">
								<uni-icons type="person" size="20" color="#1890ff"></uni-icons> <!-- 统一为主要蓝色 -->
								<text class="participant-name">{{ participant.name || '未知' }}</text>
								<text v-if="participant.wechat" class="participant-wechat"> (微信:
									{{ participant.wechat }})</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他/审计信息区 -->
			<view class="card audit-card">
				<text class="card-title">其他信息</text>
				<view class="info-row">
					<uni-icons type="info" size="18" color="#666"></uni-icons>
					<text class="label">项目ID:</text>
					<text class="value">{{ projectDetail.projectId }}</text>
				</view>
				<view class="info-row timestamp-row">
					<uni-icons type="plus" size="16" color="#999"></uni-icons>
					<text class="label">创建于:</text>
					<text class="value timestamp">{{ formatDate(projectDetail.createdAt) }}</text>
				</view>
				<view class="info-row timestamp-row">
					<uni-icons type="loop" size="16" color="#999"></uni-icons>
					<text class="label">更新于:</text>
					<text class="value timestamp">{{ formatDate(projectDetail.updatedAt) }}</text>
				</view>
			</view>

			<!-- 页面底部留白，避免内容紧贴底部 -->
			<view class="bottom-spacer"></view>

		</scroll-view>

		<!-- 数据为空或加载失败状态 -->
		<view v-else class="empty-state">
			<uni-icons type="info-filled" size="30" color="#999"></uni-icons>
			<text class="empty-text">未能加载项目详情或项目不存在。</text>
			<!-- 可以添加一个按钮用于返回或刷新 -->
		</view>
	</view>
</template>

<script setup>
	// ... (your existing script setup code remains unchanged here) ...
	// 导入 Composition API 需要的函数
	import {
		ref
	} from 'vue';
	// 导入 UniApp 的生命周期钩子
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app';


	import UNI_APP from '@/.env.js'

	const BASE_API = UNI_APP.PROJECT

	// 引入之前设计的 getStatusInfo 函数及相关映射表
	/**
	 * 状态属性映射表
	 * Key: 标准化的内部状态类型 (不区分大小写)
	 * Value: {
	 *   class: string,         // 对应的CSS类名
	 *   progressColor: string, // 对应 getProgressColor 体系的颜色 (用于项目主进度条)
	 *   taskColor: string,     // 对应 getTaskStatusColor 体系的颜色 (用于任务图标/徽章/任务进度条)
	 *   text: string,          // 标准化的中文文本描述
	 *   inputStrings: string[] // 可以映射到此状态的所有可能的输入字符串
	 * }
	 */


	// 长按数据
	const onLongPressTask = ref({
		show: false,
		data: null
	})

	//长按
	const onLongPress = (task) => {
		console.log(task)
		onLongPressTask.value = {
			show: true,
			data: task
		}
	}

	// 关闭
	const onClosePopup = () => {

		setTimeout(() => {
			onLongPressTask.value = {
				show: false,
				data: null
			}
		}, 150)

	}
	//删除
	const deleteTask = (taskId) => {
		uni.showModal({
			title: '确认删除',
			content: `确定要删除项目「${taskId.taskName}」吗？`,
			confirmText: '删除',
			cancelText: '取消',
			success: (res) => {
				if (res.confirm) {
					deleteTaskAPI(taskId);
				} else {
				}
			}
		});
	}

	//删除
	const deleteTaskAPI = (id) => {
		uni.request({
			url: `${BASE_API}/task/delete/${id}`,
			success(res) {
				const result = res.data;
				if (result.code === 200) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: result.message || '删除失败',
						icon: 'none'
					});
				}
			},
			fail: (err) => {
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			},
			complete: () => {
				fetchProjectDetail(projectId.value);
				fetchTask(projectId.value); // Fetch tasks separately
				fetchParticipants(projectId.value)
				 // 重新拉取数据
			}
		});
	}

	const statusAttributesMap = {
		'in_progress': {
			class: 'status-inprogress',
			progressColor: '#f5222d', // 项目进度：进行中/正常/延期用红色警戒
			taskColor: '#1890ff', // 任务状态：进行中用蓝色
			text: '进行中',
			inputStrings: ['正常', 'in_progress', '进行中']
		},
		'delayed': {
			class: 'status-delayed',
			progressColor: '#f5222d', // 项目进度：延期用红色警戒
			taskColor: '#f5222d', // 任务状态：延期用红色
			text: '延期',
			inputStrings: ['延期', 'delayed']
		},
		'completed': {
			class: 'status-completed',
			progressColor: '#52c41a', // 项目进度：已完成用绿色
			taskColor: '#52c41a', // 任务状态：已完成用绿色
			text: '已完成',
			inputStrings: ['已完成', 'completed']
		},
		'not_started': {
			class: 'status-notstarted',
			progressColor: '#bfbfbf', // 项目进度：未开始用浅灰
			taskColor: '#a9b7c7', // 任务状态：未开始/待处理用蓝灰
			text: '未开始',
			inputStrings: ['未开始', 'not_started']
		},
		'paused': {
			class: 'status-paused',
			progressColor: '#faad14', // 项目进度：已暂停用橙色
			taskColor: '#faad14', // 任务状态：已暂停/挂起用橙色
			text: '已暂停',
			inputStrings: ['已暂停', 'paused', '暂停/挂起']
		},
		'draft': {
			class: 'status-draft',
			progressColor: '#bfbfbf', // 项目进度：草稿视为未开始，用浅灰
			taskColor: '#c0c4cc', // 任务状态：草稿用轻度灰色
			text: '草稿',
			inputStrings: ['草稿']
		},
		'pending': { // 待处理，已提交但未开始
			class: 'status-pending',
			progressColor: '#bfbfbf', // 项目进度：待处理视为未开始，用浅灰
			taskColor: '#a9b7c7', // 任务状态：待处理用蓝灰
			text: '待处理',
			inputStrings: ['待处理']
		},
		'failed': {
			class: 'status-failed',
			progressColor: '#f5222d', // 项目进度：失败用红色警戒
			taskColor: '#f5222d', // 任务状态：失败用红色
			text: '失败',
			inputStrings: ['失败']
		},
		'rejected': { // 已拒绝，与失败颜色相同，但文本和class不同
			class: 'status-rejected',
			progressColor: '#f5222d', // 项目进度：已拒绝用红色警戒
			taskColor: '#f5222d', // 任务状态：已拒绝用红色
			text: '已拒绝',
			inputStrings: ['已拒绝']
		},
		'cancelled': {
			class: 'status-cancelled',
			progressColor: '#909399', // 项目进度：已取消用灰色
			taskColor: '#909399', // 任务状态：已取消用中度灰色
			text: '已取消',
			inputStrings: ['已取消']
		},
		'archived': {
			class: 'status-archived',
			progressColor: '#dcdfe6', // 项目进度：已归档用非常浅的灰色
			taskColor: '#dcdfe6', // 任务状态：已归档用非常浅的灰色
			text: '已归档',
			inputStrings: ['已归档']
		},
		// 可以根据需要添加更多状态...
	};

	// 创建一个反向查找 Map，用于快速通过输入字符串查找对应的状态属性
	const statusLookupMap = new Map();
	for (const statusType in statusAttributesMap) {
		const attributes = statusAttributesMap[statusType];
		attributes.inputStrings.forEach(input => {
			statusLookupMap.set(input.toLowerCase(), attributes);
		});
	}

	/**
	 * 默认状态属性
	 */
	const defaultStatusAttributes = {
		class: 'status-unknown', // 添加一个未知状态的 class
		progressColor: '#1890ff', // 原 getProgressColor 的默认颜色 (蓝色)
		taskColor: '#666666', // 原 getTaskStatusColor 的默认颜色 (默认灰色)
		text: '未知',
	};


	/**
	 * 根据状态字符串获取完整的状态属性信息
	 * @param {string | null | undefined} status 原始状态字符串
	 * @returns {{class: string, progressColor: string, taskColor: string, text: string}} 包含状态属性的对象
	 */
	const getStatusInfo = (status) => {
		const lowerStatus = (status || '').toLowerCase();
		const attributes = statusLookupMap.get(lowerStatus);

		if (attributes) {
			// 返回找到的属性，使用结构复制避免修改原始映射表
			return {
				class: attributes.class,
				progressColor: attributes.progressColor,
				taskColor: attributes.taskColor,
				text: attributes.text,
			};
		} else {
			// 未找到匹配项，返回默认属性，文本使用原始状态或默认
			return {
				class: defaultStatusAttributes.class, // 使用未知状态 class
				progressColor: defaultStatusAttributes.progressColor,
				taskColor: defaultStatusAttributes.taskColor,
				text: status && status.trim() != '' ? status : defaultStatusAttributes.text, // 如果原始状态非空，则显示原始状态
			};
		}
	};

	// 1. 响应式数据 (对应 Options API 的 data)
	const projectId = ref(null); // 从页面参数获取的项目ID
	const projectDetail = ref(null); // 项目详情数据
	const taskList = ref([]); // 项目下的任务列表数据
	const loading = ref(true); // 是否正在加载
	const fetchParticipantsList = ref([]) //任务参与人员


	const formatMoney = (amount) => {
		if (amount == null || amount == undefined) {
			return 'N/A'; // Handle null/undefined input
		}
		if (isNaN(amount)) {
			return '无效金额';
		}

		const parts = amount.toString().split('.');
		const integerPart = parts[0];
		const decimalPart = parts[1] ? '.' + parts[1] : '';

		// 给整数部分添加千分位逗号
		const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

		return formattedInteger + decimalPart;
	};





	// 2. 方法 (对应 Options API 的 methods)
	// 直接声明为常量函数
	const fetchProjectDetail = async (id) => {
		loading.value = true; // 访问 ref 需要 .value

		uni.request({
			url: `${BASE_API}/project/selectById/${id}`,
			success(res) {
				const result = res.data
				if (res.data.code != 200) {
					uni.showToast({
						title: result.message,
						icon: 'none'
					});
				}
				const project = result.data
				if (project) {
					projectDetail.value = project; // 更新 ref 需要 .value
					// taskList.value = project.tasks || []; // Task list fetched separately now

					// Dynamic set page title (UniApp API directly)
					uni.setNavigationBarTitle({
						title: project.projectName || '项目详情'
					});
				} else {
					projectDetail.value = null; // Update ref
					taskList.value = []; // Update ref
					uni.showToast({
						title: '项目不存在',
						icon: 'none'
					});
				}
				loading.value = false; // Update ref
			},
			fail(err) {
				loading.value = false;
				console.error("Failed to fetch project detail:", err);
				projectDetail.value = null;
				taskList.value = [];
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});

			}
		})
	};

	//获取任务列表
	const fetchTask = async (id) => {
		uni.request({ // Changed const res = ... to direct call as res is not used later
			url: `${BASE_API}/task/selectByProjectId/${id}`,
			success(res) {
				const result = res.data
				taskList.value = result.data || [];
			},
			fail(err) {
				console.error("Failed to fetch task list:", err);
				taskList.value = [];
				// Toast is handled by the project detail fetch for the main error state
			}
		})
	}

	// 获取项目的参与人
	const fetchParticipants = async (projectId) => {
		uni.request({ // Changed const res = ... to direct call
			url: `${BASE_API}/project/selectAllEmployeeByProjectId/${projectId}`,
			success(res) {
				fetchParticipantsList.value = res.data.data || []
			},
			fail(err) {
				console.error("Failed to fetch participants:", err); // Added error logging
				fetchParticipantsList.value = []; // Ensure it's an empty array on failure
			}
		})
	}



	const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm') => {
		if (!dateString) return '待定';
		try {
			// Attempt to parse as ISO string first
			let date = new Date(dateString);

			// If parsing as ISO fails or results in an invalid date, try other formats
			if (isNaN(date.getTime())) {
				// Try parsing by replacing hyphens with slashes for Safari/iOS compatibility
				date = new Date(dateString.replace(/-/g, '/'));
			}

			if (isNaN(date.getTime())) { // Check again if date is valid
				return '无效日期';
			}

			const year = date.getFullYear();
			const month = ('0' + (date.getMonth() + 1)).slice(-2);
			const day = ('0' + date.getDate()).slice(-2);
			const hours = ('0' + date.getHours()).slice(-2);
			const minutes = ('0' + date.getMinutes()).slice(-2);

			if (format == 'YYYY-MM-DD') {
				return `${year}-${month}-${day}`;
			} else if (format == 'YYYY-MM-DD HH:mm') {
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
			// Extendable for other formats
			return dateString; // Return original if format is unknown
		} catch (e) {
			console.error("Error formatting date:", dateString, e);
			return '格式化失败';
		}
	};

	// Removed old getStatusClass, getProgressColor, getTaskStatusColor

	const editProject = (id) => {
		if (!id) {
			uni.showToast({
				title: '项目ID无效',
				icon: 'none'
			});
			return;
		}
		console.log("点击了修改图标，项目ID:", id);
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/update/update?id=${id}`
		});
	};

	const viewTaskDetail = (taskId) => {
		console.log("点击了任务，任务ID:", taskId);
		// Assuming the task detail page URL structure
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/plan/plan?id=${taskId}`
		});
	};

	const addTask = () => {
		// Ensure projectDetail and its ID are available
		if (!projectDetail.value || !projectDetail.value.projectId) {
			uni.showToast({
				title: '无法获取项目ID',
				icon: 'none'
			});
			return;
		}
		const currentProjectId = projectDetail.value.projectId;
		const currentProjectName = projectDetail.value.projectName;
		console.log("点击了添加任务图标，项目ID:", currentProjectId);
		// Navigate to the Add Task page, passing the project ID and Name
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/plan/add/add?projectId=${currentProjectId}&projectName=${currentProjectName}`
		});
	};

	const copyWechat = (wechatId) => {
		if (!wechatId) return;
		uni.setClipboardData({
			data: wechatId,
			success: () => {
				uni.showToast({
					title: '微信已复制',
					icon: 'success'
				});
			},
			fail: () => {
				uni.showToast({
					title: '复制失败',
					icon: 'none'
				});
			}
		});
	};



	onShow(() => {
		fetchProjectDetail(projectId.value);
		fetchTask(projectId.value); // Fetch tasks separately
		fetchParticipants(projectId.value)
	})



	// 3. 生命周期钩子 (对应 Options API 的 onLoad)
	// 使用导入的 onLoad 函数
	onLoad((options) => {
		// Get project ID passed during page navigation
		if (options && options.id) {
			// Use .value to update ref
			projectId.value = parseInt(options.id);
			// Call functions directly
			fetchProjectDetail(projectId.value);
			fetchTask(projectId.value); // Fetch tasks separately
			fetchParticipants(projectId.value)
		} else {
			loading.value = false; // Update ref
			console.error("No project ID received");
			uni.showToast({
				title: '项目ID丢失',
				icon: 'none'
			});
		}

		// 监听自定义事件，用于刷新页面数据 (例如：从子页面返回后触发)
		// uni.$on("updateProjectDetail",()=>{
		// 	console.log("Received updateProjectDetail event, fetching data again.");
		// 	fetchProjectDetail(projectId.value);
		// 	fetchTask(projectId.value);
		// 	fetchParticipants(projectId.value);
		// });

		// 别忘了在页面卸载时取消监听，防止内存泄露
		// import { onUnload } from '@dcloudio/uni-app';
		// onUnload(() => {
		//     uni.$off('updateProjectDetail');
		// });
		// Note: onUnload is needed if using uni.$on globally.
		// For this example, it might be okay, but good practice is to unregister.


	});

	// Variables and functions declared in <script setup> are automatically exposed to the template.
</script>

<style lang="scss" scoped>
	/* Use rpx for dimensions, which scales better on different UniApp platforms */

	/* Global page background and base layout */
	.page-container {
		background-color: #f7f8fa; // Slightly lighter grey background
		min-height: 100vh; // Ensure background covers full height
		display: flex;
		flex-direction: column;
	}

	/* Scrollable area for content */
	.detail-scroll-view {
		flex: 1; // Fill remaining space
		padding: 20rpx; // Overall padding around the content
		box-sizing: border-box; // Include padding in the size
	}

	/* Loading/Empty state styles */
	.loading-state,
	.empty-state {
		text-align: center;
		padding: 100rpx 0;
		color: #999;
		font-size: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-grow: 1; // Make loading/empty state fill remaining space and center itself vertically
	}

	.loading-state .uni-icons {
		animation: spin 1.5s linear infinite; // Spinning animation for loading icon
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.loading-text,
	.empty-text {
		margin-top: 20rpx;
	}

	/* Card base style */
	.card {
		background-color: #ffffff; // White background
		border-radius: 16rpx; // Rounded corners
		padding: 30rpx; // Inner padding
		margin-bottom: 20rpx; // Spacing between cards
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); // Softer shadow
		overflow: hidden; // Prevent content from overflowing rounded corners
	}


	/* Card Title style - For grouping information within cards */
	.card-title {
		font-size: 32rpx; // Slightly larger title font size
		font-weight: bold;
		color: #333;
		display: block;
		border-left: 8rpx solid #1890ff; // Left accent bar using primary color
		padding-left: 15rpx; // Space between bar and text
		line-height: 1.2;
		margin-bottom: 25rpx; // Spacing below title
	}

	/* Header Card Specific */
	.header-card {
		/* padding is defined in .card */
	}

	/* Project Title Row, contains project name and edit icon */
	.project-title-row {
		display: flex;
		justify-content: space-between;
		align-items: center; // Vertically center
		margin-bottom: 15rpx; // Space below title row
	}

	.project-name {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		flex: 1; // Project name takes remaining space
		margin-right: 20rpx; // Space between name and icon
		line-height: 1.4; // Line height
		word-break: break-all; // Auto-wrap long names
	}

	/* Action icon container (e.g., edit icon, add icon) */
	.action-icon {
		flex-shrink: 0; // Prevent icon from shrinking
		padding: 10rpx; // Increase clickable area
		margin-left: 10rpx; // Add some space from the element before it (title)
		opacity: 0.9; // Slightly less opaque
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;

		&:active {
			opacity: 1; // Restore opacity on click
			background-color: rgba(0, 0, 0, 0.05); // Slight feedback on click
			border-radius: 50%; // Rounded feedback
		}
	}

	/* Status Row */
	.project-status-row {
		margin-bottom: 20rpx; // Space below status row
	}

	/* Status Badge base style */
	.status-badge,
	.task-status-badge {
		font-size: 26rpx; // Consistent font size for badges
		padding: 8rpx 20rpx; // Consistent padding
		border-radius: 20rpx; // Rounded corners
		white-space: nowrap; // Prevent wrapping
		font-weight: 500; // Semi-bold
		min-width: 120rpx; // Minimum width
		text-align: center;
		display: inline-flex; // Use inline-flex to allow vertical centering of text if needed
		align-items: center; // Vertically center text
		justify-content: center; // Horizontally center text
		line-height: 1; // Compact line height
	}

	/* Status Colors (Using taskColor palette for badges for consistency with task status) */
	.status-inprogress {
		background-color: #e6f7ff; // Blue Light
		color: #1890ff; // Blue
	}

	.status-delayed {
		background-color: #fff1f0; // Red Light
		color: #f5222d; // Red
	}

	.status-completed {
		background-color: #f6ffed; // Green Light
		color: #52c41a; // Green
	}

	.status-notstarted {
		background-color: #fafafa; // Gray Light
		color: #bfbfbf; // Gray
	}

	.status-paused {
		background-color: #fffbe6; // Orange Light
		color: #faad14; // Orange/Yellow
	}

	.status-draft {
		background-color: #f4f4f5; // Gray Light
		color: #909399; // Gray (Using a slightly darker grey for better contrast)
	}

	.status-pending {
		background-color: #ebf2f7; // Blue-Grey Light
		color: #a9b7c7; // Blue-Grey
	}

	.status-failed {
		background-color: #fff1f0; // Red Light
		color: #f5222d; // Red
	}

	.status-rejected {
		background-color: #fff1f0; // Red Light
		color: #f5222d; // Red
	}

	.status-cancelled {
		background-color: #f4f4f5; // Gray Light
		color: #909399; // Gray
	}

	.status-archived {
		background-color: #fcfcfd; // Very Light Gray
		color: #dcdfe6; // Very Light Gray
	}

	.status-unknown {
		background-color: #e9e9eb; // Default Gray Background
		color: #666666; // Default Gray Text
	}


	.project-desc {
		font-size: 28rpx; // Description font size
		color: #555;
		line-height: 1.7;
		white-space: pre-wrap; // Preserve line breaks
		word-break: break-all; // Auto-wrap long words
	}

	/* Info Item Generic Style */
	.info-row {
		display: flex;
		align-items: flex-start; // Align to top to handle text wrapping
		font-size: 28rpx; // Font size
		margin-bottom: 20rpx; // Spacing between items
		line-height: 1.6;

		&:last-child {
			margin-bottom: 0;
		}

		.uni-icons {
			margin-right: 15rpx; // Space between icon and label
			flex-shrink: 0; // Prevent icon from shrinking
			margin-top: 4rpx; // Slight top margin for vertical alignment
		}

		.label {
			color: #666; // Label color
			margin-right: 15rpx; // Space between label and value
			white-space: nowrap; // Prevent label wrapping
			min-width: 180rpx; // Increased min-width for label to better align values
			flex-shrink: 0; // Prevent label from shrinking
			text-align: right; // Right align labels for value alignment
		}

		.value {
			color: #303133; // Value color
			flex-grow: 1; // Value takes remaining space
			word-break: break-word; // Break long words
		}

		.value-currency {
			font-weight: 500; // Semi-bold for currency
		}

		.value-value {
			color: #52c41a; // Green for value
		}

		.value-cost {
			color: #f5222d; // Red for cost
		}

		.copy-btn {
			margin-left: 20rpx; // Space from value
			padding: 0 20rpx;
			line-height: 1.8;
			font-size: 26rpx; // Smaller font for button
			flex-shrink: 0; // Prevent button from shrinking
			height: auto; // Adjust height
			background-color: #1890ff; // Match primary color
			color: #fff;
			border-radius: 8rpx;
			border: none;
			display: flex; // Center text in button
			align-items: center;
			justify-content: center;
		}
	}

	/* Separator line */
	.separator {
		height: 1px;
		background-color: #eee;
		margin: 25rpx 0;
	}

	/* Timestamp Row */
	.timestamp-row {
		font-size: 24rpx;
		margin-bottom: 10rpx; // Smaller bottom margin

		&:last-child {
			margin-bottom: 0;
		}

		.uni-icons {
			margin-top: 2rpx;
		}

		.label {
			color: #999; // Label color
			min-width: 140rpx; // Adjusted min-width for timestamps
			text-align: right; // Right align labels
		}

		.value.timestamp {
			color: #888; // Value color
		}
	}

	/* Progress Card Specific */
	.progress-card {
		/* inherits .card */
	}

	.progress-item {
		display: flex;
		align-items: center;
		margin-bottom: 25rpx; // Space below percentage
		font-size: 28rpx; // Match info-row font size

		.label {
			color: #666;
			margin-right: 15rpx;
			white-space: nowrap;
			min-width: 180rpx; // Align with other info labels
			flex-shrink: 0;
			text-align: right;
		}

		.value {
			flex-grow: 1;
			word-break: break-word;
		}
	}

	.progress-percentage {
		font-size: 36rpx; // Larger font for percentage
		font-weight: bold;
		color: #1890ff; // Blue color for percentage
	}


	.progress-bar-container {
		margin: 10rpx 0 30rpx 0; // Top/Bottom margin
		/* Calculate padding-left to align progress bar with info-row label/value start:
           icon(0 in progress item) + label(180) + label_margin(15) = 195rpx */
		padding-right: 15rpx; // Add right padding for balance
		box-sizing: border-box;

		/* Deep scope to style the internal UniApp progress component elements */
		/* Use ::v-deep or :deep() depending on Vue version/UniApp setup */
		::v-deep .uni-progress-bar {
			border-radius: 8rpx;
		}

		/* Hide the default percentage text shown by progress component */
		::v-deep .uni-progress-info {
			display: none !important;
		}
	}

	.progress-info-row {

		/* Inherits info-row style */
		.label {
			min-width: 180rpx; // Ensure label aligns
		}
	}


	/* Task List Card Specific */
	.tasks-card {
		/* inherits .card */
	}

	/* Styles for Task Title Row */
	.task-title-row {
		display: flex;
		justify-content: space-between; // Pushes title left, icon right
		align-items: center; // Vertically aligns title and icon
		margin-bottom: 25rpx; // Spacing below the title row

		/* Adjust the card title style specifically within this row */
		.card-title {
			margin-bottom: 0; // Remove bottom margin from title text itself
			display: inline-block; // Allow it to sit alongside the icon
			flex-grow: 1; // Allow title to take available space
			margin-right: 15rpx; // Add some space between title and icon
		}
	}

	.no-data {
		color: #999;
		font-size: 26rpx;
		text-align: center;
		padding: 20rpx 0; // Padding inside the card for empty state
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.no-data .empty-text {
		margin-top: 15rpx;
	}


	.task-item {
		padding: 25rpx 0;
		border-bottom: 1rpx solid #eee; // Separator line
		transition: background-color 0.2s ease; // Smooth background transition

		&:last-child {
			border-bottom: none; // No border for the last item
			padding-bottom: 0;
		}

		/* Active state for click */
		&:active {
			background-color: #f8f8f8;
		}
	}


	.task-header {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.task-header .uni-icons {
		margin-right: 15rpx;
		flex-shrink: 0;
	}

	.task-name {
		flex: 1; // Task name takes space
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-right: 20rpx;
		word-break: break-all;
	}

	/* Task Status Badge */
	.task-status-badge {
		/* Inherits base style from .status-badge */
		flex-shrink: 0; // Prevent shrinking
	}


	.task-info-grid {
		display: flex;
		flex-wrap: wrap; // Allow wrapping
		/* Calculate left padding to align with task name text: icon(16) + icon_margin(15) = 31rpx */
		padding-left: calc(16rpx + 15rpx);
	}

	.task-info-item {
		display: flex;
		align-items: center;
		font-size: 26rpx; // Smaller font for task info
		color: #666;
		margin-right: 40rpx; // Space between items in the grid
		margin-bottom: 15rpx; // Bottom margin for wrap
		line-height: 1.5;

		/* Use flex-basis to control item width and wrapping */
		/* Example: owner+date can take half, progress takes remaining or full width */
		/* Adjust these based on expected content length */
		&.task-owner-item {
			flex-basis: 40%;
			min-width: 200rpx;
		}

		&.task-date-item {
			flex-basis: 60%;
			min-width: 300rpx;
		}

		&.task-progress-item {
			flex-basis: 100%;
			/* Force progress to a new line */
			margin-right: 0;
			margin-bottom: 0; // Remove bottom margin as progress bar comes next
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	.task-info-item .uni-icons {
		margin-right: 10rpx;
		margin-top: 0;
	}

	.task-label {
		color: #999;
		margin-right: 10rpx;
		flex-shrink: 0; // Prevent label from shrinking
		min-width: 80rpx; // Fixed width for task label
		text-align: right; // Align task labels right
	}

	.task-value {
		color: #555;
		word-break: break-all;
		line-height: 1.5;
	}

	.task-percentage {
		font-weight: bold;
		color: #1890ff; // Blue color for percentage
		font-size: 28rpx; // Slightly larger font for percentage
	}

	.task-progress-bar-wrapper {
		flex: 1; // Progress bar container takes remaining space
		display: flex; // Use flex to vertically center the progress bar
		align-items: center;
		padding-right: 10rpx; // Right padding
		height: 10rpx; // Give height for alignment
		/* Calculate padding-left to align with the start of task info values
           task-info-grid padding-left (31) + task-label width (80) + task-label margin-right (10) = 121rpx */
		padding-left: calc(16rpx + 15rpx + 80rpx + 10rpx);
		box-sizing: border-box;
		margin-top: 15rpx; // Add space above the progress bar


		/* Deep scope to style the internal UniApp progress component elements */
		::v-deep .uni-progress-bar {
			border-radius: 6rpx;
		}

		/* Hide percentage text from progress component */
		::v-deep .uni-progress-info {
			display: none !important;
		}
	}


	/* Finance Card Specific */
	.finance-card {

		/* inherits .card */
		.info-row .label {
			min-width: 140rpx; // Adjust label width for finance items
		}
	}


	/* Team Card Specific */
	.team-card {
		/* inherits .card */
	}

	/* Participants row (using info-row style) */
	.participants-row {
		align-items: flex-start; // Align to top because list might wrap

		.label {
			min-width: 160rpx; // Keep wide label for consistency with other info
			text-align: right;
		}
	}

	.participants-list {
		flex: 1; // Takes remaining space
		display: flex;
		flex-direction: column;
	}

	.no-data-text {
		// Style for "暂无参与人员" text
		color: #999;
		font-size: 28rpx;
		margin-left: calc(160rpx + 15rpx); // Align with value start
	}

	.participant-item {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx; // Spacing between participant items
		padding-bottom: 15rpx;
		border-bottom: 1rpx dashed #eee; // Dashed separator
		font-size: 28rpx; // Font size
		line-height: 1.5;
		flex-wrap: wrap; // Allow wrapping for long names/wechats
		/* Align items after the label */


		&:last-child {
			margin-bottom: 0;
			padding-bottom: 0;
			border-bottom: none;
		}

		.uni-icons {
			margin-right: 15rpx;
			flex-shrink: 0;
			margin-top: 0; // Reset margin-top from info-row
		}

		.participant-name {
			font-weight: bold;
			color: #333;
			margin-right: 10rpx;
			flex-shrink: 0;
			word-break: break-all;
		}

		.participant-wechat {
			font-size: 24rpx; // Smaller font
			color: #666;
			flex-shrink: 1;
			min-width: 0;
			word-break: break-all;
		}
	}

	/* Remove left padding for the first item if it's the "暂无参与人员" text */
	.participants-list .no-data-text:first-child {
		padding-left: 0;
		margin-left: 0; // Remove margin from the label position
		width: 100%; // Allow centering if needed
		text-align: center; // Center if it's the only item
	}


	/* Audit Card Specific */
	.audit-card {

		/* inherits .card */
		.info-row .label {
			min-width: 140rpx; // Adjust label width for audit items
		}
	}


	/* Page bottom spacer */
	.bottom-spacer {
		height: 40rpx; // Space at the bottom
	}
</style>