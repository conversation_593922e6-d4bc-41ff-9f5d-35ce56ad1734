<template>
	<!-- {{test}} -->
	<view style="display: flex; justify-content: center; align-items: center; height: 12vh;">
		计划详情
	</view>
	<view class="page-container">

		<!-- 使用 v-if 检查 taskData 是否已加载 -->
		<view v-if="taskData" class="task-detail">

			<!-- 任务概览卡片 -->
			<view class="card task-overview-card">
				<!-- 项目名称 -->
				<text class="project-name">项目: {{ taskData.projectName || '未知项目' }}</text>
				<!-- 任务标题、优先级和编辑图标 -->
				<view class="title-priority-wrapper">
					<text class="task-title">{{ taskData.taskName || '无标题任务' }}</text>
					<!-- 优先级徽章 -->
					<view class="priority-badge" :class="'priority-' + (taskData.priority || '').toLowerCase()">
						<text>{{ taskData.priority || '未设置' }}</text>
					</view>
					<!-- 编辑图标 (假设 editProject 方法接受任务/项目ID) -->
					<!-- 注意：如果 editProject 是编辑任务，应传递 taskData.taskId -->
					<view class="action-icon edit-icon" @click="editProject(taskData.taskId)">
						<uni-icons type="compose" size="22" color="#409EFF"></uni-icons>
					</view>
				</view>
				<!-- 任务描述 (如果存在) -->
				<view v-if="taskData.taskDesc" class="info-row task-desc">
					<!-- <uni-icons type="info" size="18" color="#666"></uni-icons> -->
					<!-- <text class="label">描述:</text> -->
					<text class="value">{{ taskData.taskDesc }}</text>
				</view>
			</view>

			<!-- 时间与成本卡片 -->
			<view class="card time-cost-card">
				<text class="card-title">时间与成本</text>
				<view class="info-row">
					<uni-icons type="calendar" size="18" color="#666"></uni-icons>
					<text class="label">计划开始:</text>
					<text class="value">{{ formatDate(taskData.plannedStartDate,"YYYY-MM-DD") || '未设置' }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="calendar-filled" size="18" color="#666"></uni-icons>
					<text class="label">计划结束:</text>
					<text class="value">{{ formatDate(taskData.plannedEndDate,"YYYY-MM-DD") || '未设置' }}</text>
				</view>
				<view class="separator"></view>
				<view class="info-row">
					<uni-icons type="smallcircle" size="18" color="#1890ff"></uni-icons>
					<text class="label">实际开始:</text>
					<text class="value">{{ formatDate(taskData.actualStartDate) || '未开始' }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="smallcircle" size="18" color="#52c41a"></uni-icons>
					<text class="label">实际结束:</text>
					<text class="value">{{ formatDate(taskData.actualEndDate) || '未结束' }}</text>
				</view>
				<view class="separator"></view>
				<view class="info-row">
					<uni-icons type="wallet" size="18" color="#666"></uni-icons>
					<text class="label">预估价值:</text>
					<text class="value value-currency">{{ formatMoney(taskData.estimatedValue)}}</text>
				</view>
				<view class="info-row">
					<uni-icons type="wallet-filled" size="18" color="#faad14"></uni-icons>
					<text class="label">预计成本:</text>
					<text class="value value-currency">{{ formatMoney(taskData.estimatedCost) }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="wallet-filled" size="18" color="#f5222d"></uni-icons>
					<text class="label">实际成本:</text>
					<text class="value value-currency">{{ formatMoney(taskData.actualCost) }}</text>
				</view>
			</view>

			<!-- 进度与状态卡片 -->
			<view class="card progress-status-card">
				<text class="card-title">进度与状态</text>
				<view class="progress-item">
					<text class="label">当前进度:</text>
					<view class="progress-bar-container">
						<progress :percent="taskData.progress || 0" show-info stroke-width="8" activeColor="#1890ff"
							border-radius="4" />
					</view>
				</view>
				<view class="separator"></view>
				<view class="info-row">
					<uni-icons type="info" size="18" color="#666"></uni-icons>
					<text class="label">状态:</text>
					<text class="value"
						:style="{ color: getStatusColor(taskData.status) }">{{ taskData.status || '未知' }}</text>
				</view>
				<view class="separator"></view>
				<view class="info-row timestamp-row">
					<uni-icons type="plus" size="16" color="#999"></uni-icons>
					<text class="label">创建于:</text>
					<text class="value timestamp">{{ formatDate(taskData.createdAt) || '未知' }}</text>
				</view>
				<view class="info-row timestamp-row">
					<uni-icons type="loop" size="16" color="#999"></uni-icons>
					<text class="label">更新于:</text>
					<text class="value timestamp">{{ formatDate(taskData.updatedAt) || '未知' }}</text>
				</view>
			</view>

			<!-- 负责人信息卡片 -->
			<view class="card responsible-card">
				<text class="card-title">负责人信息</text>
				<view class="info-row">
					<uni-icons type="person-filled" size="18" color="#666"></uni-icons>
					<text class="label">负责人:</text>
					<text class="value">{{ taskData.ownerName || '未设置' }}</text>
				</view>
				<view class="info-row">
					<uni-icons type="chatbubble-filled" size="18" color="#666"></uni-icons>
					<text class="label">负责人微信:</text>
					<text class="value">{{ taskData.ownerWechat || '未提供' }}</text>
					<button v-if="taskData.ownerWechat" size="mini" type="primary"
						@click="copyWechat(taskData.ownerWechat)" class="copy-btn">复制</button>
				</view>
			</view>

			<!-- 参与人员卡片 -->
			<view class="card participants-card">
				<text class="card-title">参与人员</text>
				<view v-if="taskData.participants && taskData.participants.length > 0" class="participant-list">
					<view v-for="member in taskData.participants" :key="member.id || member.name"
						class="participant-item">
						<uni-icons type="person" size="20" color="#409eff"></uni-icons>
						<text class="member-name">{{ member.name || '未知' }}</text>
						<text v-if="member.wechat" class="member-wechat"> (微信: {{ member.wechat }})</text>
					</view>
				</view>
				<view v-else class="no-data">
					<text>暂无参与人员</text>
				</view>
			</view>

			<!-- 每日复盘记录卡片 -->
			<view class="card reviews-card">
				<text class="card-title">每日复盘记录</text>
				<view v-if="taskData.dailyReviewSummary && taskData.dailyReviewSummary.length > 0" class="review-list">
					<view v-for="(review, index) in taskData.dailyReviewSummary" :key="index" class="review-item-card">
						<view class="review-header">
							<uni-icons type="calendar" size="16" color="#666"></uni-icons>
							<text class="review-date">{{ formatDate(review.date) || '未知日期' }}</text>
							<text class="review-hours">({{ review.hours || 'N/A' }} 小时)</text>
							<text class="review-day-progress">当日进度: {{ review.progress || 'N/A' }}%</text>
						</view>
						<view class="review-participants">
							<uni-icons type="staff" size="16" color="#666"></uni-icons>
							<text>参与: {{ formatReviewParticipants(review.participants) || '无' }}</text>
						</view>
						<view v-if='review.content' class="review-content">
							<text>内容: {{ review.content }}</text>
						</view>
						<view v-if='review.summary' class="review-summary">
							<text>总结: {{ review.summary }}</text>
						</view>
						<view v-if='!review.content && !review.summary' class="review-empty-content">
							<text>无内容记录</text>
						</view>
					</view>
				</view>
				<view v-else class="no-data">
					<text>暂无复盘记录</text>
				</view>
			</view>

		</view>
		<view v-else class="loading">
			<text>加载中...</text>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app';


	import UNI_APP from '@/.env.js'

	const BASE_API = UNI_APP.PROJECT

	// 响应式数据
	const taskData = ref(null); // 初始化为 null

	const test = ref("test")


	const formatMoney = (amount) => {
		if (isNaN(amount)) {
			return '无效金额';
		}

		if (!amount) {
			return 0;
		}

		const parts = amount.toString().split('.');
		const integerPart = parts[0];
		const decimalPart = parts[1] ? '.' + parts[1] : '';

		// 给整数部分添加千分位逗号
		const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

		return formattedInteger + decimalPart;
	};

	const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm') => {
		if (!dateString) return '待定';
		try {
			const date = new Date(dateString);

			if (isNaN(date)) {
				return '无效日期';
			}

			const year = date.getFullYear();
			const month = ('0' + (date.getMonth() + 1)).slice(-2);
			const day = ('0' + date.getDate()).slice(-2);
			const hours = ('0' + date.getHours()).slice(-2);
			const minutes = ('0' + date.getMinutes()).slice(-2);

			if (format == 'YYYY-MM-DD') {
				return `${year}-${month}-${day}`;
			} else if (format == 'YYYY-MM-DD HH:mm') {
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
			// 可扩展其他格式
			return dateString;
		} catch (e) {
			console.error("Error formatting date:", dateString, e);
			return '格式化失败';
		}
	};


	// 方法
	const editProject = (taskId) => {
		if (!taskId) {
			uni.showToast({
				title: '计划ID无效',
				icon: 'none'
			});
			return;
		}
		// 使用正确的路径到你的项目表单页，并传递计划ID
		// 注意：检查 '/pages/Jenasi/project/info/plan/update/update' 是否是正确的项目更新路径
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/plan/update/update?id=${taskId}`
		}); // 假设项目更新页路径
		console.log("Navigating to Edit Project for Project ID:", taskId);
	};

	const fetchTaskDetails = async (taskId) => { // 函数和参数重命名
		try {
			// 根据你的实际API调整接口地址URL来获取任务详情
			const res = await uni.request({
				url: `${BASE_API}/task/selectById/${taskId}`, // 示例: /api/v1/tasks/{taskId}
			});

			if (res.data.code != 200) {
				uni.showToast({
					title: result.message,
					icon: 'none'
				});
			}
			test.value = res.data.data
			taskData.value = res.data.data; // 将获取到的数据赋值给 taskData.value

		} catch (error) {
			// 处理网络或请求错误
			uni.showToast({
				title: '网络错误',
				icon: 'none'
			});
			console.error("获取数据错误:", error);
		}
	};
	// --- 真实API获取示例结束 ---

	// 更新 getStatusColor 函数以匹配新的状态列表和颜色
	const getStatusColor = (status) => {
		switch (status) {
			case '草稿': // 未正式提交
				return '#c0c4cc'; // 轻度灰色
			case '待处理': // 已提交但未开始
				return '#a9b7c7'; // 中浅度灰色/蓝灰
			case '进行中': // 正在进行
				return '#1890ff'; // 蓝色
			case '暂停/挂起': // 临时停止
				return '#faad14'; // 橙色/黄色
			case '已完成': // 成功完成
				return '#52c41a'; // 绿色
			case '失败': // 未成功完成
				return '#f5222d'; // 红色
			case '已取消': // 主动取消
				return '#909399'; // 中度灰色
			case '已拒绝': // 如审核不通过
				return '#f5222d'; // 红色 (同失败，表示负面结果)
			case '已归档': // 长期存储，不再活跃
				return '#dcdfe6'; // 非常浅的灰色
			default:
				return '#666666'; // 默认灰色 - 未知状态
		}
	};

	const formatReviewParticipants = (participantsArray) => {
		// 这个函数保持不变
		if (!participantsArray || participantsArray.length == 0) {
			return '无';
		}
		return participantsArray.join(', '); // 简单的逗号分隔
	};

	const copyWechat = (wechatId) => {
		if (!wechatId) return;
		uni.setClipboardData({
			data: wechatId,
			success: () => {
				uni.showToast({
					title: '微信已复制',
					icon: 'success'
				});
			},
			fail: () => {
				uni.showToast({
					title: '复制失败',
					icon: 'none'
				});
			}
		});
	};



	let taskId = null;

	// UniApp 生命周期钩子 onLoad
	onLoad((options) => {
		// 在实际应用中，根据通过 options 传递的任务ID获取数据
		taskId = options.id; // 假设任务ID通过路由options传递
		fetchTaskDetails(taskId); // 如果使用API，则调用获取方法
		console.log("任务详情页加载，options:", options);
		// loadSampleData(); // 加载示例数据用于演示





	});

	onShow(() => {
		fetchTaskDetails(taskId)
	})
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f4f6f8; // 柔和的背景色
		min-height: 100vh; // 确保至少占满一屏高度
		padding: 20rpx; // 页面内边距
		box-sizing: border-box; // 内边距包含在宽度内
	}

	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 50vh; // 居中显示在页面中部
		color: #999;
		font-size: 28rpx;
	}

	.task-detail {
		// 命名更符合内容
		display: flex;
		flex-direction: column;
		gap: 25rpx; // 卡片之间的间距增加
	}

	.card {
		background-color: #ffffff;
		border-radius: 16rpx; // 柔和的圆角
		padding: 30rpx; // 卡片内边距
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08); // 略强的阴影效果
		overflow: hidden; // 防止内容超出圆角
	}

	.card-title {
		font-size: 32rpx; // 子标题字体
		font-weight: bold;
		color: #333;
		margin-bottom: 25rpx;
		display: block;
		border-left: 8rpx solid #1890ff; // 强调色左边框
		padding-left: 15rpx;
		line-height: 1; // 避免边框高度问题
	}

	/* 任务概览卡片特定样式 */
	.task-overview-card {
		.project-name {
			font-size: 26rpx;
			color: #666; // 项目名颜色稍浅
			margin-bottom: 15rpx;
			display: block;
		}

		.title-priority-wrapper {
			display: flex;
			align-items: center; // 垂直居中
			gap: 20rpx; // 元素之间的间距
			margin-bottom: 15rpx; // 标题与下方内容的间距
		}

		.task-title {
			font-size: 38rpx; // 任务标题更大
			font-weight: bold;
			color: #303133;
			flex-grow: 1; // 占据剩余空间
			line-height: 1.3; // 行高优化
		}

		.action-icon {
			flex-shrink: 0; // 防止图标缩小
			padding: 10rpx; // 增加点击区域
			margin-right: -10rpx; // 抵消部分padding，让图标更靠近边缘
		}

		.task-desc {
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 1rpx dashed #eee; // 分隔线

			.value {
				font-size: 28rpx;
				color: #555;
				line-height: 1.6;
			}
		}
	}

	.priority-badge {
		padding: 8rpx 20rpx; // 徽章内边距
		border-radius: 25rpx; // 更圆的角
		font-size: 24rpx;
		font-weight: bold;
		text-align: center;
		flex-shrink: 0; // 防止徽章缩小

		text {
			color: #fff; // 确保文本颜色为白色
		}

		&.priority-高 {
			background-color: #f56c6c;
		}

		// 红色系
		&.priority-中 {
			background-color: #e6a23c;
		}

		// 黄色系
		&.priority-低 {
			background-color: #409eff;
		}

		// 蓝色系
		&.priority-未设置 {
			background-color: #909399;
		}

		// 灰色
	}


	/* 通用信息行样式 */
	.info-row {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		line-height: 1.6;
		// flex-wrap: wrap; // 允许内容换行，保持 flex 布局

		&:last-child {
			margin-bottom: 0;
		}

		.uni-icons {
			margin-right: 15rpx; // 图标与文本间距
			flex-shrink: 0; // 防止图标被压缩
		}

		.label {
			color: #666;
			margin-right: 15rpx;
			white-space: nowrap; // 标签不换行
			min-width: 140rpx; // 标签最小宽度，对齐用
			flex-shrink: 0;
		}

		.value {
			color: #303133;
			flex-grow: 1; // 值占据剩余空间
			word-break: break-word; // 长文本自动换行
			min-width: 0; // 结合 flex-grow 确保在 flex 项中正确换行
		}

		.value-currency {
			font-weight: 500;
			color: #ff5722; // 醒目的货币颜色
		}

		.copy-btn {
			margin-left: auto; // 推到最右边
			padding: 0 20rpx; // 按钮内边距
			height: auto; // 高度自适应
			line-height: 1.8; // 按钮行高
			font-size: 24rpx; // 按钮字体大小
		}
	}

	.separator {
		height: 1px;
		background-color: #eee;
		margin: 25rpx 0; // 分隔线上下间距
	}

	.timestamp-row {
		font-size: 24rpx;
		margin-bottom: 10rpx; // 时间戳行间距小一些
		color: #999; // 颜色更浅

		.label {
			color: #999;
			min-width: 100rpx; // 稍小的标签宽度
		}

		.value.timestamp {
			color: #888;
		}
	}

	/* 进度卡片样式 */
	.progress-status-card {
		.progress-item {
			display: flex;
			align-items: center;
			margin-bottom: 25rpx; // 进度条与状态分隔
			font-size: 28rpx;

			.label {
				color: #666;
				margin-right: 15rpx;
				white-space: nowrap;
				width: 150rpx; // 标签固定宽度
				flex-shrink: 0;
			}

			.progress-bar-container {
				flex-grow: 1;

				// 深度选择器修改uni-progress内部样式
				::v-deep .uni-progress-bar {
					border-radius: 8rpx !important; // 进度条圆角
				}

				::v_deep .uni-progress-info {
					font-size: 24rpx; // 进度文本大小
					margin-left: 10rpx; // 进度文本与条间距
				}
			}
		}
	}

	/* 负责人卡片样式 */
	.responsible-card {
		// 使用通用 .info-row 样式即可
	}

	/* 参与人员卡片样式 */
	.participants-card {
		.participant-list {
			// display: flex; // 如果想横向排列，可以启用 flex
			// flex-wrap: wrap;
			// gap: 15rpx 20rpx; // 控制间距
		}

		.participant-item {
			display: flex;
			align-items: center;
			padding: 15rpx 0;
			border-bottom: 1px solid #f0f0f0; // 项分隔线
			font-size: 28rpx;
			color: #333;

			&:last-child {
				border-bottom: none; // 最后一项无底边框
			}

			.uni-icons {
				margin-right: 15rpx;
				flex-shrink: 0;
			}

			.member-name {
				font-weight: 500;
				margin-right: 10rpx;
			}

			.member-wechat {
				color: #888;
				font-size: 24rpx;
			}
		}
	}

	/* 每日复盘记录卡片特定样式 */
	.reviews-card {
		.review-list {}

		// 用于包裹列表项

		.review-item-card {
			background-color: #f9f9f9; // 浅背景区分
			border: 1px solid #eee;
			border-radius: 10rpx;
			padding: 20rpx;
			margin-bottom: 20rpx; // 项间距
			font-size: 26rpx;
			color: #555;

			&:last-child {
				margin-bottom: 0;
			}

			.review-header {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				gap: 8rpx 15rpx; // 行间距和列间距
				margin-bottom: 15rpx;
				color: #333;
				font-weight: 500;

				.uni-icons {
					margin-right: 5rpx;
					flex-shrink: 0;
				}

				.review-date {
					font-size: 28rpx; // 日期稍大
				}

				.review-hours {
					font-size: 24rpx;
					color: #888;
				}

				.review-day-progress {
					font-size: 24rpx;
					color: #1890ff;
					margin-left: auto; // 推到右边
					flex-shrink: 0; // 防止被压缩
				}
			}

			.review-participants {
				display: flex;
				align-items: center;
				margin-bottom: 12rpx;
				color: #666;
				font-size: 24rpx;

				.uni-icons {
					margin-right: 8rpx;
					flex-shrink: 0;
				}

				text {
					line-height: 1.5;
				}
			}

			.review-content,
			.review-summary,
			.review-empty-content {
				line-height: 1.7;
				color: #444;
				padding: 8rpx 0; // 上下内边距
				border-top: 1rpx dashed #eee;
				margin-top: 8rpx;
			}

			.review-empty-content {
				color: #999;
				text-align: center;
			}
		}
	}

	.no-data {
		color: #999;
		font-size: 26rpx;
		text-align: center;
		padding: 20rpx 0;
	}
</style>