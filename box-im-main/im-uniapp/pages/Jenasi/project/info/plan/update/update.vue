<template>
	<view class="page-wrapper">
		<!-- 顶部标题 -->
		<view class="header-title">
			<text>编辑计划</text>
		</view>

		<view class="page-container">
			<!-- 加载状态 -->
			<view v-if="loading" class="state-container loading-state">
				<uni-load-more status="loading" :showText="false"></uni-load-more>
				<text class="state-text">加载计划数据中...</text>
			</view>

			<!-- 错误状态 -->
			<view v-else-if="initialFetchError" class="state-container error-state">
				<uni-icons type="close-filled" size="40" color="#f56c6c"></uni-icons>
				<text class="state-text error-text">加载失败: {{ initialFetchError }}</text>
				<button size="mini" @click="retryLoad">重试</button>
			</view>

			<!-- 表单内容 -->
			<!-- 确保 formData 已经加载后再渲染表单，否则 v-model 会报错 -->
			<!-- 使用 key，当 formData 完全改变时，重新渲染表单，有助于 uni-forms 正确初始化 -->
			<scroll-view v-else-if="formData" scroll-y class="form-scroll-view">
				<uni-forms ref="formRef" :modelValue="formData" :rules="formRules" label-position="top"
					:key="formData.taskId">

					<!-- 基本信息 -->
					<view class="card">
						<text class="card-title">基本信息</text>
						<uni-forms-item label="任务标题" name="taskName" required>
							<uni-easyinput type="text" v-model="formData.taskName" placeholder="请输入任务标题" trim="both" />
						</uni-forms-item>
						<uni-forms-item label="任务描述" name="taskDesc">
							<uni-easyinput type="textarea" v-model="formData.taskDesc" placeholder="请输入任务描述（可选）"
								autoHeight :maxlength="-1" />
						</uni-forms-item>
						<uni-forms-item label="所属项目">
							<!-- 项目名不可编辑，仅显示 -->
							<text class="form-value-text">{{ formData.projectName || '未知项目' }}</text>
						</uni-forms-item>
						<uni-forms-item label="当前状态" name="status" required>
							<uni-data-select v-model="formData.status" :localdata="statusOptions"
								placeholder="请选择任务状态"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="当前进度 (%)" name="progress" required>
							<!-- 使用 type="number" 并限制范围 -->
							<!-- uni-easyinput 的 min/max 是输入限制，uni-forms rules 提供验证 -->
							<uni-easyinput type="number" v-model="formData.progress" placeholder="请输入当前进度 (0-100)"
								:clearable="false" @blur="validateProgress('progress')" />
						</uni-forms-item>
					</view>

					<!-- 时间与成本 -->
					<view class="card">
						<text class="card-title">时间与成本</text>
						<uni-forms-item label="计划开始日期" name="plannedStartDate">
							<uni-datetime-picker type="date" v-model="formData.plannedStartDate" placeholder="请选择计划开始日期"
								return-type="string" :clear-icon="false" />
						</uni-forms-item>
						<uni-forms-item label="计划结束日期" name="plannedEndDate">
							<uni-datetime-picker type="date" v-model="formData.plannedEndDate" placeholder="请选择计划结束日期"
								return-type="string" :clear-icon="false" />
						</uni-forms-item>
						<uni-forms-item label="实际开始日期" name="actualStartDate">
							<uni-datetime-picker type="date" v-model="formData.actualStartDate"
								placeholder="请选择实际开始日期（可选）" return-type="string" :clear-icon="false" />
						</uni-forms-item>
						<uni-forms-item label="实际结束日期" name="actualEndDate">
							<uni-datetime-picker type="date" v-model="formData.actualEndDate"
								placeholder="请选择实际结束日期（可选）" return-type="string" :clear-icon="false" />
						</uni-forms-item>
						<!-- 金额/成本使用 digit 允许小数 -->
						<uni-forms-item label="预估价值" name="estimatedValue">
							<uni-easyinput type="digit" v-model="formData.estimatedValue" placeholder="请输入预估价值（可选）" />
						</uni-forms-item>
						<uni-forms-item label="预计成本" name="estimatedCost">
							<uni-easyinput type="digit" v-model="formData.estimatedCost" placeholder="请输入预计成本（可选）" />
						</uni-forms-item>
						<uni-forms-item label="实际成本" name="actualCost">
							<uni-easyinput type="digit" v-model="formData.actualCost" placeholder="请输入实际成本（可选）" />
						</uni-forms-item>
					</view>

					<!-- 负责人信息 -->
					<view class="card">
						<text class="card-title">负责人信息</text>
						<uni-forms-item label="选择部门" name="department">
							<picker class="picker" mode="selector" :range="departmentRange" range-key="departmentName"
								:value="selectedDepartmentIndex" @change="handleDepartmentChange">
								<view :class="['picker-value', selectedDepartmentIndex == -1 ? 'placeholder' : '']">
									{{ selectedDepartmentIndex != -1 && departments[selectedDepartmentIndex] ? departments[selectedDepartmentIndex].departmentName : '请选择部门' }}
									<uni-icons type="bottom" size="14" color="#999" class="picker-arrow"></uni-icons>
								</view>
							</picker>
						</uni-forms-item>

						<uni-forms-item label="选择人员" name="ownerId" required>
							<!-- Employee picker only enabled if a department is selected -->
							<picker class="picker" mode="selector" :range="employeeRange" range-key="name"
								:value="selectedEmployeeIndex" @change="handleEmployeeChange"
								:disabled="!employeeRange.length">
								<view
									:class="['picker-value', selectedEmployeeIndex == -1 && employeeRange.length > 0 ? 'placeholder' : '', !employeeRange.length ? 'placeholder': '']">
									<block v-if="selectedDepartmentIndex == -1 && departments.length > 0">请先选择部门</block>
									<block v-else-if="selectedDepartmentIndex != -1 && employeeRange.length == 0">
										该部门暂无人员</block>
									<block v-else-if="!departments.length">加载部门中或无部门可选</block>
									<block v-else-if="selectedEmployeeIndex == -1">请选择人员</block>
									<block v-else>
										{{ employeeRange[selectedEmployeeIndex]?.name || '请选择人员' }}
									</block>

									<uni-icons v-if="selectedDepartmentIndex != -1 && employeeRange.length > 0"
										type="bottom" size="14" color="#999" class="picker-arrow"></uni-icons>
									<text v-else
										class="no-arrow">{{ selectedDepartmentIndex == -1 ? '' : (employeeRange.length == 0 ? '' : '') }}</text>
								</view>
							</picker>
						</uni-forms-item>
						<!-- 负责人姓名和微信由选择人员自动填充，不直接编辑 -->
						<uni-forms-item label="负责人姓名">
							<text class="form-value-text">{{ formData.ownerName || '未设置' }}</text>
						</uni-forms-item>
						<uni-forms-item label="负责人微信">
							<text class="form-value-text">{{ formData.ownerWechat || '未提供' }}</text>
						</uni-forms-item>
					</view>

					<!-- 参与人员 -->
					<view class="card">
						<text class="card-title">参与人员</text>

						<!-- Trigger to open the participant selection modal -->
						<uni-forms-item label="选择参与人">
							<!-- 绑定 @click 事件到打开参与人弹窗的方法 -->
							<view class="select-participants-trigger" @click="openParticipantsModal">
								<text
									:class="['selected-count-text', taskParticipants.length == 0 ? 'placeholder' : '']">
									{{ taskParticipants.length > 0 ? `已选 ${taskParticipants.length} 人` : '点击选择参与人' }}
								</text>
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</uni-forms-item>


						<!-- 已选参与人列表 -->
						<uni-forms-item label="已选参与人">
							<view class="participant-list">
								<block v-if="taskParticipants.length > 0">
									<view v-for="participant in taskParticipants" :key="participant.id"
										class="participant-tag">
										<text>{{ participant.name }}</text>
										<uni-icons type="clear" size="16" color="#909399"
											@click="removeParticipant(participant.id)"></uni-icons>
									</view>
								</block>
								<view v-else class="placeholder">
									暂无参与人
								</view>
							</view>
						</uni-forms-item>
					</view>


					<!-- 复盘记录通常不在修改页面一次性编辑，需要单独管理 -->
					<!-- 这里的界面只展示可编辑的核心字段 -->

				</uni-forms>

				<!-- 底部保存按钮区域的占位符，确保最后一个card不会被覆盖 -->
				<view class="footer-spacer"></view>

			</scroll-view>

			<!-- 底部保存按钮区域 -->
			<view class="action-button-container">
				<button type="primary" @click="submitForm" class="submit-button" :loading="saving"
					:disabled="saving || loading || initialFetchError || !formData">
					{{ saving ? '保存中...' : '保存修改' }}
				</button>
			</view>

			<!-- 参与人员选择弹窗 -->
			<uni-popup ref="participantsPopup" type="center" @change="onPopupChange">
				<view class="popup-content">
					<!-- 弹窗头部 -->
					<view class="popup-header">
						<text class="popup-title">选择参与人</text>
						<!-- 绑定 @click 到关闭弹窗的方法 -->
						<uni-icons type="close" size="24" color="#999" @click="closeParticipantsModal"></uni-icons>
					</view>

					<!-- 弹窗内的部门选择器 -->
					<view class="popup-filter-section">
						<text class="filter-label">过滤部门:</text>
						<!-- Adding "全部部门" option to departmentRange for this picker -->
						<picker class="picker filter-picker" mode="selector"
							:range="['全部部门', ...departmentRange.map(d => d.departmentName)]"
							:value="selectedDepartmentIndexForParticipants + 1"
							@change="handleDepartmentChangeForParticipants">
							<view
								:class="['picker-value', selectedDepartmentIndexForParticipants == -1 ? 'placeholder' : '']">
								{{ selectedDepartmentIndexForParticipants == -1 ? '全部部门' : departmentRange[selectedDepartmentIndexForParticipants]?.departmentName || '全部部门' }}
								<uni-icons type="bottom" size="14" color="#999" class="picker-arrow"></uni-icons>
							</view>
						</picker>
					</view>

					<!-- 搜索框 -->
					<view class="popup-search">
						<uni-easyinput prefixIcon="search" placeholder="搜索人员姓名" trim="both"
							v-model="modalSearchQuery"></uni-easyinput>
					</view>

					<!-- 带有高亮效果的员工列表 -->
					<scroll-view scroll-y class="popup-employee-list">
						<block v-if="loadingAllEmployees"> <!-- Indicate loading state -->
							<view class="popup-empty-state">
								<uni-load-more status="loading" :showText="false"></uni-load-more>
								<text>加载员工列表中...</text>
							</view>
						</block>
						<block v-else-if="allEmployees.length == 0">
							<view class="popup-empty-state">
								<text>暂无员工数据</text>
							</view>
						</block>
						<block v-else-if="filteredEmployeesForModal.length > 0">
							<!-- Loop over the filtered list -->
							<view v-for="employee in filteredEmployeesForModal" :key="employee.id"
								:class="['employee-item', { 'selected': selectedEmployeeIdsForModal.has(employee.id) }]"
								@click="toggleEmployeeSelection(employee)">
								<text class="employee-name">{{ employee.name }}</text>
								<!-- Optional: Show department name -->
								<text v-if="selectedDepartmentIndexForParticipants == -1 && employee.departmentName"
									class="employee-department">({{ employee.departmentName }})</text>
								<!-- 选中图标 -->
								<uni-icons v-if="selectedEmployeeIdsForModal.has(employee.id)" type="checkmarkempty"
									size="20" color="#007aff" class="selected-checkmark"></uni-icons>
							</view>
						</block>
						<block v-else>
							<view class="popup-empty-state">
								<text
									v-if="selectedDepartmentIndexForParticipants != -1 && !modalSearchQuery">该部门暂无人员</text>
								<text v-else-if="modalSearchQuery">无匹配人员</text>
								<text v-else>请选择部门或部门暂无人员</text>
							</view>
						</block>
					</scroll-view>

					<!-- 弹窗底部按钮 -->
					<view class="popup-footer">
						<!-- 绑定 @click 到关闭弹窗的方法 -->
						<button size="mini" type="default" @click="closeParticipantsModal">取消</button>
						<!-- 绑定 @click到确认选择的方法 -->
						<button size="mini" type="primary" @click="confirmParticipantsSelection">确定</button>
					</view>
				</view>
			</uni-popup>

		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		computed,
		nextTick
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app';

	import UNI_APP from '@/.env.js'



	// const test = ref("test") // Debug variable, remove if not needed

	// --- Constants ---
	const BASE_URL = UNI_APP.PROJECT; // <--- Replace with your actual API base URL

	// Updated Status Options List
	const STATUS_OPTIONS_RAW = [
		'草稿', // 未正式提交
		'待处理', // 已提交但未开始
		'进行中', // 正在进行
		'暂停/挂起', // 临时停止
		'已完成', // 成功完成
		'失败', // 未成功完成
		'已取消', // 主动取消
		'已拒绝', // 如审核不通过
		'已归档', // 长期存储，不再活跃
	];


	// --- Reactive State ---
	const taskId = ref(null); // Current task ID being edited
	const loading = ref(true); // Initial page load state
	const saving = ref(false); // Save operation state
	const initialFetchError = ref(null); // Stores initial load error message
	const loadingAllEmployees = ref(false); // State specifically for loading all employees

	// Form data, initialized as null, populated after loading
	const formData = ref(null); // Use ref for the object, allows it to be null initially

	// Shared Employee Data
	const allEmployees = ref([]); // All employees list fetched once (should include departmentId and departmentName)

	// Owner Picker Related Data
	const departments = ref([]); // All departments list
	const selectedDepartmentIndex = ref(-1); // Owner department picker current selected index
	const selectedEmployeeIndex = ref(-1); // Owner employee picker current selected index

	// Participants Data and Modal State
	const taskParticipants = ref([]); // Current task participants list ({id, name, wechat})
	const participantsPopup = ref(null); // ref for uni-popup component
	const selectedDepartmentIndexForParticipants = ref(-
	1); // Participant department picker index *inside modal* (-1 for All)
	const modalSearchQuery = ref(''); // Modal search input
	const selectedEmployeeIdsForModal = ref(
	new Set()); // Set of selected employee IDs in the modal (changes before confirm)


	// Uni-forms ref
	const formRef = ref(null);


	// --- Computed Properties ---
	// Convert status options for uni-data-select
	const statusOptions = computed(() => {
		return STATUS_OPTIONS_RAW.map(status => ({
			value: status,
			text: status
		}));
	});

	// Department list for both pickers (computed from state)
	const departmentRange = computed(() => departments.value);

	// Filtered employee list for the OWNER picker (based on owner department selection)
	// This list is only used to select the SINGLE owner.
	const employeeRange = computed(() => {
		if (selectedDepartmentIndex.value == -1 || departments.value.length == 0) {
			return [];
		}
		const selectedDeptId = departments.value[selectedDepartmentIndex.value]?.id;
		if (!selectedDeptId) return [];
		return allEmployees.value.filter(emp => emp.departmentId == selectedDeptId) || [];
	});

	// Filtered employee list for the PARTICIPANT MODAL (based on modal department filter)
	// This list is the source for the search filter
	const employeesForModalDeptFilter = computed(() => {
		if (selectedDepartmentIndexForParticipants.value == -1 || departments.value.length == 0) {
			// If no department is selected in the modal (-1 index), show all employees
			return allEmployees.value;
		}
		const selectedDeptId = departments.value[selectedDepartmentIndexForParticipants.value]?.id;
		if (!selectedDeptId) return []; // Should not happen if index is valid
		return allEmployees.value.filter(emp => emp.departmentId == selectedDeptId) || [];
	});

	// Final filtered employees shown in the PARTICIPANT MODAL list (department filter + search query)
	const filteredEmployeesForModal = computed(() => {
		const query = modalSearchQuery.value.toLowerCase().trim();
		const sourceList = employeesForModalDeptFilter.value; // This list is already filtered by department

		if (!sourceList || sourceList.length == 0) {
			return [];
		}

		if (!query) {
			return sourceList; // If no search query, return the list filtered by department
		}
		// Filter by employee name (case-insensitive)
		return sourceList.filter(emp => emp && emp.name && emp.name.toLowerCase().includes(query));
	});


	// --- Utility Functions ---

	// Format date to 'YYYY-MM-DD' for picker binding and display
	// Handles potential ISO strings or Date objects
	const formatDateForPicker = (dateString) => {
		if (!dateString) return null;
		let date;
		if (typeof dateString == 'string') {
			// Attempt to parse ISO strings or simple YYYY-MM-DD
			const parts = dateString.split('T')[0].split('-');
			if (parts.length == 3) {
				// Create date object in UTC to avoid local timezone issues for YYYY-MM-DD
				date = new Date(Date.UTC(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2])));
			} else {
				// Fallback for other string formats, might have timezone issues
				date = new Date(dateString);
			}
		} else if (dateString instanceof Date) {
			date = dateString; // Already a Date object
		} else {
			return null; // Not a recognizable format
		}

		if (isNaN(date.getTime())) {
			console.warn("Invalid date provided for formatting:", dateString);
			return null;
		}

		// Get UTC date parts to ensure YYYY-MM-DD format is based on the intended date, not local timezone offset
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, '0');
		const day = String(date.getUTCDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	};


	// Unified API Request Wrapper
	const apiRequest = (options) => {
		return new Promise((resolve, reject) => {
			// Only show global loading when specifically requested or needed
			if (options.showGlobalLoading != false) {
				uni.showLoading({
					title: options.loadingTitle || '请稍候...',
					mask: true
				});
			}

			uni.request({
				url: BASE_URL + options.url,
				method: options.method || 'GET',
				data: options.data || {},
				header: options.header || {}, // Add custom headers if needed (e.g., Auth tokens)
				timeout: options.timeout || 15000,
				success: (res) => {
					if (res.data.code == "200") {
						resolve(res.data.data); // Resolve with the actual data payload
					} else {
						// Handle API business logic errors (e.g., code != 200)
						const errorMsg = res.data?.message || `请求失败 (${res.statusCode})`;
						console.error(`API Business Error (${options.url}):`, errorMsg, res);
						// Reject with the error message
						reject(errorMsg);
					}
				},
				fail: (err) => {
					// Handle network or request errors
					console.error(`API Network Error (${options.url}):`, err);
					let errorMsg = '网络错误，请稍后重试';
					if (err.errMsg?.includes('timeout')) {
						errorMsg = '请求超时，请检查网络';
					} else if (err.errMsg) {
						errorMsg = err.errMsg;
					}
					reject(errorMsg); // Reject with the network error message
				},
				complete: () => {
					if (options.showGlobalLoading != false) {
						uni.hideLoading();
					}
				}
			});
		});
	};


	// --- Form Validation Rules ---
	const formRules = reactive({
		taskName: {
			rules: [{
				required: true,
				errorMessage: '任务标题不能为空'
			}, {
				minLength: 2,
				maxLength: 50,
				errorMessage: '任务标题长度在 {minLength} 到 {maxLength} 个字符'
			}],
			validateTrigger: 'blur' // Trigger validation on blur
		},
		status: {
			rules: [{
				required: true,
				errorMessage: '请选择任务状态'
			}],
			validateTrigger: 'change' // Trigger on Picker/Select change
		},
		progress: {
			rules: [{
				required: true,
				errorMessage: '当前进度不能为空'
			}, {
				// Custom validator for number check and range
				validator: (rule, value, data, callback) => {
					if (value == '' || value == null || value == undefined) {
						return true; // Handled by required rule
					}
					const num = parseFloat(value);
					if (isNaN(num)) {
						callback('当前进度必须是数字');
						return false;
					}
					if (num < 0 || num > 100) {
						callback('进度必须在 0-100 之间');
						return false;
					}
					return true; // Validation passed
				},
				errorMessage: '进度必须是 0-100 之间的数字', // Fallback message
			}],
			validateTrigger: 'blur'
		},
		// Optional fields validation (basic type check)
		estimatedValue: {
			rules: [{
				validator: (rule, value, data, callback) => {
					if (value == '' || value == null || value == undefined)
				return true; // Optional field
					const num = parseFloat(value);
					if (isNaN(num)) {
						callback('预估价值必须是数字');
						return false;
					}
					return true;
				},
				errorMessage: '预估价值必须是数字'
			}],
			validateTrigger: 'blur'
		},
		estimatedCost: {
			rules: [{
				validator: (rule, value, data, callback) => {
					if (value == '' || value == null || value == undefined)
				return true; // Optional field
					const num = parseFloat(value);
					if (isNaN(num)) {
						callback('预计成本必须是数字');
						return false;
					}
					return true;
				},
				errorMessage: '预计成本必须是数字'
			}],
			validateTrigger: 'blur'
		},
		actualCost: {
			rules: [{
				validator: (rule, value, data, callback) => {
					if (value == '' || value == null || value == undefined)
				return true; // Optional field
					const num = parseFloat(value);
					if (isNaN(num)) {
						callback('实际成本必须是数字');
						return false;
					}
					return true;
				},
				errorMessage: '实际成本必须是数字'
			}],
			validateTrigger: 'blur'
		},
		// Owner ID validation: Required *only if* a department is selected AND that department has employees (employeeRange is not empty)
		ownerId: {
			rules: [{
				validator: (rule, value, data, callback) => {
					// Check if a department is selected and if the computed employee list for that department is not empty
					if (selectedDepartmentIndex.value != -1 && employeeRange.value.length > 0) {
						// If a department with employees is selected, owner must be selected
						if (value == null || value == undefined) {
							callback('请选择负责人'); // Validation fails
							return false;
						}
					}
					// Otherwise, owner selection is optional (or handled by other states like no departments/no employees)
					return true; // Validation passes
				}
			}],
			validateTrigger: 'change', // Trigger when picker value changes
			errorMessage: '请选择负责人' // Fallback message
		},
		// Add date validation rules if needed
	});


	// --- API Fetching Functions ---

	// Fetch Task Details - Assumes it returns task data including owner details and participant list
	const fetchTaskDetails = async (id) => {
		try {
			const data = await apiRequest({
				url: `/task/selectById/${id}`, // <-- Replace with your actual task details API address
				loadingTitle: '加载计划数据...'
			});
			if (data) {
				// Use fetched data to initialize formData
				const initialData = {
					taskId: data.taskId || id, // Ensure taskId exists
					projectId: data.projectId,
					projectName: data.projectName, // Assuming project name is returned
					taskName: data.taskName || '',
					taskDesc: data.taskDesc || '',
					plannedStartDate: formatDateForPicker(data.plannedStartDate),
					plannedEndDate: formatDateForPicker(data.plannedEndDate),
					actualStartDate: formatDateForPicker(data.actualStartDate),
					actualEndDate: formatDateForPicker(data.actualEndDate),
					estimatedValue: data.estimatedValue != null ? String(data.estimatedValue) :
					'', // Convert numbers to string for easyinput
					estimatedCost: data.estimatedCost != null ? String(data.estimatedCost) : '',
					actualCost: data.actualCost != null ? String(data.actualCost) : '',
					progress: data.progress != null ? String(Math.max(0, Math.min(100, parseFloat(data
						.progress)))) : '0', // Ensure progress is 0-100 and string
					status: data.status && STATUS_OPTIONS_RAW.includes(data.status) ? data.status :
						STATUS_OPTIONS_RAW[0], // Validate status or default
					ownerId: data.ownerId || null, // Owner ID can be null
					ownerName: data.ownerName || '',
					ownerWechat: data.ownerWechat || '', // Assuming wechat name is returned
				};
				formData.value = initialData; // Assign the processed data
				taskParticipants.value = data.participants || []; // Assume participants list is returned here

				console.log('Fetched Task Data:', JSON.parse(JSON.stringify(formData.value)));
				console.log('Fetched Participants:', JSON.parse(JSON.stringify(taskParticipants.value)));

				return initialData; // Return initial data object
			} else {
				throw new Error("未找到任务数据");
			}
		} catch (error) {
			console.error("Failed to fetch task details:", error);
			initialFetchError.value = error.toString();
			formData.value = null; // Ensure formData is null on error
			taskParticipants.value = []; // Ensure participants is empty on error
			throw error; // Re-throw to stop loadData process
		}
	};

	// Fetch All Departments
	const fetchDepartments = async () => {
		try {
			const data = await apiRequest({
				url: '/departments/selectAll', // <-- Replace with your actual departments API
				showGlobalLoading: false
			});
			return data || [];
		} catch (error) {
			console.error("Failed to fetch departments:", error);
			uni.showToast({
				title: '加载部门列表失败',
				icon: 'none'
			});
			return [];
		}
	};

	// Fetch All Employees
	// Important: Ensure this API returns departmentId AND departmentName for each employee
	const fetchAllEmployees = async () => {
		loadingAllEmployees.value = true; // Set specific loading state
		try {
			// Assume this endpoint returns List<EmployeeDTO> including departmentId, name, wechatName, departmentName
			const data = await apiRequest({
				url: '/employees/selectAll', // <-- Replace with your actual "fetch all employees" API
				showGlobalLoading: false // Don't show global loading if already shown by fetchTaskDetails
			});
			console.log("Fetched all employees:", data);
			return data || [];
		} catch (error) {
			console.error("Failed to fetch all employees:", error);
			uni.showToast({
				title: '加载员工列表失败',
				icon: 'none'
			});
			return [];
		} finally {
			loadingAllEmployees.value = false; // End specific loading state
		}
	};


	// --- Event Handlers ---

	// Validate and normalize progress input on blur
	const validateProgress = (field) => {
		let value = formData.value[field];
		if (value == '' || value == null || value == undefined) {
			// If field is empty, clear validation state or rely on 'required' rule
			formRef.value?.clearValidate(field);
			return;
		}
		const num = parseFloat(value);
		if (!isNaN(num)) {
			// Limit to 0-100 range and update the model value as a string
			formData.value[field] = String(Math.max(0, Math.min(100, num)));
		}
		nextTick(() => {
			// Manually trigger validation for the field after potential model update
			formRef.value?.validateField(field);
		});
	};

	// Handle Owner Department change
	const handleDepartmentChange = (e) => {
		const index = parseInt(e.detail.value); // Picker returns index

		// Ensure index is valid and selection is different
		// departmentRange is 0-indexed based on departments.value
		if (index < 0 || index >= departments.value.length) {
			return; // Should not happen with a well-formed picker range
		}

		// If the selected department is the same as current, do nothing
		if (index == selectedDepartmentIndex.value) return;


		selectedDepartmentIndex.value = index;

		// Reset current owner selection as the department changed
		selectedEmployeeIndex.value = -1; // Reset selected index for the new employee list
		formData.value.ownerId = null;
		formData.value.ownerName = '';
		formData.value.ownerWechat = '';

		console.log(`负责人部门已更新为索引 ${index}.`);
		// The employeeRange computed property will automatically update

		// Manually trigger validation for owner related fields after the model is updated
		nextTick(() => {
			formRef.value?.validateField(['ownerId']);
		});
	};

	// Handle Owner Employee change
	const handleEmployeeChange = (e) => {
		const index = parseInt(e.detail.value);
		// Ensure index is valid for the *current* computed employeeRange list
		// This list changes based on selectedDepartmentIndex
		if (index < 0 || index >= employeeRange.value.length) {
			return; // Should not happen with a well-formed picker range based on employeeRange
		}

		selectedEmployeeIndex.value = index;
		const selectedEmp = employeeRange.value[index]; // Get employee from the computed list

		// Check if selectedEmp is valid before updating
		if (!selectedEmp || !selectedEmp.id) {
			console.warn("Selected employee object is invalid:", selectedEmp);
			// Reset selection if invalid
			selectedEmployeeIndex.value = -1;
			formData.value.ownerId = null;
			formData.value.ownerName = '';
			formData.value.ownerWechat = '';
			return;
		}


		// Update formData with selected owner details
		formData.value.ownerId = selectedEmp.id;
		formData.value.ownerName = selectedEmp.name || '';
		formData.value.ownerWechat = selectedEmp.wechatName || ''; // Assuming wechatName from API

		console.log(`负责人人员已更新为索引 ${index}, 员工ID ${selectedEmp.id}.`);

		// Manually trigger validation for owner ID after model update
		nextTick(() => {
			formRef.value?.validateField('ownerId');
		});
	};


	// --- Participant Modal Handlers ---

	// Handle Participant Department change *inside the modal*
	const handleDepartmentChangeForParticipants = (e) => {
		const pickerIndex = parseInt(e.detail.value); // Picker index (0 is '全部部门', 1+ are departments)
		// Map picker index to internal state index (-1 for '全部', 0+ for departments)
		const newIndex = pickerIndex - 1;

		if (newIndex == selectedDepartmentIndexForParticipants.value) {
			return; // No change
		}

		selectedDepartmentIndexForParticipants.value = newIndex;
		modalSearchQuery.value = ''; // Clear search keyword when department filter changes

		console.log(`参与人弹窗部门过滤已更新为内部索引 ${newIndex}.`);
		// The filteredEmployeesForModal computed property will automatically update
	};

	// Open Participant Selection Modal
	const openParticipantsModal = () => {
		if (!participantsPopup.value) {
			console.error("参与人弹窗的 ref 不可用。");
			return;
		}

		// 1. Initialize selected IDs for modal from current task participants
		selectedEmployeeIdsForModal.value.clear();
		taskParticipants.value.forEach(p => {
			if (p && p.id != null && p.id != undefined) { // Ensure ID is valid
				selectedEmployeeIdsForModal.value.add(p.id);
			}
		});
		selectedEmployeeIdsForModal.value = new Set(selectedEmployeeIdsForModal.value); // Trigger reactivity

		// 2. Reset modal search query
		modalSearchQuery.value = '';

		// 3. Set the initial department filter in the modal
		// Option A: Default to -1 (All Departments) - Recommended for participant selection
		selectedDepartmentIndexForParticipants.value = -1;
		// Option B: Default to the owner's department if one is selected (Less common for participant selection)
		// if (selectedDepartmentIndex.value != -1) {
		//      selectedDepartmentIndexForParticipants.value = selectedDepartmentIndex.value;
		// } else {
		//      selectedDepartmentIndexForParticipants.value = -1;
		// }
		console.log(`弹窗打开，初始参与人部门过滤设置为内部索引 ${selectedDepartmentIndexForParticipants.value}.`);
		// The filteredEmployeesForModal computed property will automatically update

		// 4. Open the popup
		participantsPopup.value.open();
		console.log("打开参与人弹窗");
	};

	// Close Participant Selection Modal
	const closeParticipantsModal = () => {
		if (participantsPopup.value) {
			participantsPopup.value.close();
			console.log("关闭参与人弹窗");
			// On cancel/close without confirming, discard the temporary selection state
			// This is handled by re-initializing selectedEmployeeIdsForModal on open
			// We can optionally clear here for certainty, but opening resets it anyway.
			// selectedEmployeeIdsForModal.value.clear();
			// selectedEmployeeIdsForModal.value = new Set(); // Trigger reactive update
			modalSearchQuery.value = ''; // Clear search keyword
			selectedDepartmentIndexForParticipants.value = -1; // Reset modal department filter
		}
	};

	// Listen to popup state changes (optional)
	const onPopupChange = (e) => {
		console.log('Popup state changed:', e);
		if (!e.show) {
			console.log("Popup closed.");
			// If popup closes unexpectedly (e.g. by clicking mask), cleanup state
			modalSearchQuery.value = '';
			selectedDepartmentIndexForParticipants.value = -1;
			// Don't clear selectedEmployeeIdsForModal here,
			// it needs to persist if closed by mask and reopened,
			// it's only reset on openParticipantsModal call.
		}
	};


	// Toggle employee selection in the modal
	const toggleEmployeeSelection = (employee) => {
		if (!employee || !employee.id) return;

		const selectedIds = selectedEmployeeIdsForModal.value;
		if (selectedIds.has(employee.id)) {
			selectedIds.delete(employee.id);
		} else {
			selectedIds.add(employee.id);
		}
		// Manually create a new Set to ensure reactivity updates computed properties/template
		selectedEmployeeIdsForModal.value = new Set(selectedIds);
		console.log("Current selected IDs in modal:", Array.from(selectedEmployeeIdsForModal.value));
	};

	// Confirm selection and update the main taskParticipants list
	const confirmParticipantsSelection = () => {
		const selectedIds = selectedEmployeeIdsForModal.value;
		const confirmedParticipants = [];

		// Iterate through the *full* allEmployees list to find matching IDs
		// This is more robust than relying on the currently filtered modal list (`filteredEmployeesForModal`)
		allEmployees.value.forEach(emp => {
			if (emp && emp.id != null && emp.id != undefined && selectedIds.has(emp.id)) {
				// Build participant object matching backend's expected structure
				// Assuming 'wechatName' is the property holding wechat info
				confirmedParticipants.push({
					id: emp.id,
					name: emp.name || '',
					wechat: emp.wechatName || '' // Make sure this matches your backend EmployeeDTO
				});
			}
		});

		taskParticipants.value = confirmedParticipants; // Update the main taskParticipants list
		console.log("Task Participants list updated:", JSON.parse(JSON.stringify(taskParticipants.value)));

		closeParticipantsModal(); // Close the modal
	};

	// Remove a participant tag from the main list
	const removeParticipant = (participantId) => {
		// Ensure participantId is valid
		if (participantId == null || participantId == undefined) return;

		// Filter out the participant to remove from the main list
		taskParticipants.value = taskParticipants.value.filter(p => p.id != participantId);
		console.log("Removed participant ID:", participantId, "Current list:", JSON.parse(JSON.stringify(
			taskParticipants.value)));

		// If the modal is open, also update the internal selected state to reflect the removal
		if (participantsPopup.value && participantsPopup.value.isShow) {
			if (selectedEmployeeIdsForModal.value.has(participantId)) {
				selectedEmployeeIdsForModal.value.delete(participantId);
				selectedEmployeeIdsForModal.value = new Set(selectedEmployeeIdsForModal.value); // Trigger reactivity
				console.log("Removed ID from modal selected state.");
			}
		}
	};


	// --- Initial Data Loading Sequence ---
	const loadData = async () => {
		if (!taskId.value) {
			initialFetchError.value = "任务ID无效";
			loading.value = false;
			uni.showToast({
				title: "无法加载任务，ID丢失",
				icon: "error",
				duration: 3000
			});
			return;
		}

		loading.value = true;
		initialFetchError.value = null; // Reset error state on load/retry

		// --- Reset relevant states for a fresh load ---
		formData.value = null; // Keep null initially
		departments.value = []; // Initialize with empty array
		allEmployees.value = []; // Initialize with empty array
		selectedDepartmentIndex.value = -1; // Reset owner department
		selectedEmployeeIndex.value = -1; // Reset owner employee
		taskParticipants.value = []; // Initialize with empty array
		selectedDepartmentIndexForParticipants.value = -1; // Reset modal filter
		modalSearchQuery.value = ''; // Reset modal search
		selectedEmployeeIdsForModal.value.clear(); // Clear modal selection set
		selectedEmployeeIdsForModal.value = new Set(); // Ensure reactivity


		try {
			// 1. Fetch Task Details (Populates formData and taskParticipants)
			const initialData = await fetchTaskDetails(taskId.value);
			const initialOwnerId = initialData?.ownerId;

			// 2. Fetch Departments and All Employees concurrently
			// Use Promise.allSettled if one failure shouldn't stop the others,
			// but here we likely need both for the page to function correctly.
			const [departmentResult, employeeResult] = await Promise.allSettled([
				fetchDepartments(),
				fetchAllEmployees()
			]);

			if (departmentResult.status == 'fulfilled') {
				departments.value = departmentResult.value || [];
			} else {
				console.error("Failed to fetch departments:", departmentResult.reason);
				// Keep departments empty and potentially show a message
			}

			if (employeeResult.status == 'fulfilled') {
				allEmployees.value = employeeResult.value || [];
			} else {
				console.error("Failed to fetch all employees:", employeeResult.reason);
				// Keep allEmployees empty and potentially show a message
			}


			// 3. Process Owner based on initialOwnerId and fetched employee/department lists
			if (initialOwnerId != null && initialOwnerId != undefined && allEmployees.value.length > 0 &&
				departments.value.length > 0) {
				const owner = allEmployees.value.find(emp => emp.id == initialOwnerId);
				if (owner && owner.departmentId != null && owner.departmentId !=
					undefined) { // Assume employee object has departmentId
					const deptIndex = departments.value.findIndex(d => d.id == owner.departmentId);
					if (deptIndex != -1) {
						selectedDepartmentIndex.value = deptIndex;
						// The employeeRange computed property is now reactive to selectedDepartmentIndex

						// Need nextTick because employeeRange depends on selectedDepartmentIndex update
						await nextTick();
						// Re-find the employee in case the list structure changed or was filtered
						// (employeeRange filters allEmployees)
						const empIndex = employeeRange.value.findIndex(emp => emp.id == initialOwnerId);
						if (empIndex != -1) {
							selectedEmployeeIndex.value = empIndex;
							// Owner details in formData should already be correct from fetchTaskDetails
							// and match the selected employee.
							console.log(
								`Initial owner found and matched: Dept Index ${deptIndex}, Emp Index ${empIndex}`
								);
						} else {
							console.warn(
								`Owner ID ${initialOwnerId} found in all employees but not in employeeRange for department ${owner.departmentId}. This is unexpected.`
								);
							// If owner isn't in the employeeRange for their department, reset selection
							selectedDepartmentIndex.value = -1;
							selectedEmployeeIndex.value = -1;
							if (formData.value) {
								formData.value.ownerId = null; // Explicitly clear owner ID
								formData.value.ownerName = '';
								formData.value.ownerWechat = '';
							}
						}
					} else {
						console.warn(
							`Owner ID ${initialOwnerId} found, but department ID ${owner.departmentId} not found in department list.`
							);
						// If owner's department not found, reset owner selection
						selectedDepartmentIndex.value = -1;
						selectedEmployeeIndex.value = -1;
						if (formData.value) {
							formData.value.ownerId = null; // Explicitly clear owner ID
							formData.value.ownerName = '';
							formData.value.ownerWechat = '';
						}
					}
				} else {
					console.warn(
						`Initial owner ID ${initialOwnerId} found, but missing departmentId or employee object invalid.`
						);
					// If owner employee object invalid, reset owner selection
					selectedDepartmentIndex.value = -1;
					selectedEmployeeIndex.value = -1;
					if (formData.value) {
						formData.value.ownerId = null; // Explicitly clear owner ID
						formData.value.ownerName = '';
						formData.value.ownerWechat = '';
					}
				}
			} else if (initialOwnerId != null && initialOwnerId != undefined) {
				console.warn(
					`Initial owner ID ${initialOwnerId} exists, but failed to fetch departments or all employees, or lists are empty. Cannot select owner in UI.`
					);
				// If data fetching failed or lists empty, keep owner ID/Name/Wechat from task details
				// But reset selection UI as pickers cannot be populated correctly.
				selectedDepartmentIndex.value = -1;
				selectedEmployeeIndex.value = -1;
				// ownerId, ownerName, ownerWechat remain as fetched in formData
			} else {
				console.log("No initial owner ID found in task details or owner is null.");
				// Reset owner selection if no owner id initially or owner is null
				selectedDepartmentIndex.value = -1;
				selectedEmployeeIndex.value = -1;
				if (formData.value) { // Only if formData was successfully loaded
					formData.value.ownerId = null; // Explicitly ensure null
					formData.value.ownerName = '';
					formData.value.ownerWechat = '';
				}
			}

			// 4. Participants list is already populated by fetchTaskDetails.

			// 5. Set initial Participant Modal department filter
			selectedDepartmentIndexForParticipants.value = -1; // Default to "All Departments"


			console.log("Load data complete.");


		} catch (error) {
			// Error was already logged and initialFetchError set in fetch functions or here
			console.error("Overall initial data load process failed:", error);
			// Ensure initialFetchError is set if it wasn't by a specific fetch function error
			if (!initialFetchError.value) {
				initialFetchError.value = `加载失败: ${error.toString()}`;
			}
			// Ensure formData is null to show error state
			formData.value = null;
			// Ensure arrays are empty on error
			departments.value = [];
			allEmployees.value = [];
			taskParticipants.value = [];


		} finally {
			loading.value = false; // Loading finishes (success or failure)
		}
	};


	// --- Form Submission ---
	const submitForm = async () => {
		if (!formRef.value || !formData.value) {
			uni.showToast({
				title: '表单或数据未加载',
				icon: 'none'
			});
			return;
		}

		// Manual form validation
		let isValid = false;
		try {
			isValid = await formRef.value.validate();
			console.log('UniForms validation result:', isValid);
		} catch (validationErrors) {
			console.error('UniForms validation failed:', validationErrors);
			isValid = false;
		}


		if (!isValid) {
			console.log('表单验证失败。');
			// uni-forms typically shows error messages automatically
			uni.showToast({
				title: '请检查表单填写',
				icon: 'none'
			});
			return;
		}

		// Additional check for owner if department with employees is selected
		// Now employeeRange is reactive, so we check its length
		// This check is partially covered by the form rule on ownerId, but this provides an extra toast
		// and ensures the validation is triggered if needed.
		if (selectedDepartmentIndex.value != -1 && employeeRange.value.length > 0 && (formData.value.ownerId ==
				null || formData.value.ownerId == undefined)) {
			uni.showToast({
				title: '请选择负责人',
				icon: 'none'
			});
			console.log('Validation failed: department with employees selected but no owner.');
			// Trigger validation message visually for ownerId field
			formRef.value?.validateField('ownerId');
			return;
		}


		saving.value = true; // Set saving state
		try {
			// Prepare main task data payload - ensure correct types
			const payload = {
				...formData.value, // Copy all fields from formData
				// Convert number strings to actual numbers or null
				estimatedValue: formData.value.estimatedValue != '' ? parseFloat(formData.value
					.estimatedValue) : null,
				estimatedCost: formData.value.estimatedCost != '' ? parseFloat(formData.value.estimatedCost) :
					null,
				actualCost: formData.value.actualCost != '' ? parseFloat(formData.value.actualCost) : null,
				progress: parseFloat(formData.value.progress), // Progress is required 0-100 number
				ownerId: formData.value.ownerId // Can be null if no owner selected
			};

			// If ownerId is explicitly set to null/undefined (e.g., user cleared selection or department had no employees),
			// ensure related name/wechat are also cleared in the payload sent to backend.
			if (payload.ownerId == null || payload.ownerId == undefined) {
				payload.ownerName = '';
				payload.ownerWechat = '';
			} else {
				// If ownerId IS set, make sure ownerName/Wechat reflect the *currently selected* employee,
				// not just what was loaded initially. Although handleEmployeeChange should keep these in sync.
				// This step is belts-and-surplice.
				const currentOwner = employeeRange.value.find(emp => emp.id == payload.ownerId);
				if (currentOwner) {
					payload.ownerName = currentOwner.name || '';
					payload.ownerWechat = currentOwner.wechatName || ''; // Use wechatName from employee object
				} else {
					// Should not happen if handleEmployeeChange works, but good fallback
					console.warn(
						`Owner ID ${payload.ownerId} set, but matching employee not found in current range.`);
					payload.ownerName = '';
					payload.ownerWechat = '';
					payload.ownerId = null; // Reset ownerId if employee not found
				}
			}


			console.log("Sending main task update Payload:", JSON.parse(JSON.stringify(payload)));

			// Send main task data update request
			await apiRequest({
				url: '/task/update', // <-- Replace with your actual task update API
				method: 'POST', // Use POST or PUT as per backend
				data: payload,
				loadingTitle: '正在保存...'
			});

			// Prepare and send participant data (if any)
			// Assuming participants are updated via a separate API call that replaces the list
			// The payload sent to the backend should be a list of participant objects with ID.
			const participantsPayload = taskParticipants.value.map(p => ({
				// Ensure participant object structure matches backend DTO for participant update
				// Assuming backend expects { id: employeeId } or { userId: employeeId } etc.
				// Let's assume it expects the simple participant object { id, name, wechat } for flexibility
				id: p.id,
				name: p.name || '',
				wechat: p.wechat || ''
			}));

			console.log("Sending participants Payload:", JSON.parse(JSON.stringify(participantsPayload)));

			try {
				// Send participant list update request (e.g., replace existing list)
				// This endpoint should take task ID and the list of participant objects
				await apiRequest({
					url: `/task/updateParticipantsById/${taskId.value}`, // <-- Replace with your actual participant update API
					method: 'POST', // Use POST or PUT as per backend
					data: participantsPayload,
					showGlobalLoading: false // Don't show duplicate loading indicator
				});
				console.log("Participants saved successfully.");
			} catch (participantSaveError) {
				console.error("Failed to save participants:", participantSaveError);
				// Show a warning toast for participant saving failure but continue
				uni.showToast({
					title: `参与人保存失败: ${participantSaveError.toString()}`,
					icon: 'none',
					duration: 3000
				});
				// Do not re-throw, main task update was successful
			}


			uni.showToast({
				title: '保存成功',
				icon: 'success',
				duration: 1500
			});

			// Navigate back and notify previous page to refresh
			setTimeout(() => {
				const pages = getCurrentPages();
				if (pages.length >= 2) {
					// Send event to previous page (if it's listening)
					uni.navigateBack();
				} else {
					// Handle case where there's no previous page (e.g., direct entry)
					// uni.redirectTo({ url: '/pages/some/default/list' });
				}
			}, 500);

		} catch (error) {
			console.error("Failed to save task:", error);
			uni.showToast({
				title: `保存失败: ${error.toString()}`,
				icon: 'none',
				duration: 3000
			});
		} finally {
			saving.value = false; // End saving state
		}
	};

	// Retry loading data after initial failure
	const retryLoad = () => {
		loadData(); // Re-call the loading function
	};


	// --- Lifecycle Hook ---
	onLoad((options) => {
		if (options && options.id) {
			taskId.value = options.id; // Get task ID from page parameters
			loadData(); // Call async loading function
		} else {
			console.error("Missing task ID, cannot load edit page.");
			initialFetchError.value = "任务ID丢失，无法加载页面";
			loading.value = false;
			uni.showToast({
				title: "加载失败，任务ID丢失",
				icon: "error",
				duration: 3000
			});
			// Optional: Redirect back after a short delay
			// setTimeout(() => { uni.navigateBack(); }, 2000);
		}
	});
</script>

<style lang="scss" scoped>
	// 页面整体容器，使用 flex 布局
	.page-wrapper {
		display: flex;
		flex-direction: column;
		min-height: 100vh; // 确保页面占满整个屏幕高度
		background-color: #f4f6f8; // 页面背景色
	}

	// 顶部标题
	.header-title {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 88rpx; // 固定头部高度
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		font-size: 36rpx; // 字体大小
		font-weight: bold;
		color: #303133;
		position: sticky; // 吸顶效果
		top: 0; // 粘滞到顶部
		z-index: 10; // 确保在其他内容之上
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05); // 阴影效果
	}


	// Page container, 撑开剩余空间
	.page-container {
		display: flex;
		flex-direction: column;
		flex-grow: 1; // 占据剩余空间
		min-height: calc(100vh - 88rpx); // 减去头部高度
		background-color: #f4f6f8; // 轻灰色背景
	}

	// General styles for loading/error states
	.state-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 100rpx 0; // 添加内边距
		color: #909399;
		font-size: 28rpx;
		text-align: center;
		flex-grow: 1; // 撑开剩余空间
	}

	.state-text {
		margin-top: 20rpx; // 文字上方空间
		color: #909399; // 默认状态文字颜色
	}

	.error-state .state-text {
		color: #f56c6c; // 错误文字颜色
		margin: 20rpx 30rpx; // 添加水平边距，避免文字过长挤压
	}

	.error-state button {
		margin-top: 30rpx;
	}

	// 表单滚动区域
	.form-scroll-view {
		flex: 1; // 允许滚动区域撑满空间
		padding: 20rpx 24rpx; // 页面水平内边距
		box-sizing: border-box; // 盒模型
		/* 底部留白，防止被固定按钮覆盖 */
		padding-bottom: 160rpx; // 留出固定底部按钮区域的高度
	}

	// 卡片样式
	.card {
		background-color: #ffffff;
		border-radius: 16rpx; // 圆角
		margin-bottom: 25rpx; // 卡片间距
		padding: 10rpx 30rpx 20rpx 30rpx; // 内部内边距
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); // 阴影效果

		&:last-of-type {
			// 最后一个卡片的底部边距由 footer-spacer 控制，此处可以不设或设小
			// margin-bottom: 0; // 底部边距由 footer-spacer 提供
		}
	}

	// 卡片标题样式
	.card-title {
		display: block; // 独占一行
		font-size: 30rpx;
		font-weight: bold;
		color: #303133;
		padding: 20rpx 0 15rpx 0; // 标题上下内边距
		margin-bottom: 10rpx; // 标题下方边距
		border-bottom: 1rpx solid #eee; // 分割线
	}

	/* Uni Forms Item 样式覆盖 */
	// 使用 ::v-deep 深度选择器覆盖 uni-forms 内部组件样式
	::v-deep .uni-forms-item {
		padding: 18rpx 0; // 表单项垂直内边距
		border-bottom: 1rpx solid #f0f0f0; // 表单项间分割线
		margin-bottom: 0 !important; // 重置 uni-forms-item 默认边距

		&:last-child {
			border-bottom: none; // 最后一项无底部边框
		}

		.uni-forms-item__label {
			font-size: 28rpx !important;
			color: #606266 !important; // 标签颜色
			margin-bottom: 8rpx !important; // 标签与输入框间距
			font-weight: normal !important; // 重置可能的加粗
			padding: 0 !important; // 重置内边距
			line-height: 1.5; // 行高
		}

		.uni-forms-item__content {
			font-size: 28rpx !important; // 内容字体大小
			color: #303133; // 内容颜色
		}

		// 错误信息样式
		.uni-forms-item__error {
			font-size: 24rpx !important; // 错误文字大小
			padding-top: 8rpx !important; // 错误文字上方空间
		}
	}

	/* 特定输入/选择组件样式覆盖 */

	// Easyinput 样式
	::v-deep .uni-easyinput__content-input,
	::v-deep .uni-textarea-textarea {
		font-size: 28rpx !important;
		padding-left: 0 !important; // 移除默认左内边距
		height: auto !important; // 允许 textarea 自适应高度
		line-height: 1.6 !important; // 提高可读性
		color: #303131 !important; // 更深的文字颜色
		min-height: 40rpx; // 最小高度
		box-sizing: border-box; // 包含 padding 在高度内
	}

	::v-deep .uni-easyinput__placeholder,
	::v-deep .uni-textarea-placeholder {
		color: #c0c4cc !important; // 占位符颜色
		font-size: 28rpx !important;
	}

	// Data Select 样式
	::v-deep .uni-stat__select {
		padding: 0 !important; // 移除默认内边距
		line-height: normal; // 重置行高
		height: auto !important; // 自适应高度
		border: none !important; // 移除默认边框
	}

	::v-deep .uni-select__selector-item {
		font-size: 28rpx !important; // 选择项字体大小
	}

	::v-deep .uni-select__input-text {
		font-size: 28rpx !important; // 已选文字字体大小
		color: #303131 !important; // 更深的文字颜色
	}

	::v-deep .uni-select__placeholder {
		font-size: 28rpx !important; // 占位符字体大小
		color: #c0c4cc !important; // 占位符颜色
	}

	// Date Picker 样式
	::v-deep .uni-date-editor--x .uni-date-single {
		padding: 0 !important; // 移除默认内边距
		font-size: 28rpx !important; // 日期文字字体大小
		color: #303131 !important; // 更深的文字颜色
		height: 40rpx !important; // 调整高度
		line-height: 40rpx !important; // 垂直居中文本
		display: flex;
		align-items: center;
	}

	::v-deep .uni-date-x--border {
		border: none !important; // 移除默认边框
	}

	::v-deep .uni-date-editor--x .uni-icons {
		color: #909399 !important; // 图标颜色
		order: 1; // 将图标移到右侧
		margin-left: 10rpx; // 文字与图标间距
	}

	::v-deep .uni-date-single__text {
		flex-grow: 1; // 文本占据剩余空间
	}


	// 原生 Picker (部门/人员) 样式
	.picker {
		height: auto; // 自适应内容高度
		line-height: normal;
		display: flex;
		align-items: center;
		width: 100%; // 满宽
		font-size: 28rpx;
		color: #303131; // 更深的文字颜色
		min-height: 40rpx; // 最小高度
	}

	.picker-value {
		font-size: 28rpx;
		color: #303131; // 更深的文字颜色
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: 40rpx; // 最小高度

		&.placeholder {
			color: #c0c4cc; // 占位符颜色
		}

		.no-arrow {
			margin-left: auto;
			/* 将文字推向左侧，视觉上移除箭头占位空间 */
			color: #909399;
			/* 提示不可选状态 */
			font-size: 24rpx;
			/* 略小文字 */
		}
	}

	.picker-arrow {
		flex-shrink: 0; // 防止箭头收缩
		margin-left: 10rpx;
	}


	// 表单项中的只读文本值样式
	.form-value-text {
		display: block; // 独占一行
		font-size: 28rpx;
		color: #303131; // 更深的文字颜色
		padding: 10rpx 0; // 与输入框垂直内边距对齐
		word-break: break-word; // 防止长文本溢出
	}

	// --- 参与人员部分样式 ---

	// 触发打开弹窗的可点击区域
	.select-participants-trigger {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 10rpx 0; // 与输入框垂直内边距对齐
		box-sizing: border-box;
		min-height: 40rpx;
		cursor: pointer; // 指示可点击

		text {
			flex-grow: 1; // 文本占据大部分空间
			font-size: 28rpx;
			color: #303131; // 默认文本颜色
		}

		.selected-count-text.placeholder {
			color: #c0c4cc; // 占位符颜色
		}

		uni-icons {
			flex-shrink: 0; // 防止图标收缩
		}
	}


	// 已选参与人列表 (标签列表) 样式
	.participant-list {
		display: flex;
		flex-wrap: wrap; // 允许标签换行
		gap: 16rpx 16rpx; // 标签间距 (行间距, 列间距)
		padding: 10rpx 0; // 垂直内边距
		min-height: 40rpx; // 即使列表为空也保持最小高度
		align-items: center; // 垂直居中 (单行时)
	}

	.participant-tag {
		display: inline-flex; // 使用 flex 对齐文本和图标
		align-items: center;
		background-color: #e9e9eb; // 标签背景色
		color: #303131; // 标签文字颜色
		font-size: 26rpx; // 标签文字大小
		padding: 8rpx 16rpx; // 标签内部内边距
		border-radius: 12rpx; // 标签圆角
		white-space: nowrap; // 防止标签内文字换行
		overflow: hidden; // 隐藏溢出文本
		text-overflow: ellipsis; // 溢出显示省略号
		max-width: 100%; // 确保标签不超过容器宽度

		text {
			margin-right: 8rpx; // 文本与关闭图标间距
			flex-shrink: 0; // 防止文本过度收缩
		}

		.uni-icons {
			flex-shrink: 0; // 防止图标收缩
			line-height: 1; // 图标行高
			cursor: pointer; // 指示可点击图标
		}
	}

	.participant-list .placeholder {
		color: #c0c4cc; // 空列表占位符颜色
		font-size: 28rpx;
	}


	// --- 弹窗/Modal 样式 ---
	// 深度选择器用于 uni-popup 内部样式
	::v-deep .uni-popup__wrapper {
		.popup-content {
			background-color: #fff;
			border-radius: 16rpx;
			width: 650rpx; // 弹窗宽度
			display: flex;
			flex-direction: column;
			box-sizing: border-box; // 盒模型
			max-height: 80vh; // 限制弹窗最大高度
		}

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx; // 内边距
			border-bottom: 1rpx solid #eee; // 分割线
			flex-shrink: 0; // 防止头部收缩
		}

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #303131;
		}

		.popup-filter-section {
			display: flex;
			align-items: center;
			padding: 20rpx 30rpx 0; // 内边距
			flex-shrink: 0; // 防止过滤区域收缩

			.filter-label {
				font-size: 28rpx;
				color: #606266;
				margin-right: 20rpx; // 标签与选择器间距
				flex-shrink: 0; // 防止标签收缩
			}

			.filter-picker {
				flex-grow: 1; // 占据剩余空间
				min-height: 40rpx;

				.picker-value {
					font-size: 28rpx;
					color: #303131;
					justify-content: flex-start; // 左对齐
				}

				.picker-arrow {
					margin-left: auto; // 将箭头推到右侧
				}
			}
		}

		.popup-search {
			padding: 20rpx 30rpx 0; // 内边距
			flex-shrink: 0; // 防止搜索框收缩

			::v-deep .uni-easyinput__content {
				border-radius: 8rpx !important; // 圆角
				padding: 0 10rpx !important; // 内边距
			}

			::v-deep .uni-easyinput__content-input {
				font-size: 28rpx !important; // 字体大小
			}

			::v-deep .uni-easyinput__placeholder {
				font-size: 28rpx !important; // 占位符字体大小
			}
		}


		.popup-employee-list {
			max-height: calc(80vh - 320rpx); // 计算列表最大高度 (总高度 - 头部 - 过滤 - 搜索 - 底部)
			padding: 0 30rpx; // 水平内边距
			overflow-y: auto; // 允许垂直滚动
			flex-grow: 1; // 撑开空间
		}

		.popup-loading-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 40rpx 0;
			font-size: 28rpx;
			color: #909399;
		}

		/* 员工列表项，现在通过高亮而非复选框表示选中状态 */
		.employee-item {
			width: 78vw;
			display: flex;
			align-items: center;
			padding: 20rpx 0; // 垂直内边距
			border-bottom: 1rpx solid #f0f0f0; // 分割线
			transition: background-color 0.2s ease; // 平滑过渡效果
			cursor: pointer; // 提示可点击

			&:last-child {
				border-bottom: none; // 最后一项无底部边框
			}

			&.selected {
				background-color: #e6f7ff; // 选中时的浅蓝色背景
				border-left: 6rpx solid #007aff; // 左侧蓝色边框作为强调
				padding-left: 24rpx; // 因新增边框，需调整左侧内边距
			}
		}

		.employee-name {
			font-size: 28rpx;
			color: #303131;
			flex-grow: 1;
		}

		.employee-department {
			font-size: 24rpx;
			color: #909399;
			margin-left: 10rpx;
			flex-shrink: 0; // 防止部门名称收缩
		}

		.selected-checkmark {
			margin-left: 20rpx; // 对勾图标与文本的间距
			flex-shrink: 0; // 防止图标收缩
		}

		.popup-empty-state {
			padding: 40rpx 0;
			text-align: center;
			font-size: 28rpx;
			color: #909399;
		}

		.popup-footer {
			display: flex;
			justify-content: flex-end; // 按钮右对齐
			padding: 20rpx 30rpx 30rpx; // 底部内边距
			gap: 20rpx; // 按钮间距
			flex-shrink: 0; // 防止底部收缩
		}

		.popup-footer button {
			font-size: 28rpx;
			height: 70rpx; // 按钮高度
			line-height: 70rpx; // 垂直居中文本
			padding: 0 30rpx; // 水平内边距
		}
	}


	// 底部留白，用于撑开滚动区域，防止最后一个卡片被固定底部按钮覆盖
	.footer-spacer {
		height: 140rpx; // 调整高度以匹配或略高于固定底部按钮区域的高度
	}


	// 固定底部操作按钮区域
	.action-button-container {
		position: fixed; // 固定到底部
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 20rpx 30rpx; // 按钮区域内边距
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom)); // iOS 安全区域
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); // 标准安全区域
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1); // 区域上方阴影
		z-index: 10; // 确保在滚动内容之上
	}

	// 保存按钮样式
	.submit-button {
		background-color: #007aff; // uni-app 主题蓝
		color: #ffffff;
		font-size: 32rpx; // 按钮文字大小
		border-radius: 8rpx; // 圆角
		height: 90rpx; // 按钮高度
		line-height: 90rpx; // 垂直居中文本
		width: 100%; // 满宽

		// 禁用状态样式
		&[disabled] {
			background-color: #a0cfff; // 禁用时的浅蓝
			color: #ffffff;
			opacity: 0.8; // 透明度
		}
	}
</style>