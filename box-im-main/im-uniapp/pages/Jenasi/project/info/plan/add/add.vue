<template>
	<!-- {{test}} -->
	<!-- {{formData}} -->
	<view
		style="display: flex; justify-content: center; align-items: flex-end; height: 7vh; padding-bottom: 10rpx;">
		新增计划
	</view>
	<view class="container">
		<form @submit.prevent="submitForm">

			<!-- 项目关联信息 (假设通过页面参数传递) -->
			<view class="form-item project-info" v-if="formData.projectId">
				<uni-icons type="folder-filled" size="20" color="#5e8acb"></uni-icons>
				<text class="label">所属项目</text>
				<text class="info-text">{{ formData.projectName || '未知项目' }}</text>
				<!-- 这里不展示输入框，projectId 和 projectName 假设由父页面传递 -->
			</view>
			<view class="form-item" v-else>
				<uni-icons type="folder" size="20" color="#f3a73f"></uni-icons>
				<text class="label">所属项目ID</text>
				<!-- 如果项目ID不是通过参数传递，而是需要选择，这里需要一个项目选择器 -->
				<input class="input" v-model="formData.projectId" placeholder="请手动输入项目ID (临时)" type="number" />
			</view>


			<!-- 计划的名称 -->
			<view class="form-item">
				<uni-icons type="compose" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">计划名称</text>
				<input class="input" v-model="formData.taskName" placeholder="请输入计划名称" name="taskName" />
			</view>

			<!-- 计划描述 (对应 taskDesc 在 DTO 中) -->
			<view class="form-item form-item-textarea">
				<uni-icons type="chat" size="20" color="#5e8acb"></uni-icons>
				<text class="label">计划描述</text>
				<textarea class="textarea" v-model="formData.taskDesc" placeholder="请输入计划描述" name="taskDesc"
					auto-height />
			</view>

			<!-- 优先级 (文字说明) - DTO中没有，这里作为一个文本字段 -->
			<view class="form-item">
				<uni-icons type="star" size="20" color="#ff9800"></uni-icons>
				<text class="label">优先级</text>
				<input class="input" v-model="formData.priority" placeholder="请输入优先级说明 (例如：高，中)" name="priority" />
			</view>


			<!-- 计划开始时间 -->
			<view class="form-item">
				<uni-icons type="calendar" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">计划开始时间</text>
				<uni-datetime-picker class="flex-picker" type="date" :value="formData.plannedStartDate"
					@change="handleDateChange($event, 'plannedStartDate')" />
			</view>

			<!-- 计划结束时间 -->
			<view class="form-item">
				<uni-icons type="calendar-filled" size="20" color="#5e8acb"></uni-icons>
				<text class="label">计划结束时间</text>
				<uni-datetime-picker class="flex-picker" type="date" :value="formData.plannedEndDate"
					:start="formData.plannedStartDate || ''" @change="handleDateChange($event, 'plannedEndDate')" />
			</view>

			<!-- 预估价值 -->
			<view class="form-item">
				<uni-icons type="wallet" size="20" color="#5e8acb"></uni-icons>
				<text class="label">预估价值</text>
				<input class="input" type="digit" v-model="formData.estimatedValue" placeholder="请输入预估价值"
					name="estimatedValue" />
			</view>

			<!-- 预计成本 -->
			<view class="form-item">
				<uni-icons type="wallet-filled" size="20" color="#5e8acb"></uni-icons>
				<text class="label">预计成本</text>
				<input class="input" type="digit" v-model="formData.estimatedCost" placeholder="请输入预计成本"
					name="estimatedCost" />
			</view>

			<!-- 实际成本 -->
			<view class="form-item">
				<uni-icons type="wallet" size="20" color="#e64340"></uni-icons>
				<text class="label">实际成本</text>
				<input class="input" type="digit" v-model="formData.actualCost" placeholder="请输入实际成本 (可选)"
					name="actualCost" />
			</view>

			<!-- 进度 -->
			<view class="form-item">
				<uni-icons type="flag" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">进度 (%)</text>
				<input class="input" v-model="formData.progress" placeholder="请输入当前进度 (%) 例如: 0, 50, 100"
					name="progress" type="number" />
			</view>

			<!-- 状态 -->
			<view class="form-item">
				<uni-icons type="notification" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">状态</text>
				<picker class="flex-picker" mode="selector" :range="statusOptions" @change="handleStatusChange"
					:value="statusIndex">
					<view class="picker-view">
						<text :class="{'placeholder-text': statusIndex == -1}">
							{{ statusIndex != -1 ? statusOptions[statusIndex] : '请选择状态' }}
						</text>
						<uni-icons type="bottom" size="16" color="#999"></uni-icons>
					</view>
				</picker>
			</view>


			<view class="divider">
				<uni-icons type="personadd" size="16" color="#666" style="margin-right: 8rpx;"></uni-icons>
				负责人选择
			</view>

			<!-- 部门选择 -->
			<view class="form-item">
				<uni-icons type="flag" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">选择部门</text>
				<picker class="flex-picker" mode="selector" :range="departmentsList" range-key="departmentName"
					@change="handleDepartmentChange" :value="departmentIndex">
					<view class="picker-view">
						<text :class="{'placeholder-text': departmentIndex == -1}">
							{{ departmentIndex != -1 ? departmentsList[departmentIndex]?.departmentName : '请选择部门' }}
						</text>
						<uni-icons type="bottom" size="16" color="#999"></uni-icons>
					</view>
				</picker>
			</view>

			<!-- 员工 (负责人) 选择 -->
			<view class="form-item">
				<uni-icons type="person" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">选择负责人</text>
				<picker class="flex-picker" mode="selector" :range="employeesList" range-key="name"
					@change="handleEmployeeChange" :value="employeeIndex"
					:disabled="departmentIndex == -1 || employeesLoading">
					<view class="picker-view" :class="{disabled: departmentIndex == -1 || employeesLoading}">
						<text v-if="employeesLoading" class="placeholder-text">加载中...</text>
						<text v-else :class="{'placeholder-text': employeeIndex == -1}">
							{{ employeeIndex != -1 ? employeesList[employeeIndex]?.name : (departmentIndex == -1 ? '请先选择部门' : '请选择负责人') }}
						</text>
						<uni-icons v-if="!employeesLoading" type="bottom" size="16" color="#999"></uni-icons>
					</view>
				</picker>
			</view>

			<!-- 显示已选负责人信息 -->
			<!-- 这里修改为显示 formData.wechatName -->
			<view v-if="formData.ownerId" class="selected-manager-info">
				<uni-icons type="person-filled" size="16" color="#18b566" style="margin-right: 8rpx;"></uni-icons>
				<text>已选负责人: {{ formData.ownerName }}</text>
				<text v-if="formData.wechatName">，微信：{{ formData.wechatName }}</text>
			</view>

			<view class="button-container">
				<button class="submit-button" type="primary" @click="submitForm" :loading="isSubmitting"
					:disabled="isSubmitting || departmentsLoading || employeesLoading">
					<uni-icons v-if="!isSubmitting" type="checkmark" size="20" color="#fff"
						style="margin-right: 8rpx;"></uni-icons>
					提 交
				</button>
			</view>
		</form>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app'; // 导入 onLoad 生命周期钩子
	
		import UNI_APP from '@/.env.js'
		
		
		const API_BASE_URL = UNI_APP.PROJECT; // 替换为你的后端接口地址


	const test = ref("test")

	// --- 响应式状态 ---
	const formData = reactive({
		// 假设 projectId 和 projectName 会通过页面参数传递进来
		taskId: null, // 新增不需要填写ID，由后端生成
		projectId: null, // 关联的项目ID，重要！
		projectName: '', // 关联的项目名称，用于展示
		// parentTaskId: null, // 如果是子任务需要，这里暂不涉及
		taskName: '', // 计划的名称
		taskDesc: '', // 计划描述 (对应 DTO taskDesc)
		priority: '', // 优先级 (文字说明，DTO 中没有此字段，作为额外输入)
		plannedStartDate: null, // 计划开始日期
		plannedEndDate: null, // 计划结束日期
		// actualStartDate/EndDate: null, // 实际日期，新增时不填写，后续更新
		estimatedValue: '', // 预估价值
		estimatedCost: '', // 预计成本
		actualCost: '', // 实际成本 (用户要求填写，但通常是后续更新)
		progress: '', // 进度 (%)
		status: '', // 状态 (字符串，将通过 picker 选择填充)
		// createdAt/updatedAt: '', // 创建/更新时间，后端生成
		// dailyReviewSummary: '[]', // 日报摘要，新增时不填写
		ownerId: null, // 负责人ID (对应 DTO ownerId)
		ownerName: '', // 负责人名称 (对应 DTO ownerName)
		wechatName: '' // <---- 新增字段，用于前端显示选中的负责人微信
		// DTO 中没有 ownerWechat，所以此字段不会提交到后端
	});

	// --- 项目信息获取 (假设从页面参数获取) ---
	onLoad((options) => {
		console.log('页面接收参数:', options);
		if (options.projectId) {
			formData.projectId = parseInt(options.projectId, 10); // 确保是数字
		}
		if (options.projectName) {
			formData.projectName = options.projectName;
		}

		// 如果没有 projectId 参数，可能需要用户手动输入或从另一个选择器选择
		if (!formData.projectId) {
			console.warn("未从参数接收到 projectId，请注意处理");
			// 可以添加逻辑引导用户输入或选择项目
			uni.showToast({
				title: '请指定所属项目',
				icon: 'none',
				duration: 2000
			});
		}

		fetchDepartments(); // 页面加载时获取部门列表
	});


	// --- 部门选择状态 ---
	const departmentsList = ref([]); // 部门列表 (示例: {id: 1, departmentName: '研发部', ...})
	const departmentIndex = ref(-1); // 当前选中部门在 departmentsList 中的索引
	const departmentsLoading = ref(false); // 部门列表加载状态

	// --- 员工选择状态 (负责人) ---
	// 假设 employeesList 中的每个员工对象都包含 id, name, wechatName 等字段
	const employeesList = ref([]); // 员工列表 (示例: {id: 101, name: '张三', wechatName: 'zhangsan_wx', departmentId: 1, ...})
	const employeeIndex = ref(-1); // 当前选中员工在 employeesList 中的索引
	const employeesLoading = ref(false); // 员工列表加载状态

	// --- 状态选择状态 ---
	// 更新此数组以包含新的状态列表
	const statusOptions = ref([
        '草稿',
        '待处理',
        '进行中',
        '暂停/挂起',
        '已完成',
        '失败',
        '已取消',
        '已拒绝',
        '已归档'
    ]);
	const statusIndex = ref(-1); // 当前选中状态在 statusOptions 中的索引

	// --- 提交状态 ---
	const isSubmitting = ref(false); // 表单是否正在提交中

	// --- 模拟/真实数据获取函数 ---
	

	const fetchDepartments = async () => {
		departmentsLoading.value = true;
		console.log("开始获取部门列表...");
		try {
			const res = await uni.request({
				url: `${API_BASE_URL}/departments/selectAll`
			});
			
			if(res.data.code != 200){
				uni.showToast({
					title: result.message,
					icon: 'none'
				});
			}

			// test.value = res

			departmentsList.value = res.data.data;
			console.log("部门列表获取成功:", departmentsList.value);

		} catch (error) {
			console.error("获取部门列表异常:", error);
			uni.showToast({
				title: '获取部门列表异常',
				icon: 'none'
			});
		} finally {
			departmentsLoading.value = false; // 结束加载状态
		}
	};

	const fetchEmployees = async (departmentId) => {
		if (!departmentId) return; // 如果没有部门ID，则不执行
		employeesLoading.value = true;
		employeesList.value = []; // 清空之前的员工列表
		employeeIndex.value = -1; // 重置员工选择索引
		// 清空表单中的负责人信息，包括 wechatName
		formData.ownerId = null;
		formData.ownerName = '';
		formData.wechatName = ''; // <---- 重置 wechatName
		console.log(`开始获取部门 ID 为 ${departmentId} 的员工列表...`);

		try {
			const res = await uni.request({
				url: `${API_BASE_URL}/employees/selectAllByDepartmentId/${departmentId}`,
				method: 'GET'
			});

			// 假设后端返回的员工对象包含 wechatName 字段
			employeesList.value = res.data.data;
			console.log(`部门 ID ${departmentId} 的员工列表:`, employeesList.value);
			if (employeesList.value.length == 0) {
				uni.showToast({
					title: '该部门暂无员工可选',
					icon: 'none',
					duration: 1500
				});
			}

		} catch (error) {
			console.error("获取员工列表异常:", error);
			uni.showToast({
				title: '获取员工列表异常',
				icon: 'none'
			});
		} finally {
			employeesLoading.value = false; // 结束加载状态
		}
	};

	// --- 事件处理函数 ---
	const handleDateChange = (selectedDate, field) => {
		// uni-datetime-picker 直接返回选中的日期字符串 (YYYY-MM-DD)
		formData[field] = selectedDate; // 直接更新 reactive 对象
		console.log(`${field} 日期更改为:`, selectedDate);
		// 可选: 校验计划结束日期不能早于计划开始日期
		if (field == 'plannedStartDate' && formData.plannedEndDate && formData.plannedStartDate > formData
			.plannedEndDate) {
			formData.plannedEndDate = null; // 如果开始日期晚于结束日期，重置结束日期
			uni.showToast({
				title: '计划结束日期已重置',
				icon: 'none',
				duration: 1500
			});
		}
	};

	const handleDepartmentChange = (e) => {
		const index = parseInt(e.detail.value, 10); // picker 返回的是字符串索引，转为数字
		// 检查索引是否有效
		if (index >= 0 && index < departmentsList.value.length) {
			departmentIndex.value = index; // 更新选中部门的索引
			const selectedDepartment = departmentsList.value[departmentIndex.value];
			console.log("选择了部门:", selectedDepartment);
			fetchEmployees(selectedDepartment.id); // 根据选中的部门ID获取员工列表
		} else {
			// 处理无效选择或取消选择的情况
			departmentIndex.value = -1;
			employeesList.value = [];
			employeeIndex.value = -1;
			formData.ownerId = null;
			formData.ownerName = '';
			formData.wechatName = ''; // <---- 重置 wechatName
			console.log("部门选择已取消或无效");
		}
	};

	const handleEmployeeChange = (e) => {
		const index = parseInt(e.detail.value, 10); // picker 返回的是字符串索引，转为数字
		// 检查索引是否有效
		if (index >= 0 && index < employeesList.value.length) {
			employeeIndex.value = index; // 更新选中员工的索引
			const selectedEmployee = employeesList.value[employeeIndex.value];
			console.log("选择了负责人:", selectedEmployee);
			// 更新表单数据中的负责人信息 (对应 DTO 的 ownerId, ownerName)
			formData.ownerId = selectedEmployee.id;
			formData.ownerName = selectedEmployee.name;
			// 确保员工对象有 wechatName 字段
			formData.wechatName = selectedEmployee.wechatName || ''; // <---- 将选中的 wechatName 存入 formData
		} else {
			// 处理无效选择或取消选择的情况
			employeeIndex.value = -1;
			formData.ownerId = null;
			formData.ownerName = '';
			formData.wechatName = ''; // <---- 重置 wechatName
			console.log("负责人选择已取消或无效");
		}
	};

	const handleStatusChange = (e) => {
		const index = parseInt(e.detail.value, 10);
		if (index >= 0 && index < statusOptions.value.length) {
			statusIndex.value = index;
			formData.status = statusOptions.value[statusIndex.value];
			console.log("选择了状态:", formData.status);
		} else {
			statusIndex.value = -1;
			formData.status = '';
			console.log("状态选择已取消或无效");
		}
	};


	// --- 表单提交 ---
	const submitForm = async () => {
		// 1. 表单校验
		if (!formData.projectId) {
			uni.showToast({
				title: '请指定所属项目ID',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (!formData.taskName.trim()) {
			uni.showToast({
				title: '请输入计划名称',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (!formData.plannedStartDate) {
			uni.showToast({
				title: '请选择计划开始时间',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (formData.plannedStartDate && formData.plannedEndDate && formData.plannedStartDate > formData
			.plannedEndDate) {
			uni.showToast({
				title: '计划结束日期不能早于计划开始日期',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (!String(formData.progress).trim()) { // 检查是否为空或只包含空格
			uni.showToast({
				title: '请输入进度',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		const progressValue = Number(formData.progress);
		if (isNaN(progressValue) || progressValue < 0 || progressValue > 100) {
			uni.showToast({
				title: '进度请输入0-100的数字',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (!formData.status.trim()) {
			uni.showToast({
				title: '请选择状态',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (!formData.ownerId) {
			uni.showToast({
				title: '请选择负责人',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		// 可在此处添加更多校验规则...

		isSubmitting.value = true; // 开始提交，设置按钮为加载状态

		// 2. 准备提交数据 (匹配 DTO 结构)
		// 过滤掉 DTO 中没有的字段 (例如 priority, wechatName) 或由后端生成的字段 (taskId, createdAt, updatedAt等)
		const payload = {
			projectId: formData.projectId,
			projectName: formData.projectName, // projectName 通常不需要在创建任务时发送
			// parentTaskId: formData.parentTaskId, // 如果需要支持子任务，这里包含
			taskName: formData.taskName,
			taskDesc: formData.taskDesc,
			plannedStartDate: formData.plannedStartDate,
			plannedEndDate: formData.plannedEndDate,
			estimatedValue: formData.estimatedValue ? Number(formData.estimatedValue) : null, // 转换为数字或 null
			estimatedCost: formData.estimatedCost ? Number(formData.estimatedCost) : null, // 转换为数字或 null
			actualCost: formData.actualCost ? Number(formData.actualCost) : null, // 转换为数字或 null
			progress: String(progressValue), // DTO 中进度是字符串，确保发送字符串
			status: formData.status,
			ownerId: formData.ownerId,
			ownerName: formData.ownerName,
			priority: formData.priority,
			ownerWechat: formData.wechatName
		};
		console.log("准备提交的任务数据:", JSON.parse(JSON.stringify(payload))); // 打印查看最终数据结构

		// 3. 调用 API 接口
		try {
			const res = await uni.request({
				url: `${API_BASE_URL}/task/add`, // 你的后端接口地址
				method: 'POST',
				data: payload,
			});

			if (res.statusCode == 200 && res.data.code == 200) {
				console.log("任务创建成功:", res.data);
				uni.showToast({
					title: '任务创建成功',
					icon: 'success',
					duration: 1500
				});
				// 可选：成功后返回上一页或跳转到任务列表页
				// setTimeout(() => {
				//     uni.navigateBack();
				// }, 1500);

				uni.navigateBack(); // 返回上一页


			} else {
				console.error("任务创建失败:", res.data.message || res.statusCode);
				uni.showToast({
					title: res.data.message || '任务创建失败，请重试',
					icon: 'none',
					duration: 2000
				});
			}

		} catch (error) {
			console.error("任务创建异常:", error);
			uni.showToast({
				title: error.message || '任务创建异常，请重试',
				icon: 'none',
				duration: 2000
			});
		} finally {
			isSubmitting.value = false; // 无论成功或失败，结束提交状态
		}
	};


</script>

<style scoped>
	.container {
		padding: 20rpx 30rpx 40rpx;
		background-color: #f9f9f9;
		min-height: 100vh;
		box-sizing: border-box;
	}

	.form-item {
		background-color: #ffffff;
		padding: 25rpx 30rpx;
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		border: 1rpx solid #eee;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* 项目信息展示项 */
	.form-item.project-info .info-text {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		margin-left: 20rpx;
		/* 增加与 label 的距离 */
		word-break: break-all;
		/* 防止长文本溢出 */
	}

	.form-item.project-info .label {
		width: auto;
		/* 项目信息标签不需要固定宽度 */
	}


	/* 特殊处理包含 textarea 的 form-item */
	.form-item-textarea {
		align-items: flex-start;
		/* 图标和标签顶部对齐 */
	}

	.form-item-textarea .uni-icons {
		margin-top: 6rpx;
		/* 微调图标位置，使其与标签顶部大致对齐 */
	}

	.form-item-textarea .label {
		margin-top: 6rpx;
		/* 微调标签位置 */
	}

	/* 图标样式 */
	.uni-icons {
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.label {
		width: 180rpx;
		/* 调整标签宽度以适应更多文字 */
		font-size: 28rpx;
		color: #333;
		flex-shrink: 0;
		line-height: 1.5;
	}

	.label.required::before {
		content: '*';
		color: #fa3534;
		margin-right: 6rpx;
		font-weight: bold;
	}

	.input,
	.textarea {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		padding: 0;
		line-height: 1.5;
		background-color: transparent;
	}

	.textarea {
		min-height: 120rpx;
		padding: 10rpx 0;
		width: 100%;
		box-sizing: border-box;
	}

	/* 选择器 (Picker, DateTimePicker) 占据剩余空间 */
	.flex-picker {
		flex: 1;
	}

	/* Picker 内部视图样式 */
	.picker-view {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}

	.picker-view.disabled {
		color: #c0c4cc;
	}

	.placeholder-text {
		color: #999;
	}

	/* 调整 uni-datetime-picker 内部样式对齐 */
	/* 注意：深度选择器 (::v-deep 或 >>>) 可能因 UniApp 版本或平台有差异 */
	:deep(.uni-date-editor) {
		flex: 1;
		height: auto !important;
	}

	:deep(.uni-date-single) {
		padding: 0 !important;
		font-size: 28rpx !important;
		color: #333 !important;
		line-height: 1.5 !important;
		height: auto !important;
	}

	:deep(.uni-date__input) {
		height: auto !important;
		line-height: 1.5 !important;
		padding: 0 !important;
		font-size: 28rpx !important;
		color: #333 !important;
	}

	:deep(.uni-date-x--border) {
		border: none !important;
	}

	/* 隐藏日期选择器自带的日历图标 */
	:deep(.uni-date__icon-clear) {
		display: none !important;
	}


	.divider {
		font-size: 26rpx;
		color: #666;
		padding: 30rpx 0 15rpx 0;
		margin-top: 10rpx;
		display: flex;
		align-items: center;
	}

	.selected-manager-info {
		font-size: 26rpx;
		color: #18b566;
		margin-top: -10rpx;
		margin-bottom: 20rpx;
		padding: 15rpx 30rpx;
		background-color: #e8f8f0;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
	}


	.button-container {
		margin-top: 50rpx;
		padding: 0 30rpx;
	}

	.submit-button {
		background-color: #409eff;
		color: white;
		border-radius: 50rpx;
		font-size: 32rpx;
		height: 90rpx;
		line-height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 10rpx rgba(64, 158, 255, 0.4);
	}

	/* 按钮禁用和加载状态 */
	.submit-button[disabled] {
		background-color: #a0cfff !important;
		color: #ffffff !important;
		opacity: 0.8;
		box-shadow: none;
	}

	.submit-button[loading] {
		background-color: #66b1ff !important;
	}
</style>