<template>
	<view class="page-container">
		<!-- Fixed Header Title -->
		<view class="form-header">
			<text class="form-title">修改项目信息</text>
		</view>

		<!-- Loading State -->
		<view v-if="loading" class="loading-state">
			<uni-load-more status="loading" :showText="false"></uni-load-more>
			<text>加载项目数据中...</text>
		</view>

		<!-- Error State -->
		<view v-else-if="initialFetchError" class="error-state">
			<uni-icons type="close-filled" size="40" color="#f56c6c"></uni-icons>
			<text class="error-text">加载失败: {{ initialFetchError }}</text>
			<button size="mini" @click="retryLoad">重试</button>
		</view>

		<!-- Form Content -->
		<scroll-view v-else scroll-y class="form-scroll-view">
			<view class="form-card">
				<text class="card-title">基本信息</text>
				<!-- Project Name -->
				<view class="form-item">
					<text class="form-label required">项目名称</text>
					<uni-easyinput class="form-input" trim="all" v-model="formData.projectName" placeholder="请输入项目名称" />
				</view>

				<!-- Project Description -->
				<view class="form-item">
					<text class="form-label">项目描述</text>
					<uni-easyinput type="textarea" autoHeight class="form-input" v-model="formData.projectDesc"
						placeholder="请输入项目描述" :maxlength="-1" />
				</view>

				<!-- Start Date -->
				<view class="form-item">
					<text class="form-label required">开始时间</text>

					<uni-datetime-picker class="form-input" type="date" v-model="formData.startDate"
						@change="handleDateChange('startDate', $event)" return-type="string" :clear-icon="false" />
				</view>

				<!-- End Date -->
				<view class="form-item">
					<text class="form-label">结束时间</text>
					<uni-datetime-picker class="form-input" type="date" v-model="formData.endDate"
						@change="handleDateChange('endDate', $event)" return-type="string" :clear-icon="false" />
				</view>
			</view>

			<view class="form-card">
				<text class="card-title">负责人</text>
				<!-- Manager Selection -->
				<view class="form-item">
					<text class="form-label required">所属部门</text>
					<picker class="form-input picker" mode="selector" :range="departmentRange"
						range-key="departmentName" :value="selectedDepartmentIndex" @change="handleDepartmentChange">
						<view :class="['picker-value', selectedDepartmentIndex == -1 ? 'placeholder' : '']">
							{{ selectedDepartmentIndex != -1 ? departments[selectedDepartmentIndex].departmentName : '请选择部门' }}
							<uni-icons type="bottom" size="14" color="#999" class="picker-arrow"></uni-icons>
						</view>
					</picker>
				</view>
				<view class="form-item" v-if="selectedDepartmentIndex != -1">
					<text class="form-label required">选择人员</text>
					<picker class="form-input picker" mode="selector" :range="employeeRange" range-key="name"
						:value="selectedEmployeeIndex" @change="handleEmployeeChange" :disabled="fetchingEmployees">
						<view
							:class="['picker-value', selectedEmployeeIndex == -1 && !fetchingEmployees && employees.length > 0 ? 'placeholder' : '', fetchingEmployees ? 'placeholder': '']">
							<block v-if="fetchingEmployees">人员加载中...</block>
							<block v-else-if="employees.length == 0">该部门暂无人员</block>
							<block v-else>
								{{ selectedEmployeeIndex != -1 ? employees[selectedEmployeeIndex].name : '请选择人员' }}
							</block>
							<uni-icons v-if="!fetchingEmployees && employees.length > 0" type="bottom" size="14"
								color="#999" class="picker-arrow"></uni-icons>
						</view>
					</picker>
				</view>
				<view class="form-item readonly-item" v-if="formData.managerName">
					<text class="form-label">管理员微信</text>
					<!-- Displaying based on fetched data, not editable here -->
					<text class="form-value">{{ formData.managerWechat || '未提供' }}</text>
				</view>
			</view>

			<view class="form-card">
				<text class="card-title">项目进度</text>
				<!-- Progress Status -->
				<view class="form-item">
					<text class="form-label required">进度状态</text>
					<picker class="form-input picker" mode="selector" :range="statusOptions"
						:value="selectedStatusIndex" @change="handleStatusChange">
						<view :class="['picker-value', selectedStatusIndex == -1 ? 'placeholder' : '']">
							{{ selectedStatusIndex != -1 ? statusOptions[selectedStatusIndex] : '请选择状态' }}
							<uni-icons type="bottom" size="14" color="#999" class="picker-arrow"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- Current Progress -->
				<view class="form-item">
					<text class="form-label">当前进度(%)</text>
					<uni-easyinput type="number" class="form-input" v-model="formData.currentProgress"
						placeholder="输入0-100的数字" @input="validateProgress('currentProgress')" />
				</view>

				<!-- Planned Progress -->
				<view class="form-item">
					<text class="form-label">计划进度(%)</text>
					<uni-easyinput type="number" class="form-input" v-model="formData.plannedProgress"
						placeholder="输入0-100的数字" @input="validateProgress('plannedProgress')" />
				</view>

				<!-- Progress Description -->
				<view class="form-item">
					<text class="form-label">进度说明</text>
					<uni-easyinput type="textarea" autoHeight class="form-input" v-model="formData.progressDesc"
						placeholder="请输入进度说明" :maxlength="-1" />
				</view>
			</view>

			<view class="form-card">
				<text class="card-title">财务信息</text>
				<!-- Total Value -->
				<view class="form-item">
					<text class="form-label">总价值</text>
					<uni-easyinput type="digit" class="form-input" v-model="formData.totalValue"
						placeholder="请输入项目总价值(数字)" />
				</view>

				<!-- Total Cost -->
				<view class="form-item">
					<text class="form-label">总成本</text>
					<uni-easyinput type="digit" class="form-input" v-model="formData.totalCost"
						placeholder="请输入项目总成本(数字)" />
				</view>
			</view>

			<!-- Action Button Area -->
			<view class="action-button-container">
				<button class="save-button" type="primary" @click="saveChanges" :loading="isSaving"
					:disabled="isSaving || loading">
					{{ isSaving ? '保存中...' : '保存修改' }}
				</button>
			</view>

		</scroll-view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		computed,
		nextTick
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app';


	import UNI_APP from '@/.env.js'

	// --- Constants ---
	const BASE_URL = UNI_APP.PROJECT; // Your API base URL
	const STATUS_OPTIONS = ['正常', '延期', '已完成', '未开始', '已暂停']; // Available progress statuses

	// --- Reactive State ---
	const projectId = ref(null);
	const loading = ref(true); // Initial page loading state
	const isSaving = ref(false); // Save operation in progress
	const initialFetchError = ref(null); // Store error during initial load

	// Form data using reactive for better object handling
	const formData = reactive({
		projectId: null,
		projectName: '',
		projectDesc: '',
		startDate: null, // Store as 'YYYY-MM-DD' string
		endDate: null, // Store as 'YYYY-MM-DD' string
		totalValue: '',
		totalCost: '',
		currentProgress: '0',
		plannedProgress: '0',
		progressStatus: '正常', // Default value, will be overwritten by fetch
		progressDesc: '',
		managerId: null,
		managerName: '',
		managerWechat: '',
		// Fields not directly edited but needed for update payload (like projectId) are included
	});

	// State for pickers and related data
	const departments = ref([]);
	const employees = ref([]);
	const fetchingEmployees = ref(false); // Loading state specifically for employees

	const selectedDepartmentIndex = ref(-1); // Index for department picker
	const selectedEmployeeIndex = ref(-1); // Index for employee picker
	const selectedStatusIndex = ref(-1); // Index for status picker, initialized to -1 until data loads

	// --- Computed Properties ---
	// Use computed properties for picker ranges for clarity
	const departmentRange = computed(() => departments.value);
	const employeeRange = computed(() => employees.value);
	const statusOptions = computed(() => STATUS_OPTIONS);

	// --- Utility Functions ---

	// Function to get current date as 'YYYY-MM-DD'
	const getCurrentDateFormatted = () => {
		const today = new Date();
		const year = today.getFullYear();
		const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
		const day = String(today.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	};

	// Format date from API (ISO string or similar) to 'YYYY-MM-DD' for picker
	const formatDateForPicker = (dateString) => {
		if (!dateString) return null;
		try {
			// Handle potential timezone offsets in ISO strings more reliably
			// Also handle simple 'YYYY-MM-DD' strings that might come directly
			let date;
			if (dateString.includes('T')) {
				date = new Date(dateString.replace('T', ' ').replace('+00:00',
				'Z')); // Treat as UTC if timezone is present
			} else {
				// Attempt to parse as local date 'YYYY-MM-DD' - be cautious about timezone interpretation if this happens
				const parts = dateString.split('-');
				if (parts.length == 3) {
					date = new Date(parts[0], parts[1] - 1, parts[2]); // Month is 0-indexed
				} else {
					throw new Error("Invalid date format without T");
				}
			}

			if (isNaN(date.getTime())) return null; // Invalid date object

			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		} catch (e) {
			console.error("Error formatting date for picker:", dateString, e);
			return null; // Return null if any error occurs during parsing/formatting
		}
	};


	// Centralized API Request Function
	const apiRequest = (options) => {
		return new Promise((resolve, reject) => {
			uni.showLoading({
				title: options.loadingTitle || '请稍候...',
				mask: true
			}); // Show loading indicator
			uni.request({
				url: BASE_URL + options.url,
				method: options.method || 'GET',
				data: options.data || {},
				timeout: options.timeout || 15000, // 15 seconds timeout
				success: (res) => {
					if (res.statusCode == 200 && res.data && res.data.code == "200") {
						resolve(res.data.data);
					} else {
						const errorMsg = res.data?.message || `请求失败 (${res.statusCode})`;
						console.error(`API Error (${options.url}):`, errorMsg, res);
						reject(errorMsg); // Reject with backend message or status code
					}
				},
				fail: (err) => {
					console.error(`Network Error (${options.url}):`, err);
					let errorMsg = '网络错误，请稍后重试';
					if (err.errMsg?.includes('timeout')) {
						errorMsg = '请求超时，请检查网络';
					}
					reject(errorMsg); // Reject with generic network error message
				},
				complete: () => {
					uni.hideLoading(); // Always hide loading
				}
			});
		});
	};

	// --- API Fetching Functions ---
	const fetchProjectDetails = async (id) => {
		try {
			const data = await apiRequest({
				url: `/project/selectById/${id}`,
				loadingTitle: '加载项目数据...'
			});


			if (data) {
				// Assign fetched data to reactive formData object
				Object.assign(formData, data); // Assign all matching fields

				// Specifically format dates and ensure progress are strings
				// *** MODIFICATION: Use || getCurrentDateFormatted() to default to today if API data is null/invalid ***
				formData.startDate = formatDateForPicker(data.startDate) || getCurrentDateFormatted();
				formData.endDate = formatDateForPicker(data.endDate) || getCurrentDateFormatted();

				formData.currentProgress = String(data.currentProgress ?? '0');
				formData.plannedProgress = String(data.plannedProgress ?? '0');
				// Ensure totalValue/Cost are strings for input binding if they might be numbers
				formData.totalValue = String(data.totalValue ?? '');
				formData.totalCost = String(data.totalCost ?? '');


				// Find and set the initial index for the status picker
				const statusIndex = STATUS_OPTIONS.findIndex(status => status == data.progressStatus);
				selectedStatusIndex.value = statusIndex != -1 ? statusIndex :
				0; // Default to '正常' (index 0) if not found or null

				console.log('Fetched Project Data (with date defaults):', JSON.parse(JSON.stringify(formData)));
				return data.managerId; // Return managerId for subsequent lookup
			} else {
				throw new Error("未找到项目数据");
			}
		} catch (error) {
			console.error("Failed to fetch project details:", error);
			initialFetchError.value = error.toString(); // Store error message
			formData.projectId = null; // Indicate data loading failure
			throw error; // Re-throw to stop subsequent loading steps if needed
		}
	};

	const fetchDepartments = async () => {
		try {
			const data = await apiRequest({
				url: '/departments/selectAll',
				loadingTitle: '加载部门...'
			});

			departments.value = data || [];
			return departments.value;
		} catch (error) {
			console.error("Failed to fetch departments:", error);
			initialFetchError.value = initialFetchError.value || error.toString(); // Store first error
			departments.value = [];
			throw error; // Re-throw
		}
	};

	const fetchEmployeesByDepartment = async (departmentId) => {
		if (!departmentId) {
			employees.value = [];
			selectedEmployeeIndex.value = -1;
			return;
		}
		fetchingEmployees.value = true;
		employees.value = []; // Clear previous list
		selectedEmployeeIndex.value = -1; // Reset index
		try {
			// No separate loading title here, as it's part of the main load or department change
			const data = await apiRequest({
				url: `/employees/selectAllByDepartmentId/${departmentId}`
			});
			if (data.data.code != 200) {
				uni.showToast({
					title: result.message,
					icon: 'none'
				});
			}
			employees.value = data || [];
		} catch (error) {
			console.error(`Failed to fetch employees for department ${departmentId}:`, error);
			uni.showToast({
				title: '加载人员列表失败',
				icon: 'none',
				duration: 2000
			});
			employees.value = [];
		} finally {
			fetchingEmployees.value = false;
		}
	};

	// --- Event Handlers ---
	const handleDateChange = (field, value) => {
		formData[field] = value;
		// Optional: Validate end date is not before start date immediately
		if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {
			uni.showToast({
				title: '提示: 结束时间早于开始时间',
				icon: 'none'
			});
			// Decide if you want to auto-correct or just warn
			// if (field == 'startDate') formData.endDate = formData.startDate;
			// else formData.startDate = formData.endDate;
		}
	};

	const handleDepartmentChange = async (e) => {
		const index = parseInt(e.detail.value);
		if (index < 0 || index >= departments.value.length || index == selectedDepartmentIndex.value) {
			console.log('Department selection unchanged or invalid');
			return; // No change or invalid index
		}

		selectedDepartmentIndex.value = index;
		const selectedDeptId = departments.value[index].id;

		// Reset employee selection and related formData fields
		selectedEmployeeIndex.value = -1;
		formData.managerId = null;
		formData.managerName = '';
		formData.managerWechat = '';
		employees.value = []; // Clear employee list visually first

		await fetchEmployeesByDepartment(selectedDeptId); // Fetch new employees
	};

	const handleEmployeeChange = (e) => {
		const index = parseInt(e.detail.value);
		if (index < 0 || index >= employees.value.length) {
			console.log('Invalid employee selection');
			return; // Invalid index
		}

		selectedEmployeeIndex.value = index;
		const selectedEmp = employees.value[index];
		formData.managerId = selectedEmp.id;
		formData.managerName = selectedEmp.name;
		// Map 'wechatName' from employee data to 'managerWechat' in formData
		formData.managerWechat = selectedEmp.wechatName || '.'; // Use '.' or '' based on detail page logic
	};

	const handleStatusChange = (e) => {
		const index = parseInt(e.detail.value);
		if (index < 0 || index >= statusOptions.value.length) return; // Invalid index

		selectedStatusIndex.value = index;
		formData.progressStatus = statusOptions.value[index];
	};

	// Basic validation for progress fields (0-100)
	const validateProgress = (field) => {
		// Use nextTick because v-model updates might not be synchronous
		nextTick(() => {
			let valueStr = String(formData[field]).trim();
			if (valueStr == '') {
				// Allow temporarily empty field, will default to 0 on save if needed
				return;
			}
			let value = parseFloat(valueStr);
			if (isNaN(value) || value < 0) {
				formData[field] = '0';
			} else if (value > 100) {
				formData[field] = '100';
			} else {
				// Allow integer or valid float input within range
				// Optional: Round or format here if needed, e.g., formData[field] = String(Math.round(value));
			}
		});
	};

	// --- Form Submission ---
	const validateForm = () => {
		if (!formData.projectName?.trim()) {
			uni.showToast({
				title: '项目名称不能为空',
				icon: 'none'
			});
			return false;
		}
		if (!formData.startDate) {
			uni.showToast({
				title: '请选择开始时间',
				icon: 'none'
			});
			return false;
		}
		if (!formData.endDate) {
			uni.showToast({
				title: '请选择结束时间',
				icon: 'none'
			});
			return false;
		}
		if (new Date(formData.startDate) > new Date(formData.endDate)) {
			uni.showToast({
				title: '结束时间不能早于开始时间',
				icon: 'none'
			});
			return false;
		}
		if (selectedDepartmentIndex.value == -1) {
			uni.showToast({
				title: '请选择所属部门',
				icon: 'none'
			});
			return false;
		}
		// Check if a department is selected but no employee is chosen (and list isn't empty/loading)
		if (selectedDepartmentIndex.value != -1 && selectedEmployeeIndex.value == -1 && !fetchingEmployees.value &&
			employees.value.length > 0) {
			uni.showToast({
				title: '请选择人员',
				icon: 'none'
			});
			return false;
		}
		// Allow saving if department has no employees
		if (selectedDepartmentIndex.value != -1 && employees.value.length == 0) {
			formData.managerId = null; // Ensure managerId is null if dept has no people
			formData.managerName = '';
			formData.managerWechat = '';
			// Allow save in this case, maybe warn user?
			console.warn("Selected department has no employees, manager fields cleared.");
		} else if (!formData.managerId && selectedDepartmentIndex.value != -1 && employees.value.length > 0) {
			// This case covers if somehow employee picker wasn't selected correctly
			uni.showToast({
				title: '请选择管理员人员',
				icon: 'none'
			});
			return false;
		}


		if (selectedStatusIndex.value == -1) {
			uni.showToast({
				title: '请选择进度状态',
				icon: 'none'
			});
			return false;
		}

		// Final validation/cleanup for progress values before submit
		formData.currentProgress = String(Math.max(0, Math.min(100, parseFloat(formData.currentProgress) || 0)));
		formData.plannedProgress = String(Math.max(0, Math.min(100, parseFloat(formData.plannedProgress) || 0)));

		// Validate numeric fields (optional, depends on backend tolerance)
		if (formData.totalValue && isNaN(parseFloat(formData.totalValue))) {
			uni.showToast({
				title: '总价值必须是数字',
				icon: 'none'
			});
			return false;
		}
		if (formData.totalCost && isNaN(parseFloat(formData.totalCost))) {
			uni.showToast({
				title: '总成本必须是数字',
				icon: 'none'
			});
			return false;
		}


		return true; // All checks passed
	};

	const saveChanges = async () => {
		if (!validateForm()) {
			return; // Stop if validation fails
		}

		isSaving.value = true;
		try {
			// *** CRITICAL: Verify the update endpoint, method, and payload structure ***
			// Assume PUT /project/update expects the formData object
			const payload = {
				...formData
			};

			// Optional: Convert numeric strings back to numbers if backend expects numbers
			// payload.totalValue = payload.totalValue ? Number(payload.totalValue) : null;
			// payload.totalCost = payload.totalCost ? Number(payload.totalCost) : null;
			// payload.currentProgress = Number(payload.currentProgress); // Check backend.
			// payload.plannedProgress = Number(payload.plannedProgress); // Check backend.


			// ** Ensure you are sending the `projectId` **
			if (!payload.projectId) {
				throw new Error("项目ID丢失，无法保存");
			}


			console.log("Sending Update Payload:", JSON.stringify(payload)); // Log before sending

			await apiRequest({
				url: '/project/update', // *** VERIFY THIS ENDPOINT ***
				method: 'POST', // *** VERIFY THIS HTTP METHOD (Often PUT or POST for updates) ***
				data: payload, // *** VERIFY EXPECTED PAYLOAD STRUCTURE ***
				loadingTitle: '正在保存...'
			});

			uni.showToast({
				title: '保存成功',
				icon: 'success',
				duration: 1500
			});

			uni.$on("updateProject")
			uni.navigateBack();


		} catch (error) {
			console.error("Failed to save project:", error);
			uni.showToast({
				title: `保存失败: ${error.toString()}`,
				icon: 'none',
				duration: 3000
			});
		} finally {
			isSaving.value = false;
		}
	};

	// --- Lifecycle Hook ---
	const loadData = async () => {
		if (!projectId.value) {
			initialFetchError.value = "项目ID无效";
			loading.value = false;
			return;
		}

		loading.value = true;
		initialFetchError.value = null; // Reset error on load/retry

		try {
			// 1. Fetch project details to get base data and managerId
			const initialManagerId = await fetchProjectDetails(projectId.value);


			// 2. Fetch all departments
			await fetchDepartments();

			// 3. Find initial department and employee selection based on initialManagerId
			if (initialManagerId && departments.value.length > 0) {
				console.log(`Attempting to find initial dept/emp for managerId: ${initialManagerId}`);
				let foundDeptIndex = -1;
				let foundEmpIndex = -1;

				// --- Optimization Placeholder ---
				// Ideally, the backend would provide the department ID along with the manager ID
				// in the project details API response. If not, the following loop is necessary
				// but less efficient as it might fetch employees for multiple departments.
				// Consider backend changes for improvement if performance becomes an issue.
				// --- End Optimization Placeholder ---


				for (let i = 0; i < departments.value.length; i++) {
					const dept = departments.value[i];
					try {
						// Fetch employees for this department to check if the manager belongs here
						// We need to do this here to pre-select the department and employee pickers
						// Avoid showing extra loading indicators for these internal checks if possible
						const tempEmployees = await apiRequest({
							url: `/employees/selectAllByDepartmentId/${dept.id}`
						});
						const empIndex = (tempEmployees || []).findIndex(emp => emp.id == initialManagerId);


						if (empIndex != -1) {
							console.log(`Manager found in department: ${dept.departmentName}`);
							foundDeptIndex = i;
							foundEmpIndex = empIndex;
							// **Important**: Assign the fetched employees to the main state
							// so the employee picker is populated correctly for the selected department
							employees.value = tempEmployees || [];
							break; // Found the manager, stop checking other departments
						}
					} catch (e) {
						// Log error but continue checking other departments
						console.warn(`Could not check employees for dept ${dept.id} during initial load:`, e);
					}
				}

				if (foundDeptIndex != -1) {
					selectedDepartmentIndex.value = foundDeptIndex;
					// Use nextTick to ensure the picker component updates before setting the employee index
					await nextTick();
					selectedEmployeeIndex.value = foundEmpIndex;
					// Ensure formData manager details are consistent (redundant check, but safe)
					if (employees.value[foundEmpIndex]) {
						const emp = employees.value[foundEmpIndex];
						formData.managerId = emp.id;
						formData.managerName = emp.name;
						formData.managerWechat = emp.wechatName || '.';
					}
					console.log(`Initial selection set: Dept Index ${foundDeptIndex}, Emp Index ${foundEmpIndex}`);
				} else {
					console.warn(
						`Could not automatically select initial department/employee for managerId: ${initialManagerId}. Manager might be inactive or in an unexpected department.`
						);
					// Keep selections at default (-1). User will need to re-select.
					selectedDepartmentIndex.value = -1;
					selectedEmployeeIndex.value = -1;
					// Clear employee list if no department is auto-selected
					employees.value = [];
					// Optionally clear manager fields in formData if lookup fails, though fetchProjectDetails already sets them initially
					// formData.managerId = null; // Might already be set by fetchProjectDetails
					// formData.managerName = '';
					// formData.managerWechat = '';
				}
			} else if (initialManagerId) {
				console.warn("Manager ID exists but no departments found or department list is empty.");
				employees.value = []; // Ensure employee list is empty
			} else {
				// No initial manager ID - likely a new project or manager not assigned
				console.log("No initial manager ID found in project details.");
				employees.value = []; // Ensure employee list is empty
			}


		} catch (error) {
			// Error is already stored in initialFetchError by the fetching functions
			console.error("Error during initial data loading sequence:", error);
		} finally {
			loading.value = false; // Loading finished (success or fail)
		}
	};


	// Retry loading data if initial fetch failed
	const retryLoad = () => {
		loadData();
	};


	onLoad((options) => {
		if (options && options.id) {
			projectId.value = parseInt(options.id);
			loadData(); // Call the async loading function
		} else {
			console.error("No project ID provided to update page.");
			initialFetchError.value = "项目ID丢失";
			loading.value = false;
			// Optionally navigate back or show a persistent error
			uni.showToast({
				title: "无法加载项目，ID丢失",
				icon: "error",
				duration: 3000
			});
			// Consider uni.navigateBack() here after a delay if appropriate
		}
	});
</script>

<style lang="scss" scoped>
	// 整个页面容器
	.page-container {
		display: flex;
		flex-direction: column;
		min-height: 100vh; // 最小高度占满视口
		background-color: #f4f6f8; // 温和的浅灰色背景
	}

	// 固定的头部标题区域
	.form-header {
		background-color: #ffffff;
		height: 6vh; // 头部高度
		padding: 20rpx 30rpx;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); // 底部阴影
		display: flex;
		/* 开启flex布局 */
		justify-content: center;
		/* 水平居中 */
		align-items: flex-end;
		/* 垂直底部对齐，与原代码一致 */

		position: sticky; // 粘性定位
		top: 0; // 顶部固定
		z-index: 10; // 保证在内容上方
	}

	// 头部标题文本
	.form-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #303133; // 深色文字
	}

	// 加载和错误状态样式
	.loading-state,
	.error-state {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding-top: 200rpx; // 距离顶部留白
		color: #909399; // 柔和的灰色
		font-size: 28rpx;
		text-align: center;
		flex-grow: 1; // 占满剩余空间
	}

	.error-state .error-text {
		margin: 20rpx 30rpx;
		color: #f56c6c; // 错误红色
	}

	.error-state button {
		margin-top: 30rpx;
	}

	// 可滚动的表单内容区域
	.form-scroll-view {
		flex: 1; // 占满头部和底部按钮之间的空间
		padding-bottom: 140rpx; // 留出底部固定按钮的高度加一些额外空间，防止内容被遮挡
		box-sizing: border-box; // 内边距和边框包含在元素总宽度和高度内
	}

	// 卡片样式
	.form-card {
		background-color: #ffffff;
		border-radius: 16rpx; // 圆角
		margin: 20rpx 24rpx; // 外部间距
		padding: 10rpx 30rpx 20rpx 30rpx; // 内部间距
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); // 卡片阴影
	}

	// 卡片标题
	.card-title {
		display: block; // 独占一行
		font-size: 30rpx;
		font-weight: bold;
		color: #303133;
		padding: 20rpx 0 15rpx 0;
		margin-bottom: 10rpx;
		border-bottom: 1rpx solid #eee; // 底部细线分隔
	}

	// 单个表单项（一行）
	.form-item {
		display: flex;
		align-items: center; // 垂直居中对齐
		padding: 28rpx 0; // 上下内边距
		border-bottom: 1rpx solid #f0f0f0; // 底部细线分隔
		font-size: 28rpx;
		min-height: 40rpx; // 最小高度

		&:last-child {
			border-bottom: none; // 最后一个项取消底部边框
		}
	}

	// 表单标签
	.form-label {
		width: 180rpx; // 固定标签宽度
		color: #606266; // 标签文字颜色
		flex-shrink: 0; // 不允许收缩
		padding-right: 20rpx; // 标签右侧间距
		position: relative;
		white-space: nowrap; // 不换行
		display: flex; // 配合flex实现星号对齐
		align-items: center; // 配合flex实现星号对齐

		&.required::before {
			content: '*';
			color: #f56c6c; // 红色星号
			margin-right: 6rpx; // 星号与文本间距
			font-size: 30rpx;
			font-weight: bold;
			line-height: 1;
		}
	}

	// 表单输入区域（包含输入框、选择器等组件的容器）
	.form-input {
		flex: 1; // 占据剩余空间
		font-size: 28rpx;
		color: #303133;
		overflow: hidden; // 防止内容溢出
		min-width: 0; // 允许在flex布局中正确收缩
	}

	// 深度选择器，用于修改 UniApp 组件内部样式
	// 修改 uni-easyinput 的输入框和 uni-textarea 的文本域
	:deep(.uni-easyinput__content-input),
	:deep(.uni-textarea-textarea) {
		font-size: 28rpx !important;
		padding-left: 0 !important; // 可能需要重置默认padding
		height: auto; // 允许高度自适应
		line-height: 1.6; // 提高 textarea 可读性
		color: #303133 !important; // 确保文字颜色
		min-height: 40rpx; // 与 form-item 最小高度一致
		display: flex; // 垂直居中
		align-items: center; // 垂直居中
	}

	// 修改 uni-easyinput 的 placeholder 颜色
	:deep(.uni-easyinput__placeholder) {
		color: #c0c4cc !important; // placeholder 颜色
		font-size: 28rpx !important;
	}

	// picker 组件的样式
	.picker {
		height: auto;
		line-height: normal;
		display: flex;
		align-items: center; // 垂直居中
		width: 100%; // 占据 form-input 的全部宽度
		font-size: 28rpx;
		color: #303133;
		min-height: 40rpx; // 与 form-item 最小高度一致
	}

	// picker 中显示选中的值或placeholder的view
	.picker-value {
		font-size: 28rpx;
		color: #303133;
		width: 100%;
		display: flex;
		justify-content: space-between; // 值和箭头之间留白
		align-items: center; // 垂直居中
		min-height: 40rpx;

		&.placeholder {
			color: #c0c4cc; // placeholder 颜色
		}
	}

	// picker 旁边的箭头图标
	.picker-arrow {
		flex-shrink: 0; // 防止箭头被压缩
		margin-left: 10rpx;
	}

	// 深度选择器，修改 uni-datetime-picker 样式
	:deep(.uni-date-editor--x .uni-date-single) {
		padding: 0 !important; // 清除默认padding
		font-size: 28rpx !important;
		color: #303133 !important; // 确保文字颜色
		height: 40rpx !important; // 匹配 form-item 高度
		line-height: 40rpx !important;
		display: flex; // 垂直居中
		align-items: center; // 垂直居中
	}

	:deep(.uni-date-x--border) {
		border: none !important; // 移除边框
	}

	:deep(.uni-date-editor--x .uni-icons) {
		color: #909399 !important; // 日期图标颜色
		order: 1; // 将图标移到文本后面
		margin-left: 10rpx;
	}

	:deep(.uni-date-single__text) {
		// 日期文本的容器
		flex-grow: 1; // 占满剩余空间
	}


	// 只读项样式
	.readonly-item {
		.form-value {
			font-size: 28rpx;
			color: #303133;
			flex: 1; // 占满剩余空间
			word-break: break-all; // 防止长文本溢出
		}
	}

	// 底部固定按钮区域
	.action-button-container {
		position: fixed; // 固定定位
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom)); // 兼容 iOS 底部安全区域
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); // 兼容 iOS 底部安全区域
		box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.08); // 顶部阴影
		z-index: 10; // 保证在内容上方
	}

	// 保存按钮样式
	.save-button {
		background-color: #007aff; // UniApp 主题蓝色
		color: #ffffff;
		font-size: 30rpx;
		border-radius: 8rpx;
		height: 80rpx;
		line-height: 80rpx;

		// 禁用状态样式
		&[disabled] {
			background-color: #a0cfff; // 浅蓝色
			color: #ffffff;
			opacity: 0.7; // 降低透明度
		}
	}

	// 给最后一个卡片增加底部间距，防止被固定按钮遮挡
	.form-scroll-view>.form-card:last-of-type {
		margin-bottom: 40rpx; // 确保最后一个卡片和底部按钮之间有足够的空间
	}
</style>