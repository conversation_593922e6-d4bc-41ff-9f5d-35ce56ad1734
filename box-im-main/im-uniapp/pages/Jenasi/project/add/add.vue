<template>
	<view style="display: flex; justify-content: center; align-items: flex-end; height: 7vh; padding-bottom: 10rpx;">
		项目列表
	</view>
	<view class="container">
		<form @submit.prevent="submitForm">

			<!-- 项目名称 -->
			<view class="form-item">
				<uni-icons type="compose" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">项目名称</text>
				<input class="input" v-model="formData.projectName" placeholder="请输入项目名称" name="projectName" />
			</view>

			<!-- 项目描述 -->
			<!-- 注意：对于textarea，使用 align-items: flex-start 可能更合适 -->
			<view class="form-item form-item-textarea">
				<uni-icons type="chat" size="20" color="#5e8acb"></uni-icons>
				<text class="label">项目描述</text>
				<textarea class="textarea" v-model="formData.projectDesc" placeholder="请输入项目描述" name="projectDesc"
					auto-height />
			</view>

			<!-- 开始时间 -->
			<view class="form-item">
				<uni-icons type="calendar" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">开始时间</text>
				<uni-datetime-picker class="flex-picker" type="date" :value="formData.startDate"
					@change="handleDateChange($event, 'startDate')" />
			</view>

			<!-- 结束时间 -->
			<view class="form-item">
				<uni-icons type="calendar-filled" size="20" color="#5e8acb"></uni-icons>
				<text class="label">结束时间</text>
				<uni-datetime-picker class="flex-picker" type="date" :value="formData.endDate"
					:start="formData.startDate || ''" @change="handleDateChange($event, 'endDate')" />
			</view>

			<!-- 总价值 -->
			<view class="form-item">
				<uni-icons type="wallet" size="20" color="#5e8acb"></uni-icons>
				<text class="label">总价值</text>
				<input class="input" type="digit" v-model="formData.totalValue" placeholder="请输入项目总价值"
					name="totalValue" />
			</view>

			<!-- 总成本 -->
			<view class="form-item">
				<uni-icons type="wallet-filled" size="20" color="#5e8acb"></uni-icons>
				<text class="label">总成本</text>
				<input class="input" type="digit" v-model="formData.totalCost" placeholder="请输入项目总成本"
					name="totalCost" />
			</view>

			<!-- 当前进度 -->
			<view class="form-item">
				<uni-icons type="flag" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">当前进度</text>
				<input class="input" v-model="formData.currentProgress" placeholder="请输入当前进度" name="currentProgress" />
			</view>


			<view class="divider">
				<uni-icons type="gear" size="16" color="#666" style="margin-right: 8rpx;"></uni-icons>
				管理员选择
			</view>

			<!-- 部门选择 -->
			<view class="form-item">
				<uni-icons type="flag" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">选择部门</text>
				<picker class="flex-picker" mode="selector" :range="departmentsList" range-key="departmentName"
					@change="handleDepartmentChange" :value="departmentIndex">
					<view class="picker-view">
						<text :class="{'placeholder-text': departmentIndex == -1}">
							{{ departmentIndex != -1 ? departmentsList[departmentIndex]?.departmentName : '请选择部门' }}
						</text>
						<uni-icons type="bottom" size="16" color="#999"></uni-icons>
					</view>
				</picker>
			</view>

			<!-- 员工 (管理员) 选择 -->
			<view class="form-item">
				<uni-icons type="person" size="20" color="#5e8acb"></uni-icons>
				<text class="label required">选择管理员</text>
				<picker class="flex-picker" mode="selector" :range="employeesList" range-key="name"
					@change="handleEmployeeChange" :value="employeeIndex"
					:disabled="departmentIndex == -1 || employeesLoading">
					<view class="picker-view" :class="{disabled: departmentIndex == -1 || employeesLoading}">
						<text v-if="employeesLoading" class="placeholder-text">加载中...</text>
						<text v-else :class="{'placeholder-text': employeeIndex == -1}">
							{{ employeeIndex != -1 ? employeesList[employeeIndex]?.name : (departmentIndex == -1 ? '请先选择部门' : '请选择管理员') }}
						</text>
						<uni-icons v-if="!employeesLoading" type="bottom" size="16" color="#999"></uni-icons>
					</view>
				</picker>
			</view>

			<!-- 显示已选管理员信息 (可选) -->
			<view v-if="formData.managerId" class="selected-manager-info">
				<uni-icons type="person-filled" size="16" color="#18b566" style="margin-right: 8rpx;"></uni-icons>
				<text>已选管理员: {{ formData.managerName }} ({{ formData.managerWechat || '无微信' }})</text>
			</view>

			<view class="button-container">
				<button class="submit-button" type="primary" @click="submitForm" :loading="isSubmitting"
					:disabled="isSubmitting || departmentsLoading || employeesLoading">
					<uni-icons v-if="!isSubmitting" type="checkmark" size="20" color="#fff"
						style="margin-right: 8rpx;"></uni-icons>
					提 交
				</button>
			</view>
		</form>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app'; // 导入 onLoad 生命周期钩子

	import UNI_APP from '@/.env.js'

	const BASE_API = UNI_APP.PROJECT

	// --- 响应式状态 ---
	const formData = reactive({
		projectName: '', // 项目名称
		projectDesc: '', // 项目描述
		startDate: null, // 开始日期 (使用 null 或空字符串作为初始值)
		endDate: null, // 结束日期
		totalValue: '', // 总价值
		totalCost: '', // 总成本
		// 管理员信息将通过选择填充
		managerId: null, // 管理员ID
		managerName: '', // 管理员名称
		managerWechat: '', // 管理员微信

		currentProgress: '' // 新添加的当前进度字段
	});

	// --- 部门选择状态 ---
	const departmentsList = ref([]); // 部门列表 (示例: {id: 1, departmentName: '研发部', ...})
	const departmentIndex = ref(-1); // 当前选中部门在 departmentsList 中的索引
	const departmentsLoading = ref(false); // 部门列表加载状态

	const test = ref("test")

	// --- 员工选择状态 ---
	const employeesList = ref([]); // 员工列表 (示例: {id: 101, name: '张三', departmentId: 1, ...})
	const employeeIndex = ref(-1); // 当前选中员工在 employeesList 中的索引
	const employeesLoading = ref(false); // 员工列表加载状态

	// --- 提交状态 ---
	const isSubmitting = ref(false); // 表单是否正在提交中

	// --- 生命周期钩子 ---
	onLoad(() => {
		fetchDepartments(); // 页面加载时获取部门列表
	});

	// --- 模拟数据获取函数 (请替换为真实的 API 请求) ---
	const fetchDepartments = async () => {
		departmentsLoading.value = true;
		console.log("开始获取部门列表...");
		// 模拟 API 调用
		await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟
		try {
			// 真实场景: const res = await uni.request({ url: '/api/departments', method: 'GET' });
			// const mockDepartments = [
			// 	{ id: 1, departmentName: '研发部', description: '技术研发核心', leaderId: 10, leaderName: '张三' },
			// 	{ id: 2, departmentName: '市场部', description: '市场推广与销售', leaderId: 20, leaderName: '李四' },
			// 	{ id: 3, departmentName: '人事部', description: '人力资源管理', leaderId: 30, leaderName: '王五' },
			//           { id: 4, departmentName: '产品部', description: '产品设计与规划', leaderId: 40, leaderName: '赵六' },
			// ];

			uni.request({
				url: `${BASE_API}/departments/selectAll`,
				success(res) {
					// test.value = res.data.data
					departmentsList.value = res.data.data; // 更新部门列表 (ref 需要 .value)
				}
			})






			// departmentsList.value = mockDepartments; // 更新部门列表 (ref 需要 .value)

			console.log("部门列表获取成功:", departmentsList.value);
		} catch (error) {
			console.error("获取部门列表失败:", error);
			uni.showToast({
				title: '获取部门列表失败',
				icon: 'none'
			});
		} finally {
			departmentsLoading.value = false; // 结束加载状态
		}
	};

	const fetchEmployees = async (departmentId) => {
		if (!departmentId) return; // 如果没有部门ID，则不执行
		employeesLoading.value = true;
		employeesList.value = []; // 清空之前的员工列表
		employeeIndex.value = -1; // 重置员工选择索引
		// 清空表单中的管理员信息
		formData.managerId = null;
		formData.managerName = '';
		formData.managerWechat = '';
		console.log(`开始获取部门 ID 为 ${departmentId} 的员工列表...`);

		// 模拟 API 调用
		try {


			uni.request({
				url: `${BASE_API}/employees/selectAllByDepartmentId/${departmentId}`,
				success(res) {
					// test.value = res.data
					employeesList.value = res.data.data
					if (employeesList.value.length == 0) {
						uni.showToast({
							title: '该部门暂无员工可选',
							icon: 'none',
							duration: 1500
						});

					}
				}
			})


		} catch (error) {
			console.error("获取员工列表失败:", error);
			uni.showToast({
				title: '获取员工列表失败',
				icon: 'none'
			});
		} finally {
			employeesLoading.value = false; // 结束加载状态
		}
	};

	// --- 事件处理函数 ---
	const handleDateChange = (selectedDate, field) => {
		// uni-datetime-picker 直接返回选中的日期字符串
		formData[field] = selectedDate; // 直接更新 reactive 对象
		console.log(`${field} 日期更改为:`, selectedDate);
		// 可选: 校验结束日期不能早于开始日期
		if (field == 'startDate' && formData.endDate && formData.startDate > formData.endDate) {
			formData.endDate = null; // 如果开始日期晚于结束日期，重置结束日期
			uni.showToast({
				title: '结束日期已重置',
				icon: 'none',
				duration: 1500
			});
		}
	};

	const handleDepartmentChange = (e) => {
		const index = parseInt(e.detail.value, 10); // picker 返回的是字符串索引，转为数字
		// 检查索引是否有效
		if (index >= 0 && index < departmentsList.value.length) {
			departmentIndex.value = index; // 更新选中部门的索引
			const selectedDepartment = departmentsList.value[departmentIndex.value];
			console.log("选择了部门:", selectedDepartment);
			fetchEmployees(selectedDepartment.id); // 根据选中的部门ID获取员工列表
		} else {
			// 处理无效选择或取消选择的情况
			departmentIndex.value = -1;
			employeesList.value = [];
			employeeIndex.value = -1;
			formData.managerId = null;
			formData.managerName = '';
			formData.managerWechat = '';
			console.log("部门选择已取消或无效");
		}
	};

	const handleEmployeeChange = (e) => {
		const index = parseInt(e.detail.value, 10); // picker 返回的是字符串索引，转为数字
		// 检查索引是否有效
		if (index >= 0 && index < employeesList.value.length) {
			employeeIndex.value = index; // 更新选中员工的索引
			const selectedEmployee = employeesList.value[employeeIndex.value];
			console.log("选择了管理员:", selectedEmployee);
			// 更新表单数据中的管理员信息
			formData.managerId = selectedEmployee.id;
			formData.managerName = selectedEmployee.name;
			formData.managerWechat = selectedEmployee.wechatName || ''; // 使用员工 DTO 中的 wechatName，如果为空则给空字符串
		} else {
			// 处理无效选择或取消选择的情况
			employeeIndex.value = -1;
			formData.managerId = null;
			formData.managerName = '';
			formData.managerWechat = '';
			console.log("管理员选择已取消或无效");
		}
	};

	// --- 表单提交 ---
	const submitForm = async () => {


		// 1. 表单校验
		if (!formData.projectName.trim()) {
			uni.showToast({
				title: '请输入项目名称',
				icon: 'error',
				duration: 1500
			}); // 使用 error 图标
			return;
		}
		if (!formData.managerId) {
			uni.showToast({
				title: '请选择管理员',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		if (formData.startDate && formData.endDate && formData.startDate > formData.endDate) {
			uni.showToast({
				title: '结束日期不能早于开始日期',
				icon: 'error',
				duration: 1500
			});
			return;
		}

		if (!formData.currentProgress || !String(formData.currentProgress).trim()) {
			uni.showToast({
				title: '请输入当前进度',
				icon: 'error',
				duration: 1500
			});
			return;
		}


		if (!formData.startDate) { // 检查 startDate 是否为 null 或未定义
			uni.showToast({
				title: '请选择开始时间',
				icon: 'error',
				duration: 1500
			});
			return;
		}
		// 可在此处添加更多校验规则...



		isSubmitting.value = true; // 开始提交，设置按钮为加载状态
		// 使用 JSON.parse(JSON.stringify(formData)) 获取纯净的 JS 对象副本，便于打印或发送
		console.log("准备提交的表单数据:", JSON.parse(JSON.stringify(formData)));

		// 2. 准备提交数据 (如果API需要特定格式，在此处处理，例如日期格式化)
		const payload = {
			...formData
		};
		// 示例：如果API需要ISO 8601日期格式
		// if (payload.startDate) payload.startDate = new Date(payload.startDate).toISOString();
		// if (payload.endDate) payload.endDate = new Date(payload.endDate).toISOString();

		// 3. 调用 API 接口
		try {
			// 替换为真实的 API 请求

			// 真实场景:
			const res = await uni.request({
				url: `${BASE_API}/project/add`, // 你的后端接口地址
				method: 'POST',
				data: payload,
			});

			// test.value = res.data

			if (res.data.code != 200) {
				uni.showToast({
					title: res.data.message,
					icon: 'success',
					duration: 1500
				});
				return
			}

			// 模拟成功
			console.log("提交成功 (模拟)");
			uni.showToast({
				title: '项目创建成功',
				icon: 'success',
				duration: 1500
			});


			uni.$emit('projectAdd');
			uni.navigateBack();
		} catch (error) {
			console.error("表单提交失败:", error);
			uni.showToast({
				title: error.message || '项目创建失败，请重试',
				icon: 'none',
				duration: 2000
			});
		} finally {
			isSubmitting.value = false; // 无论成功或失败，结束提交状态
		}
	};

	// --- 可选的重置表单函数 ---
	// const resetForm = () => {
	//     // 重置 reactive 对象 formData 的所有属性
	//     Object.assign(formData, {
	//         projectName: '', projectDesc: '', startDate: null, endDate: null,
	//         totalValue: '', totalCost: '', managerId: null, managerName: '', managerWechat: '',
	//     });
	//     departmentIndex.value = -1; // 重置部门选择
	//     employeeIndex.value = -1;   // 重置员工选择
	//     employeesList.value = [];   // 清空员工列表
	//     // 可选：是否需要重新获取部门列表
	//     // departmentsList.value = [];
	//     // fetchDepartments();
	//     uni.showToast({ title: '表单已重置', icon: 'none' });
	// };
</script>

<style scoped>
	.container {
		padding: 20rpx 30rpx 40rpx;
		/* 调整内边距 */
		background-color: #f9f9f9;
		/* 浅灰色背景 */
		min-height: 100vh;
		box-sizing: border-box;
	}

	.form-item {
		background-color: #ffffff;
		padding: 25rpx 30rpx;
		/* 增加垂直内边距 */
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		/* 更圆润的边角 */
		display: flex;
		align-items: center;
		/* 垂直居中对齐 Icon, Label, Input */
		border: 1rpx solid #eee;
		/* 添加细边框 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		/* 轻微阴影 */
	}

	/* 特殊处理包含 textarea 的 form-item */
	.form-item-textarea {
		align-items: flex-start;
		/* 图标和标签顶部对齐 */
	}

	.form-item-textarea .uni-icons {
		margin-top: 6rpx;
		/* 微调图标位置，使其与标签顶部大致对齐 */
	}

	.form-item-textarea .label {
		margin-top: 6rpx;
		/* 微调标签位置 */
	}

	.progress-bar {
		width: 100%;
		height: 20rpx;
		border-radius: 10rpx;
		background-color: #e6e6e6;
	}

	s

	/* 图标样式 */
	.uni-icons {
		margin-right: 20rpx;
		/* 图标和标签之间的距离 */
		flex-shrink: 0;
		/* 防止图标被压缩 */
	}

	.label {
		width: 160rpx;
		/* 调整标签宽度 */
		font-size: 28rpx;
		/* 调整字体大小 */
		color: #333;
		/* margin-right: 20rpx; (由图标的 margin-right 控制) */
		flex-shrink: 0;
		/* 防止标签被压缩 */
		line-height: 1.5;
		/* 增加行高防止文字截断 */
	}

	.label.required::before {
		content: '*';
		color: #fa3534;
		/* 醒目的红色 */
		margin-right: 6rpx;
		font-weight: bold;
		/* 加粗星号 */
	}

	.input,
	.textarea {
		flex: 1;
		/* 占据剩余空间 */
		font-size: 28rpx;
		color: #333;
		/* 输入文字颜色加深 */
		padding: 0;
		/* 移除默认内边距，通过 form-item 控制 */
		line-height: 1.5;
		background-color: transparent;
		/* 输入框背景透明 */
	}

	.textarea {
		min-height: 120rpx;
		/* 增加最小高度 */
		padding: 10rpx 0;
		/* 增加文本域内部垂直padding */
		width: 100%;
		box-sizing: border-box;
	}

	/* 选择器 (Picker, DateTimePicker) 占据剩余空间 */
	.flex-picker {
		flex: 1;
		/* background-color: #e0e0e0; */
		/* 用于调试对齐 */
	}

	/* Picker 内部视图样式 */
	.picker-view {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		/* padding: 15rpx 0; (调整为与 input 对齐) */
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		/* 确保宽度填满 */
	}

	.picker-view.disabled {
		color: #c0c4cc;
		/* 禁用文字颜色 */
		/* background-color: #f5f7fa;  */
		/* 可选：禁用背景色 */
	}

	.placeholder-text {
		color: #999;
		/* 占位符文字颜色 */
	}

	/* 调整 uni-datetime-picker 内部样式对齐 */
	/* 注意：深度选择器 (::v-deep 或 >>>) 可能因 UniApp 版本或平台有差异 */
	:deep(.uni-date-editor) {
		flex: 1;
		/* border: none !important; */
		/* 移除默认边框 */
		height: auto !important;
		/* background-color: #d0d0d0; */
		/* 用于调试对齐 */
	}

	:deep(.uni-date-single) {
		padding: 0 !important;
		/* 移除内部padding */
		font-size: 28rpx !important;
		color: #333 !important;
		line-height: 1.5 !important;
		/* 确保与 input/picker 行高一致 */
		height: auto !important;
		/* display: flex; */
		/* align-items: center; */
	}

	:deep(.uni-date__input) {
		height: auto !important;
		line-height: 1.5 !important;
		padding: 0 !important;
		font-size: 28rpx !important;
		color: #333 !important;
	}

	:deep(.uni-date-x--border) {
		border: none !important;
		/* 移除边框 */
	}

	/* 隐藏日期选择器自带的日历图标 (如果和我们添加的图标重复) */
	:deep(.uni-date__icon-clear) {
		display: none !important;
	}

	:deep(.uni-icons-calendar) {
		/* display: none !important;  */
		/* 如果想完全隐藏 */
	}


	.divider {
		font-size: 26rpx;
		/* 分隔线文字稍小 */
		color: #666;
		padding: 30rpx 0 15rpx 0;
		/* 增加上下间距 */
		margin-top: 10rpx;
		/* border-top: 1rpx solid #eee; */
		/* 移除顶部线条，用文字和间距分隔 */
		display: flex;
		align-items: center;
	}

	.selected-manager-info {
		font-size: 26rpx;
		color: #18b566;
		/* 成功/选中的颜色 */
		margin-top: -10rpx;
		margin-bottom: 20rpx;
		padding: 15rpx 30rpx;
		/* 增加内边距 */
		background-color: #e8f8f0;
		/* 淡绿色背景 */
		border-radius: 8rpx;
		display: flex;
		align-items: center;
	}


	.button-container {
		margin-top: 50rpx;
		/* 增加按钮与上方元素的距离 */
		padding: 0 30rpx;
	}

	.submit-button {
		background-color: #409eff;
		/* 更柔和的蓝色 */
		color: white;
		border-radius: 50rpx;
		/* 圆角按钮 */
		font-size: 32rpx;
		/* 稍大字体 */
		height: 90rpx;
		/* 按钮高度 */
		line-height: 90rpx;
		/* 使文字垂直居中 */
		display: flex;
		/* 使用 flex 布局让图标和文字居中 */
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 10rpx rgba(64, 158, 255, 0.4);
		/* 按钮阴影 */
	}

	/* 按钮禁用和加载状态 */
	.submit-button[disabled] {
		background-color: #a0cfff !important;
		/* 禁用时的背景色 */
		color: #ffffff !important;
		opacity: 0.8;
		/* 轻微透明 */
		box-shadow: none;
		/* 移除阴影 */
	}

	.submit-button[loading] {
		background-color: #66b1ff !important;
		/* 加载时的背景色 */
		/* uni-app button 的 loading 默认会处理 */
	}
</style>