<template>
	<view class="container">

		<!-- 组件显示区域 -->
		<view class="main-content">
			<view v-if="selectedContent === 'HomeContent'" class="content-block home-content">
				<ProjectList />
			</view>

			<view v-if="selectedContent === 'PageTwoContent'" class="content-block page-two-content">
				<tree />
			</view>

			<view v-if="selectedContent === 'PageSearch'" class="content-block page-one-content">
				<search/>
			</view>

		</view>

		<view class="bottom-nav-bar">
			<view class="nav-bar-item" @click="selectContent('PageSearch')"
				:class="{ 'active': selectedContent === 'PageSearch' }">
				<text class="icon">&#127963;</text> <text class="text">首页</text>
			</view>

			<view class="drawer-button-bottom" @click="toggleDrawer" aria-label="打开导航菜单">
				<text class="icon">&#9776;</text>
			</view>

			<view class="nav-bar-item" @click="selectContent('PageTwoContent')"
				:class="{ 'active': selectedContent === 'PageTwoContent' }">
				<text class="icon">&#128221;</text> <text class="text">项目树</text>
			</view>
		</view>

		<view :class="['drawer-menu-bottom', { 'open': isDrawerOpen }]">
			<view class="drawer-header-bottom">
				<text class="header-title">更多导航</text>
				<view class="close-button-bottom" @click="toggleDrawer" aria-label="关闭导航菜单">X</view>
			</view>
			
			<view class="nav-list-bottom">
				<view :class="['nav-item-bottom', { 'active': selectedContent === 'HomeContent' }]"
					@click="selectContent('HomeContent')">
					<text>项目列表</text>
				</view>
			</view>
			
		</view>

		<view v-if="isDrawerOpen" class="overlay" @click="toggleDrawer"></view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import ProjectList from '@/pages/Jenasi/project/ProjectList.vue'
	import tree from '@/pages/Jenasi/project/tree/tree.vue'
	import search from '@/pages/Jenasi/project/page/search.vue'

	// 控制抽屉的显示/隐藏
	const isDrawerOpen = ref(false);
	// 控制主内容区域显示哪个内容块
	
	
	//这个值等于什么就显示什么
	const selectedContent = ref('PageSearch'); // 默认显示首页内容

	/**
	 * 切换抽屉的打开/关闭状态。
	 */
	const toggleDrawer = () => {
		isDrawerOpen.value = !isDrawerOpen.value;
	};

	/**
	 * 选择要显示的内容块并关闭抽屉。
	 * @param {string} contentName - 要显示的内容块的名称。
	 */
	const selectContent = (contentName) => {
		selectedContent.value = contentName;
		isDrawerOpen.value = false; // 选择内容后关闭抽屉
	};
</script>

<style lang="scss" scoped>
	/* 页面整体布局 */
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;

		overflow: hidden;
		background-color: #f8f9fa;
		/* 轻微的背景色 */
	}

	/* 主内容区域 */
	.main-content {
		flex: 1;
		/* 占据除底部导航栏外的所有空间 */
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		// padding: 40rpx; /* 调整内边距，因为顶部不再有固定按钮 */
		// text-align: center;
		box-sizing: border-box;
		/* 确保padding不撑大元素 */
		padding-bottom: 160rpx;
		/* 为底部导航栏留出空间 */
	}

	.content-block {
		background-color: #ffffff;
		border-radius: 16rpx;
		// padding: 60rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		// width: 90%;
		max-width: 700rpx;
		/* 控制最大宽度 */
		margin-bottom: 40rpx;
		animation: fadeIn 0.5s ease-out;
		/* 内容切换时的淡入动画 */
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		/* 文本之间的间距 */
	}

	.content-title {
		font-size: 48rpx;
		/* 标题更大 */
		font-weight: bold;
		color: #2c3e50;
		margin-bottom: 20rpx;
	}

	.content-description {
		font-size: 32rpx;
		color: #607d8b;
		line-height: 1.6;
	}

	/* 底部导航栏 */
	.bottom-nav-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 120rpx;
		/* 底部导航栏高度 */
		background-color: #ffffff;
		box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
		/* 向上阴影 */
		display: flex;
		justify-content: space-around;
		align-items: center;
		z-index: 102;
		/* 确保在抽屉和遮罩之上 */
		padding-bottom: constant(safe-area-inset-bottom);
		/* 适配iPhoneX刘海屏底部安全区域 */
		padding-bottom: env(safe-area-inset-bottom);
	}

	.nav-bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		color: #607d8b;
		font-size: 24rpx;
		transition: color 0.2s ease;

		.icon {
			font-size: 48rpx;
			margin-bottom: 8rpx;
		}

		.text {
			font-size: 24rpx;
		}

		&.active {
			color: #007bff;
		}
	}


	/* 底部抽屉按钮 */
	.drawer-button-bottom {
		background-color: #007bff;
		/* 蓝色圆形按钮 */
		border-radius: 50%;
		width: 100rpx;
		/* 按钮宽度 */
		height: 100rpx;
		/* 按钮高度 */
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
		position: relative;
		/* 确保z-index有效 */
		z-index: 103;
		/* 确保在底部导航栏之上 */
		transform: translateY(-30rpx);
		/* 向上浮动，形成突出效果 */
		transition: transform 0.2s ease, background-color 0.2s ease;

		&:active {
			transform: translateY(-25rpx) scale(0.95);
			background-color: #0056b3;
		}

		.icon {
			font-size: 48rpx;
			color: #ffffff;
			/* 白色图标 */
		}
	}


	/* 底部抽屉菜单 */
	.drawer-menu-bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		/* 底部抽屉宽度占满 */
		height: 0;
		/* 初始高度为0，隐藏 */
		background-color: #ffffff;
		box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.15);
		/* 向上阴影 */
		overflow: hidden;
		transition: height 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
		/* 高度过渡动画 */
		z-index: 99;
		/* 在主内容之上，底部导航之下 */
		display: flex;
		flex-direction: column;
		padding-bottom: constant(safe-area-inset-bottom);
		/* 适配iPhoneX刘海屏底部安全区域 */
		padding-bottom: env(safe-area-inset-bottom);
	}

	.drawer-menu-bottom.open {
		height: 60vh;
		/* 打开时占据60%视口高度 */
	}

	.drawer-header-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 30rpx;
		background-color: #f0f3f6;
		font-size: 38rpx;
		font-weight: bold;
		color: #2c3e50;
		border-bottom: 1rpx solid #e0e6ed;
	}

	.close-button-bottom {
		font-size: 44rpx;
		color: #607d8b;
		padding: 10rpx;
		transition: transform 0.2s ease;

		&:active {
			transform: rotate(90deg);
		}
	}

	.nav-list-bottom {
		flex: 1;
		padding: 20rpx 0;
	}

	.nav-item-bottom {
		padding: 35rpx 40rpx;
		font-size: 34rpx;
		color: #333;
		border-bottom: 1rpx solid #f7f7f7;
		transition: background-color 0.2s ease;

		&:active {
			background-color: #eef2f6;
		}

		&.active {
			background-color: #e0f2f7;
			color: #007bff;
			font-weight: bold;
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				width: 8rpx;
				background-color: #007bff;
				border-radius: 0 4rpx 4rpx 0;
			}
		}
	}

	/* 遮罩层 */
	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.4);
		/* 半透明黑色 */
		z-index: 98;
		/* 在抽屉菜单之下 */
		transition: opacity 0.3s ease;
		opacity: 0;
		/* 初始隐藏 */
		pointer-events: none;
		/* 初始禁用事件，避免遮挡点击 */
	}

	/* 当抽屉打开时，显示遮罩层并允许点击 */
	.drawer-menu-bottom.open+.overlay {
		opacity: 1;
		pointer-events: auto;
	}

	/* 动画效果 */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>