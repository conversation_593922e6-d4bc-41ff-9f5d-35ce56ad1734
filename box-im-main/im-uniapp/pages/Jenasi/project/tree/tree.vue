<template>
	<view class="container" style="overflow-y: auto">
		<text class="page-title">项目与任务管理</text>

		<view v-if="isLoading" class="loading-indicator">
			<text>数据加载中，请稍候...</text>
		</view>

		<view v-if="error" class="error-message">
			<text>{{ error }}</text>
			<button @click="fetchProjects" class="retry-button">点击重试</button>
		</view>

		<view class="project-list" v-if="!isLoading && !error && projects.length > 0">
			<uni-collapse v-for="(project, projectIndex) in projects" :key="project.projectId">
				<uni-collapse-item :title="project.name" :open="project.open"
					@change="handleCollapseChange(projectIndex, $event)" show-arrow class="project-item">
					<template #title>
						<view class="project-title-content">
							<text class="project-name">{{ project.name }}</text>
							<view class="project-meta">
								<text class="task-count">{{ project.tasks.length }} 任务</text>
								<text
									:class="['project-status', getProjectCssClass(project.progressStatus)]">{{ project.progressStatus }}</text>
							</view>
						</view>
					</template>

					<view class="task-list">
						<view v-if="project.tasks.length === 0" class="no-tasks">
							<text>暂无任务</text>
						</view>
						<view v-for="(task, taskIndex) in project.tasks" :key="task.taskId" class="task-item" @click="toTask(task.taskId)">
							<text class="task-name">{{ task.name }}</text>
							<view class="task-details">
								<text :class="['task-priority', getTaskCssClass(task.status)]">{{ task.status }}</text>
								<text class="task-due-date">{{ task.dueDate }}</text>
							</view>
						</view>
					</view>
				</uni-collapse-item>
			</uni-collapse>
		</view>

		<view v-if="!isLoading && !error && projects.length === 0" class="no-data">
			<text>暂无项目数据</text>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	// 引入 uni-ui 组件
	import uniCollapse from '@/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue';
	import uniCollapseItem from '@/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue';

	import UNI_APP from '@/.env.js'
	const BASE_API = UNI_APP.PROJECT

	// 存储项目数据
	const projects = ref([]);
	// 加载状态
	const isLoading = ref(true);
	// 错误信息
	const error = ref(null);
	
	//跳转子任务
	const toTask = (taskId) =>{
		// console.log('点击了任务:', projectId);		
		uni.navigateTo({
			url: `/pages/Jenasi/project/info/plan/plan?id=${taskId}`
		});
	}

	/**
	 * 格式化日期字符串，只保留年月日
	 * @param {string} dateString - 日期字符串 (e.g., "2025-05-14T06:15:52.954+00:00")
	 * @returns {string} 格式化后的日期 (e.g., "2025-05-14")
	 */
	const formatDate = (dateString) => {
		if (!dateString) return '';
		try {
			// 尝试使用 Date 对象解析
			const date = new Date(dateString);
			// 检查日期是否有效
			if (!isNaN(date.getTime())) {
				return date.toISOString().split('T')[0]; // 获取YYYY-MM-DD 部分
			}
			// 如果 Date 对象解析失败，尝试手动解析YYYY-MM-DD 部分
			const parts = dateString.split('T')[0].split('-');
			if (parts.length === 3) {
				return `${parts[0]}-${parts[1]}-${parts[2]}`;
			}
			return dateString; // 无法解析则返回原始字符串
		} catch (e) {
			console.error("日期格式化失败:", e);
			return dateString; // 出现错误返回原始字符串
		}
	};

	/**
	 * 根据项目状态返回对应的 CSS 类名。
	 * @param {string} status - 项目状态的中文标识。
	 * @returns {string} CSS 类名。
	 */
	const getProjectCssClass = (status) => {
		switch (status) {
			case '未开始':
				return 'pending'; // 黄色系，表示待处理或初始状态
			case '进行中':
			case '已暂停':
				return 'in_progress'; // 蓝色系，表示进行中或暂时挂起
			case '延期':
				return 'delayed'; // 橙红色系，表示警告或延期
			case '已完成':
			case '正常':
				return 'completed'; // 绿色系
			case '已取消':
				return 'cancelled'; // 红色系，表示终止
			default:
				return '';
		}
	};
	/**
	 * 根据任务状态返回对应的 CSS 类名。
	 * @param {string} status - 任务状态的中文标识。
	 * @returns {string} CSS 类名。
	 */
	const getTaskCssClass = (status) => {
		switch (status) {
			case '草稿':
			case '待处理':
				return 'pending'; // 黄色系
			case '进行中':
				return 'in_progress'; // 蓝色系
			case '暂停/挂起':
				return 'paused'; // 紫色系，表示暂时挂起
			case '已归档':
			case '已完成':
				return 'completed'; // 绿色系
			case '失败':
			case '已取消':
			case '已拒绝':
				return 'failed'; // 红色系，表示失败或拒绝
			default:
				return '';
		}
	};


	/**
	 * 将API数据映射到前端所需的格式
	 * @param {Array} apiData - 从API获取的原始数据
	 * @returns {Array} 格式化后的项目数据
	 */
	const mapApiDataToProjects = (apiData) => {
		return apiData.map(project => ({
			projectId: project.projectId,
			name: project.projectName,
			progressStatus: project.progressStatus, // 存储原始的 progressStatus
			open: false, // 默认不展开
			tasks: project.tacksTreeVoList ? project.tacksTreeVoList.map(task => ({
				taskId: task.taskId,
				name: task.taskName,
				status: task.status, // 存储原始的 status
				dueDate: formatDate(task.updatedAt), // 格式化日期
			})) : [],
		}));
	};

	/**
	 * 从API获取项目和任务数据
	 */
	const fetchProjects = async () => {
		isLoading.value = true;
		error.value = null; // 重置错误信息
		try {
			const response = await uni.request({
				url: `${BASE_API}/project/selectTaskTree`,
				method: 'GET',
				header: {
					'Content-Type': 'application/json'
					// 如果需要认证，这里可以添加 token
					// 'Authorization': 'Bearer YOUR_TOKEN'
				}
			});

			// uni.request 的成功回调会返回一个对象，data 属性中包含 response
			if (response.statusCode === 200 && response.data && response.data.code === "200") {
				projects.value = mapApiDataToProjects(response.data.data);
			} else {
				// 处理 API 返回的业务错误或非200状态码
				error.value = response.data?.message || '获取数据失败，请稍后重试。';
				console.error("API Error:", response.data);
			}
		} catch (e) {
			// 处理网络请求错误
			error.value = '网络请求失败，请检查网络连接或服务器状态。';
			console.error("Network Error:", e);
		} finally {
			isLoading.value = false;
		}
	};

	/**
	 * 处理折叠面板的展开/收起事件。
	 * @param {number} index - 当前操作的项目索引。
	 * @param {boolean} isOpen - 是否是展开状态。
	 */
	const handleCollapseChange = (index, isOpen) => {
		if (projects.value[index]) {
			projects.value[index].open = isOpen;
		}
	};

	// 组件挂载时获取数据
	onMounted(() => {
		fetchProjects();
	});
</script>

<style lang="scss" scoped>
	// 定义颜色变量
	$primary-color: #42b983; // 绿色，常用于积极、普通优先级
	$success-color: #28a745; // 成功绿色
	$warning-color: #ffc107; // 警告黄色
	$danger-color: #dc3545; // 危险红色，常用于高优先级
	$info-color: #17a2b8; // 信息蓝色，常用于进行中
	$purple-color: #6f42c1; // 紫色，用于暂停/挂起
	$orange-red-color: #fd7e14; // 橙红色，用于延期

	$text-color-dark: #34495e; // 深色文本
	$text-color-light: #6c757d; // 浅色文本
	$border-color: rgba(0, 0, 0, 0.3); // 边框颜色
	$background-light: #f5f7fa; // 页面浅背景
	$card-background: #ffffff; // 卡片背景
	$shadow-light: rgba(0, 0, 0, 0.08); // 轻微阴影

	.container {
		margin-top: 5vh;
		padding: 30rpx;
		background-color: $background-light;
		min-height: 100vh;
		box-sizing: border-box;
	}

	.page-title {
		font-size: 48rpx;
		font-weight: bold;
		color: $text-color-dark;
		margin-bottom: 30rpx;
		text-align: center;
		display: block;
		letter-spacing: 2rpx;
	}

	.loading-indicator,
	.error-message,
	.no-data {
		text-align: center;
		padding: 40rpx;
		color: $text-color-light;
		font-size: 30rpx;
	}

	.error-message {
		color: $danger-color;

		.retry-button {
			margin-top: 20rpx;
			background-color: $primary-color;
			color: #fff;
			font-size: 28rpx;
			padding: 10rpx 30rpx;
			border-radius: 10rpx;
			border: none; // 移除默认按钮边框
		}
	}


	.project-list {
		.project-item {
			margin-bottom: 20rpx;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 16rpx $shadow-light;

			/* 覆盖 uni-collapse-item 的内部样式 */
			::v-deep .uni-collapse-item__wrap {
				// 使用 ::v-deep 穿透组件样式
				padding: 0 !important;
			}

			::v-deep .uni-collapse-item__wrap-content {
				// 使用 ::v-deep 穿透组件样式
				padding: 0 !important;
			}

			::v-deep .uni-collapse-item__title {
				// 使用 ::v-deep 穿透组件样式
				padding: 30rpx 40rpx;
				background-color: $card-background;
				border-bottom: none !important;
				display: flex;
				align-items: center;
			}

			::v-deep .uni-collapse-item__arrow {
				// 使用 ::v-deep 穿透组件样式
				color: $text-color-light !important;
				font-size: 38rpx !important;
				margin-left: 20rpx;
			}
		}
	}

	.project-title-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex: 1;
		margin-right: 20rpx;
	}

	.project-name {
		font-size: 34rpx;
		font-weight: bold;
		color: $text-color-dark;
		flex: 1;
		margin-right: 20rpx;
		word-break: break-all;
	}

	.project-meta {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.task-count {
		font-size: 26rpx;
		color: $text-color-light;
		background-color: #eef2f7;
		padding: 8rpx 14rpx;
		border-radius: 14rpx;
		font-weight: 500;
	}

	.project-status {
		font-size: 26rpx;
		padding: 8rpx 14rpx;
		border-radius: 14rpx;
		font-weight: 500;
		min-width: 100rpx;
		text-align: center;
		white-space: nowrap;

		&.pending {
			// 项目正常、未开始 (黄色系)
			background-color: lighten($warning-color, 38%);
			color: darken($warning-color, 18%);
		}

		&.in_progress {
			// 项目进行中、已暂停 (蓝色系)
			background-color: lighten($info-color, 38%);
			color: darken($info-color, 18%);
		}

		&.delayed {
			// 项目延期 (橙红色系) - 新增
			background-color: lighten($orange-red-color, 38%);
			color: darken($orange-red-color, 18%);
		}

		&.completed {
			// 项目已完成 (绿色系)
			background-color: lighten($success-color, 38%);
			color: darken($success-color, 18%);
		}

		&.cancelled {
			// 项目已取消 (深红色系)
			background-color: lighten($danger-color, 38%);
			color: darken($danger-color, 18%);
		}
	}

	.task-list {
		padding: 0 40rpx 20rpx;
		background-color: #fcfcfc;
		border-top: 1rpx solid lighten($border-color, 5%);
	}

	.no-tasks {
		padding: 30rpx 0;
		text-align: center;
		color: $text-color-light;
		font-size: 28rpx;
	}

	.task-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx dashed lighten($border-color, 10%); // 虚线分隔符

		&:last-child {
			border-bottom: none;
		}
	}

	.task-name {
		font-size: 30rpx;
		color: $text-color-dark;
		flex: 1;
		margin-right: 20rpx;
		line-height: 1.4;
	}

	.task-details {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.task-priority {
		font-size: 24rpx;
		padding: 6rpx 10rpx;
		border-radius: 10rpx;
		font-weight: 500;
		min-width: 60rpx;
		text-align: center;
		white-space: nowrap;

		&.pending {
			// 任务草稿、待处理 (黄色系)
			background-color: lighten($warning-color, 38%);
			color: darken($warning-color, 18%);
		}

		&.in_progress {
			// 任务进行中 (蓝色系)
			background-color: lighten($info-color, 38%);
			color: darken($info-color, 18%);
		}

		&.paused {
			// 任务暂停/挂起 (紫色系) - 新增
			background-color: lighten($purple-color, 38%);
			color: darken($purple-color, 18%);
		}

		&.completed {
			// 任务已完成、已归档 (绿色系)
			background-color: lighten($success-color, 38%);
			color: darken($success-color, 18%);
		}

		&.failed {
			// 任务失败、已取消、已拒绝 (红色系)
			background-color: lighten($danger-color, 38%);
			color: darken($danger-color, 18%);
		}
	}

	.task-due-date {
		font-size: 24rpx;
		color: $text-color-light;
		white-space: nowrap;
	}

	::v-deep .uni-collapse-item__wrap-content {
		transition: max-height 0.3s ease-out, padding 0.3s ease-out;
		/* 关键属性，控制高度和内边距过渡 */
		overflow: hidden;
		/* 确保内容超出时隐藏 */
	}

	/* 箭头旋转动画 */
	::v-deep .uni-collapse-item__arrow {
		transition: transform 0.3s ease;

		&.uni-collapse-item__arrow-active {
			transform: rotate(90deg);
			/* 展开时箭头旋转 */
		}
	}
</style>