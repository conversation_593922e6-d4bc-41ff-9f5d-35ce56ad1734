<template>
  <view class="test-scan">
    <nav-bar add @add="onShowMenu">聊天功能测试</nav-bar>
    
    <view class="content">
      <view class="demo-section">
        <text class="demo-title">聊天功能菜单演示</text>
        <text class="demo-desc">点击右上角的+号按钮体验完整的聊天功能菜单</text>
        
        <view class="demo-actions">
          <button class="demo-btn" @click="onShowMenu">
            <uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
            <text>打开功能菜单</text>
          </button>
        </view>
      </view>
      
      <view class="feature-list">
        <view class="feature-item">
          <view class="feature-icon">
            <uni-icons type="person-add" size="24" color="#4f46e5"></uni-icons>
          </view>
          <view class="feature-content">
            <text class="feature-title">添加好友</text>
            <text class="feature-desc">通过搜索添加新好友</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <uni-icons type="contact" size="24" color="#06b6d4"></uni-icons>
          </view>
          <view class="feature-content">
            <text class="feature-title">创建群聊</text>
            <text class="feature-desc">发起群聊邀请好友</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <uni-icons type="scan" size="24" color="#10b981"></uni-icons>
          </view>
          <view class="feature-content">
            <text class="feature-title">扫码登录</text>
            <text class="feature-desc">扫码快速登录系统</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <uni-icons type="qr-code" size="24" color="#f59e0b"></uni-icons>
          </view>
          <view class="feature-content">
            <text class="feature-title">扫一扫</text>
            <text class="feature-desc">扫描二维码获取信息</text>
          </view>
        </view>
      </view>
    </view>
    
    <chat-menu-modal ref="chatMenuModal"></chat-menu-modal>
  </view>
</template>

<script>
import chatMenuModal from '@/components/chat-menu-modal/chat-menu-modal.vue';

export default {
  components: {
    chatMenuModal
  },
  methods: {
    onShowMenu() {
      this.$refs.chatMenuModal.open();
    }
  }
}
</script> 