<template>
	<view class="page mine">
		<nav-bar>我的</nav-bar>
		<uni-card :is-shadow="false" is-full :border="false">
			<view class="content" @click="onModifyInfo()">
				<head-image :name="userInfo.nickName" :url="userInfo.headImage" :size="160"></head-image>
				<view class="info-item">
					<view class="info-primary">
						<text class="info-username">
							{{ userInfo.userName }}
						</text>
						<text v-show="userInfo.sex == 0" class="iconfont icon-man" color="darkblue"></text>
						<text v-show="userInfo.sex == 1" class="iconfont icon-girl" color="darkred"></text>
					</view>
					<view class="info-text">
						<text class="label-text">
							昵称:
						</text>
						<text class="content-text">
							{{ userInfo.nickName }}
						</text>
					</view>
					<view class="info-text">
						<text class="label-text">
							签名:
						</text>
						<text class="content-text">
							{{ userInfo.signature }}
						</text>
					</view>
					<view class="info-text">
						<text class="label-text">
							手机:
						</text>
						<text class="content-text">
							{{ userInfo.phone || '未绑定' }}
						</text>
					</view>
				</view>
				<uni-icons class="info-arrow" type="right" size="16"></uni-icons>
			</view>
		</uni-card>
		<bar-group>
			<arrow-bar title="修改密码" icon="icon-modify-pwd" @tap="onModifyPassword()"></arrow-bar>
			<arrow-bar title="绑定手机" icon="icon-phone" @tap="onBindPhone()"></arrow-bar>
			<arrow-bar title="扫码登录" icon="icon-scan" @tap="onScanLogin()"></arrow-bar>
			<arrow-bar title="注销账号" icon="icon-un-register" @tap="onUnregister()"></arrow-bar>
		</bar-group>
		<bar-group> 
			<arrow-bar title="用户协议" icon="icon-user-protocol" @tap="onShowProtocol()"></arrow-bar>
			<arrow-bar title="隐私政策" icon="icon-privacy-protocol" @tap="onShowPrivacy()"></arrow-bar>
		</bar-group>
		
		<bar-group>
			<arrow-bar title="编程" icon="" @tap="Interface()"></arrow-bar>
			<arrow-bar title="项目管理" icon="" @tap="projectManagement()"></arrow-bar>
			<arrow-bar title="出入库扫码设计" icon="icon-warehouse" @tap="onWarehouseManagement()"></arrow-bar>
		</bar-group>

		
		
		<bar-group>
			<btn-bar title="退出登录" type="danger" @tap="onQuit()"></btn-bar>
		</bar-group>
	</view>
</template>

<script>
import UNI_APP from '@/.env.js';
export default {
	data() {
		return {}
	},
	methods: {
		Interface(){
			let targetUrl = encodeURIComponent('http://jenasi.ai:82');
			uni.navigateTo({
			  url: '/pages/webview/webview?url=' + targetUrl
			});
		},
		projectManagement(){
			uni.navigateTo({
			  url: '/pages/Jenasi/project/index/index'
			});
		},
		onWarehouseManagement(){
			uni.navigateTo({
				url: '/pages/warehouse/warehouse-main'
			});
		},
		onModifyInfo() {
			uni.navigateTo({
				url: "/pages/mine/mine-edit"
			})
		},
		onModifyPassword() {
			uni.navigateTo({
				url: "/pages/mine/mine-password"
			})
		},
		onBindPhone() {
			if (this.userInfo.phone) {
				uni.showToast({
					title: '您已绑定手机号',
					icon: 'none'
				})
				return
			}
			uni.navigateTo({
				url: "/pages/mine/mine-phone"
			})
		},
		onScanLogin() {
			uni.navigateTo({
				url: "/pages/scan/scan"
			})
		},
		onUnregister() {
			uni.showModal({
				title: '注销账号',
				content: '账号注销后将无法恢复，确认注销吗?',
				success: (res) => {
					if (res.confirm) {
						this.$http({
							url: '/unregister',
							method: 'DELETE'
						})
					}
				}
			});
		},
		onShowProtocol() {
			const linkUrl = encodeURIComponent(UNI_APP.PROTOCOL_URL);
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			});
		},
		onShowPrivacy() {
			const linkUrl = encodeURIComponent(UNI_APP.PRIVACY_URL);
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			});
		},
		onQuit() {
			uni.showModal({
				title: '确认退出?',
				success: (res) => {
					if (res.confirm) {
						// 主动退出后不再接收离线通知
						getApp().$vm.removeCid();
						getApp().$vm.exit()
					}
				}
			});
		}
	},
	computed: {
		userInfo() {
			return this.userStore.userInfo;
		}
	}


}
</script>

<style scoped lang="scss">
.mine {
	.content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10rpx;
		padding-right: 30rpx;
		overflow: hidden;
		
		.info-item {
			display: flex;
			align-items: flex-start;
			flex-direction: column;
			padding-left: 40rpx;
			flex: 1;

			.info-text {
				line-height: 1.5;
			}

			.label-text {
				font-size: $im-font-size-small;
				color: $im-text-color-light;

			}

			.content-text {
				font-size: $im-font-size-small;
				color: $im-text-color-light;
			}

			.info-primary {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;

				.info-username {
					font-size: $im-font-size-large;
					font-weight: 600;
				}

				.icon-man {
					color: $im-text-color;
					font-size: $im-font-size-large;
					padding-left: 10rpx;
				}

				.icon-girl {
					color: darkred;
				}
			}
		}

		.info-arrow {
			width: 50rpx;
			font-size: 30rpx;
			position: relative;
			left: 20rpx;
		}
	}
}
</style>