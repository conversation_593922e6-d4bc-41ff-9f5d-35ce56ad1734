<template>
	<view class="page group-info">
		<nav-bar back>群聊信息</nav-bar>
		<view v-if="!group.quit" class="group-members">
			<view class="member-items">
				<view v-for="(member, idx) in groupMembers" :key="idx">
					<view class="member-item" v-if="idx < 7">
						<head-image :id="member.userId" :name="member.showNickName" :url="member.headImage" size="small"
							:online="member.online"></head-image>
						<view class="member-name">
							<text>{{ member.showNickName }}</text>
						</view>
					</view>
				</view>
				<view class="member-item" @click="onInviteMember()">
					<view class="tools-btn">
						<uni-icons class="icon" type="plusempty"  color="#888888"></uni-icons>
					</view>
					<view class="member-name">邀请</view>
				</view>
				<view v-if="isOwner" class="member-item" @click="onMuted()">
					<view class="tools-btn">
						<text class="icon iconfont icon-chat-muted"></text>
					</view>
					<view class="member-name">禁言</view>
				</view>
				<view v-if="isOwner" class="member-item" @click="onUnmuted()">
					<view class="tools-btn">
						<text class="icon iconfont icon-chat-unmuted"></text>
					</view>
					<view class="member-name">解除禁言</view>
				</view>
			</view>
			<view class="member-more" @click="onShowMoreMmeber()">{{ `查看全部群成员${groupMembers.length}人` }}></view>
		</view>
		<bar-group v-if="isOwner">
			<switch-bar title="全员禁言" :checked="group.isMuted" @change="onGroupMutedChange"></switch-bar>
		</bar-group>
		<view class="form">
			<view class="form-item">
				<view class="label">群聊名称</view>
				<view class="value">{{group.name}}</view>
			</view>
			<view class="form-item">
				<view class="label">群主</view>
				<view class="value">{{ownerName}}</view>
			</view>
			<view class="form-item">
				<view class="label">群名备注</view>
				<view class="value">{{group.remarkGroupName}}</view>
			</view>
			<view class="form-item">
				<view class="label">我在本群的昵称</view>
				<view class="value">{{group.showNickName}}</view>
			</view>
			<view v-if="group.notice" class="form-item">
				<view class="label">群公告</view>
			</view>
			<view v-if="group.notice" class="form-item">
				<uni-notice-bar :text="group.notice" />
			</view>
			<view v-if="!group.quit" class="group-edit" @click="onEditGroup()">修改群聊资料 > </view>
		</view>
		<bar-group v-if="!group.quit">
			<btn-bar type="primary" title="发送消息" @tap="onSendMessage()"></btn-bar>
			<btn-bar v-if="!isOwner" type="danger" title="退出群聊" @tap="onQuitGroup()"></btn-bar>
			<btn-bar v-if="isOwner" type="danger" title="解散群聊" @tap="onDissolveGroup()"></btn-bar>
		</bar-group>
		<group-member-selector ref="mutedSelector" :members="groupMembers" :group="group"
			@complete="onMutedComplete"></group-member-selector>
		<group-member-selector ref="unmutedSelector" :members="groupMembers" :group="group"
			@complete="onUnmutedComplete"></group-member-selector>
	</view>
</template>

<script>
export default {
	data() {
		return {
			groupId: null,
			group: {},
			groupMembers: []
		}
	},
	methods: {
		onInviteMember() {
			uni.navigateTo({
				url: `/pages/group/group-invite?id=${this.groupId}`
			})
		},
		onShowMoreMmeber() {
			uni.navigateTo({
				url: `/pages/group/group-member?id=${this.groupId}`
			})
		},
		onEditGroup() {
			uni.navigateTo({
				url: `/pages/group/group-edit?id=${this.groupId}`
			})
		},
		onSendMessage() {
			let chat = {
				type: 'GROUP',
				targetId: this.group.id,
				showName: this.group.showGroupName,
				headImage: this.group.headImage,
			};
			this.chatStore.openChat(chat);
			let chatIdx = this.chatStore.findChatIdx(chat);
			uni.navigateTo({
				url: "/pages/chat/chat-box?chatIdx=" + chatIdx
			})
		},
		onMuted() {
			// 群主不显示
			let hideIds = [this.group.ownerId]
			let checkedIds = this.groupMembers.filter(m => m.isMuted).map(m => m.userId);
			this.$refs.mutedSelector.init(checkedIds, checkedIds, hideIds);
			this.$refs.mutedSelector.open();
		},
		onMutedComplete(userIds) {
			let data = {
				groupId: this.group.id,
				userIds: userIds,
				isMuted: true
			}
			let tip = `您对${userIds.length}位成员进行了禁言`;
			this.sendMemberMuted(data, tip);
		},
		onUnmuted() {
			// 过滤掉未禁言的用户
			const hideIds = this.groupMembers.filter(m => !m.isMuted).map(m => m.userId)
			this.$refs.unmutedSelector.init([], [], hideIds);
			this.$refs.unmutedSelector.open();
		},
		onUnmutedComplete(userIds) {
			let data = {
				groupId: this.group.id,
				userIds: userIds,
				isMuted: false
			}
			let tip = `您解除了${userIds.length}位成员的禁言`;
			this.sendMemberMuted(data, tip);
		},
		onGroupMutedChange(e) {
			let data = {
				id: this.group.id,
				isMuted: e.detail.value
			}
			this.$http({
				url: `/group/muted`,
				method: 'PUT',
				data: data
			})
		},
		onQuitGroup() {
			uni.showModal({
				title: '确认退出?',
				content: `退出群聊后将不再接受群里的消息，确认退出吗?`,
				success: (res) => {
					if (res.cancel)
						return;
					this.$http({
						url: `/group/quit/${this.groupId}`,
						method: 'DELETE'
					}).then(() => {
						uni.showModal({
							title: `退出成功`,
							content: `您已退出群聊'${this.group.name}'`,
							showCancel: false,
							success: () => {
								setTimeout(() => {
									uni.switchTab({
										url: "/pages/group/group"
									});
									this.groupStore.removeGroup(this.groupId);
									this.chatStore.removeGroupChat(this
										.groupId);
								}, 100)
							}
						})
					});
				}
			});
		},
		onDissolveGroup() {
			uni.showModal({
				title: '确认解散?',
				content: `确认要解散群聊'${this.group.name}'吗?`,
				success: (res) => {
					if (res.cancel)
						return;
					this.$http({
						url: `/group/delete/${this.groupId}`,
						method: 'delete'
					}).then(() => {
						uni.showModal({
							title: `解散成功`,
							content: `群聊'${this.group.name}'已解散`,
							showCancel: false,
							success: () => {
								setTimeout(() => {
									uni.switchTab({
										url: "/pages/group/group"
									});
									this.groupStore.removeGroup(this.groupId);
									this.chatStore.removeGroupChat(this
										.groupId);
								}, 100)
							}
						})
					});
				}
			});
		},
		sendMemberMuted(data, tip) {
			this.$http({
				url: "/group/members/muted",
				method: "PUT",
				data: data
			}).then(() => {
				this.loadGroupMembers();
				uni.showToast({
					title: tip,
					icon: 'none'
				})
			})
		},
		loadGroupInfo() {
			this.$http({
				url: `/group/find/${this.groupId}`,
				method: 'GET'
			}).then((group) => {
				this.group = group;
				// 更新聊天页面的群聊信息
				this.chatStore.updateChatFromGroup(group);
				// 更新聊天列表的群聊信息
				this.groupStore.updateGroup(group);
			});
		},
		loadGroupMembers() {
			this.$http({
				url: `/group/members/${this.groupId}`,
				method: "GET"
			}).then((members) => {
				this.groupMembers = members.filter(m => !m.quit);
			})
		}
	},
	computed: {
		ownerName() {
			let member = this.groupMembers.find((m) => m.userId == this.group.ownerId);
			return member && member.showNickName;
		},
		isOwner() {
			return this.group.ownerId == this.userStore.userInfo.id;
		}
	},
	onLoad(options) {
		this.groupId = options.id;
		// 查询群聊信息
		this.loadGroupInfo(options.id);
		// 查询群聊成员
		this.loadGroupMembers(options.id)
	}

}
</script>

<style lang="scss">
.group-info {
	.group-members {
		padding: 30rpx;
		background: white;

		.member-items {
			display: flex;
			flex-wrap: wrap;

			.member-item {
				width: 120rpx;
				display: flex;
				flex-direction: column;
				margin: 8rpx 2rpx;
				position: relative;
				align-items: center;
				padding-right: 5px;
				white-space: nowrap;

				.member-name {
					width: 100%;
					flex: 1;
					overflow: hidden;
					text-align: center;
					white-space: nowrap;
					padding-top: 8rpx;
					font-size: $im-font-size-smaller;
				}

				.tools-btn {
					display: flex;
					justify-content: center;
					align-items: center;
					border: $im-border solid 1rpx;
					border-radius: 10%;
					width: 80rpx;
					height: 80rpx;
					
					.icon {
						font-size: 40rpx !important;
						color: $im-text-color-lighter !important;
					}
				}
			}
		}

		.member-more {
			padding-top: 24rpx;
			text-align: center;
			font-size: $im-font-size-small;
			color: $im-text-color-lighter;
		}
	}

	.form {
		margin-top: 20rpx;

		.form-item {
			padding: 0 40rpx;
			display: flex;
			background: white;
			align-items: center;
			margin-top: 2rpx;

			.label {
				width: 220rpx;
				line-height: 100rpx;
				font-size: $im-font-size;
				white-space: nowrap;
			}

			.value {
				flex: 1;
				text-align: right;
				line-height: 100rpx;
				color: $im-text-color-lighter;
				font-size: $im-font-size-small;
				white-space: nowrap;
				overflow: hidden;
			}
		}

		.group-edit {
			padding: 10rpx 40rpx 30rpx 40rpx;
			text-align: center;
			background: white;
			font-size: $im-font-size-small;
			color: $im-text-color-lighter;
		}
	}
}
</style>