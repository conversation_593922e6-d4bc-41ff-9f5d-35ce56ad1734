<template>
  <view class="page friend-add">
    <nav-bar back>添加好友</nav-bar>
    <view class="nav-bar">
      <view class="nav-search">
        <uni-search-bar v-model="searchText" radius="100" @confirm="onSearch()"
                        @cancel="onCancel()" placeholder="用户名/昵称"></uni-search-bar>
      </view>
    </view>
    <view class="loading-container" v-if="loading">
      <uni-load-more status="loading" iconType="circle" :iconSize="24"></uni-load-more>
    </view>
    <view class="user-items" v-else>
      <scroll-view class="scroll-bar" scroll-with-animation="true" scroll-y="true">
        <view v-for="(user) in sortedUsers" :key="user.id" v-show="user.id != userStore.userInfo.id">
          <view class="user-item">
            <head-image :id="user.id" :name="user.nickName" :online="user.online"
                        :url="user.headImage"></head-image>
            <view class="user-info">
              <view class="user-name">
                <view>{{ user.userName }}</view>
                <uni-tag v-if="user.status == 1" circle type="error" text="已注销" size="small"></uni-tag>
              </view>
              <view class="nick-name">{{ `昵称:${user.nickName}`}}</view>
            </view>
            <view class="user-btns">
              <button type="primary" v-show="!isFriend(user.id)" size="mini"
                      @click.stop="onAddFriend(user)">加为好友</button>
              <button type="default" v-show="isFriend(user.id)" size="mini" disabled>已添加</button>
            </view>
          </view>
        </view>
        <view class="empty-tip" v-if="sortedUsers.length === 0">
          <text>暂无用户数据</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: "",
      users: [],
      loading: false
    }
  },
  computed: {
    sortedUsers() {
      // 对用户进行排序，未添加的显示在前面，已添加的显示在后面
      if (!this.users || this.users.length === 0) return [];

      return [...this.users].sort((a, b) => {
        const aIsFriend = this.isFriend(a.id);
        const bIsFriend = this.isFriend(b.id);

        if (aIsFriend && !bIsFriend) return 1; // a是好友，b不是好友，a排后面
        if (!aIsFriend && bIsFriend) return -1; // a不是好友，b是好友，a排前面
        return 0; // 其他情况保持原顺序
      });
    }
  },
  onLoad() {
    // 页面加载时自动获取用户列表
    this.loadAllUsers()
  },
  methods: {
    onCancel() {
      uni.navigateBack();
    },
    loadAllUsers() {
      this.loading = true;
      this.$http({
        url: "/user/findByName",
        method: "GET",
        data: {
          name: "" // 空字符串请求所有用户
        }
      }).then((data) => {
        this.users = data.filter(user => user.id !== this.userStore.userInfo.id);
        this.loading = false;
      }).catch(error => {
        console.error('加载用户失败:', error);
        uni.showToast({
          title: '加载用户列表失败',
          icon: 'none'
        });
        this.loading = false;
      })
    },
    onSearch() {
      this.loading = true;
      this.$http({
        url: "/user/findByName",
        method: "GET",
        data: {
          name: this.searchText || "" // 如果搜索文本为空，获取所有用户
        }
      }).then((data) => {
        this.users = data.filter(user => user.id !== this.userStore.userInfo.id);
        this.loading = false;
      }).catch(error => {
        console.error('搜索用户失败:', error);
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        });
        this.loading = false;
      })
    },
    onAddFriend(user) {
      this.$http({
        url: "/friend/add?friendId=" + user.id,
        method: "POST"
      }).then((data) => {
        let friend = {
          id: user.id,
          nickName: user.nickName,
          showNickName: user.nickName,
          headImage: user.headImage,
          online: user.online
        }
        this.friendStore.addFriend(friend);
        uni.showToast({
          title: "添加成功，对方已成为您的好友",
          icon: "none"
        });

        // 更新用户列表，触发重新排序
        this.users = [...this.users];
      })
    },
    onShowUserInfo(user) {
      uni.navigateTo({
        url: "/pages/common/user-info?id=" + user.id
      })
    },
    isFriend(userId) {
      let friends = this.friendStore.friends;
      let friend = friends.find((f) => f.id == userId);
      return !!friend;
    }
  }
}
</script>

<style scoped lang="scss">
.friend-add {
  position: relative;
  display: flex;
  flex-direction: column;

  .loading-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .empty-tip {
    padding: 30rpx;
    text-align: center;
    color: $im-text-color-lighter;
  }

  .user-items {
    position: relative;
    flex: 1;
    overflow: hidden;

    .user-item {
      height: 100rpx;
      display: flex;
      margin-bottom: 1rpx;
      position: relative;
      padding: 18rpx 20rpx;
      align-items: center;
      background-color: white;
      white-space: nowrap;

      .user-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-left: 20rpx;
        font-size: $im-font-size;
        white-space: nowrap;
        overflow: hidden;

        .user-name {
          display: flex;
          flex: 1;
          font-size: $im-font-size-large;
          white-space: nowrap;
          overflow: hidden;
          align-items: center;

          .uni-tag {
            text-align: center;
            margin-left: 5rpx;
            padding: 1px 5px;
          }
        }

        .nick-name {
          display: flex;
          font-size: $im-font-size-smaller;
          color: $im-text-color-lighter;
          padding-top: 8rpx;

        }
      }
    }

    .scroll-bar {
      height: 100%;
    }
  }
}
</style>