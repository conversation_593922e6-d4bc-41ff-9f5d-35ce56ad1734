<template>
	<view class="tab-page friend">
		<nav-bar add search @add="onAddNew" @search="onSearch">联系人</nav-bar>
		
		<!-- 顶部切换标签 -->
		<view class="contact-tabs">
			<view class="tab-item" :class="{ active: currentTab === 'friend' }" @click="switchTab('friend')">
				好友
			</view>
			<view class="tab-item" :class="{ active: currentTab === 'group' }" @click="switchTab('group')">
				群聊
			</view>
		</view>
		
		<view class="nav-bar" v-if="showSearch">
			<view class="nav-search">
				<uni-search-bar v-model="searchText" focus="true" radius="100" cancelButton="none"
					:placeholder="currentTab === 'friend' ? '点击搜索好友' : '点击搜索群聊'"></uni-search-bar>
			</view>
		</view>
		
		<!-- 好友列表 -->
		<view v-if="currentTab === 'friend'" class="friend-content">
			<view class="friend-tip" v-if="friends.length == 0">
				温馨提示：您现在还没有任何好友，快点击右上方'+'按钮添加好友吧~
			</view>
			<view class="friend-items" v-else>
				<up-index-list :index-list="friendIdx">
					<template v-for="(friends, i) in friendGroups" >
						<up-index-item>
							<up-index-anchor :text="friendIdx[i] == '*' ? '在线' : friendIdx[i]"></up-index-anchor>
							<view v-for="(friend, idx) in friends" :key="idx">
								<friend-item :friend="friend"></friend-item>
							</view>
						</up-index-item>
					</template>
				</up-index-list>
			</view>
		</view>
		
		<!-- 群聊列表 -->
		<view v-if="currentTab === 'group'" class="group-content">
			<view class="group-tip" v-if="groupStore.groups.length == 0">
				温馨提示：您现在还没有加入任何群聊，点击右上方'+'按钮可以创建群聊哦~
			</view>
			<view class="group-items" v-else>
				<scroll-view class="scroll-bar" scroll-with-animation="true" scroll-y="true">
					<view v-for="group in groupStore.groups" :key="group.id">
						<group-item v-if="!group.quit && group.showGroupName.includes(searchText)"
							:group="group"></group-item>
					</view>
				</scroll-view>
			</view>
		</view>

	</view>
</template>

<script>
import { pinyin } from 'pinyin-pro';

export default {
	data() {
		return {
			showSearch: false,
			searchText: '',
			currentTab: 'friend' // 默认显示好友标签
		}
	},
	methods: {
		// 切换标签
		switchTab(tab) {
			this.currentTab = tab;
			this.searchText = '';
			this.showSearch = false;
		},
		// 根据当前标签添加好友或群聊
		onAddNew() {
			if (this.currentTab === 'friend') {
				this.onAddNewFriends();
			} else {
				this.onCreateNewGroup();
			}
		},
		onAddNewFriends() {
			uni.navigateTo({
				url: "/pages/friend/friend-add"
			})
		},
		onCreateNewGroup() {
			uni.navigateTo({
				url: "/pages/group/group-edit"
			})
		},
		onSearch() {
			this.showSearch = !this.showSearch;
			this.searchText = "";
		},
		firstLetter(strText) {
			// 使用pinyin-pro库将中文转换为拼音
			let pinyinOptions = {
				toneType: 'none', // 无声调
				type: 'normal' // 普通拼音
			};
			let pyText = pinyin(strText, pinyinOptions);
			return pyText[0];
		},
		isEnglish(character) {
			return /^[A-Za-z]+$/.test(character);
		}
	},
	computed: {
		friends() {
			return this.friendStore.friends;
		},
		friendGroupMap() {
			// 按首字母分组
			let groupMap = new Map();
			this.friends.forEach((f) => {
				if (this.searchText && !f.showNickName.includes(this.searchText)) {
					return;
				}
				let letter = this.firstLetter(f.showNickName).toUpperCase();
				// 非英文一律为#组
				if (!this.isEnglish(letter)) {
					letter = "#"
				}
				if (f.online) {
					letter = '*'
				}
				if (groupMap.has(letter)) {
					groupMap.get(letter).push(f);
				} else {
					groupMap.set(letter, [f]);
				}
			})
			// 排序
			let arrayObj = Array.from(groupMap);
			arrayObj.sort((a, b) => {
				// #组在最后面
				if (a[0] == '#' || b[0] == '#') {
					return b[0].localeCompare(a[0])
				}
				return a[0].localeCompare(b[0])
			})
			groupMap = new Map(arrayObj.map(i => [i[0], i[1]]));
			return groupMap;
		},
		friendIdx() {
			return Array.from(this.friendGroupMap.keys());
		},
		friendGroups() {
			return Array.from(this.friendGroupMap.values());
		}
	}
}
</script>

<style lang="scss" scoped>
.friend {
	position: relative;
	display: flex;
	flex-direction: column;

	// 顶部切换标签样式
	.contact-tabs {
		display: flex;
		background-color: white;
		border-bottom: 1px solid #eee;
		
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 30rpx 0;
			color: #666;
			font-size: 32rpx;
			position: relative;
			
			&.active {
				color: #587ff0;
				font-weight: 500;
			}
			
			&.active::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 4rpx;
				background-color: #587ff0;
				border-radius: 2rpx;
			}
		}
	}

	:deep(.u-index-anchor) {
		height: 60rpx !important;
		background-color: unset !important;
		border-bottom: none !important;
	}

	:deep(.u-index-anchor__text) {
		color: $im-text-color !important;
	}

	:deep(.u-index-list__letter__item) {
		width: 48rpx !important;
		height: 48rpx !important;
	}

	:deep(.u-index-list__letter__item__index) {
		font-size: $im-font-size-small !important;
	}

	.friend-tip, .group-tip {
		position: absolute;
		top: 400rpx;
		padding: 50rpx;
		text-align: center;
		line-height: 50rpx;
		color: $im-text-color-lighter;
	}

	.friend-items, .group-items {
		flex: 1;
		padding: 0;
		overflow: hidden;
		position: relative;

		.scroll-bar {
			height: 100%;
		}
	}
	
	.friend-content, .group-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
}
</style>