<template>
  <view class="bom-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">BOM清单</view>
      <view class="nav-right"></view>
    </view>

    <!-- 产品信息 -->
    <view class="product-info-card">
      <view class="info-row">
        <text class="info-label">产品名称:</text>
        <text class="info-value">{{ productName || '-' }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">款式名称:</text>
        <text class="info-value">{{ styleName || '-' }}</text>
      </view>
      <view class="step-product-btn product-btn" @click="handleReceiveProduct">
        <text>领取半成品</text>
      </view>
    </view>

    <!-- BOM清单内容 -->
    <view class="bom-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="spinner" size="40"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <view class="error-icon">⚠️</view>
        <text class="error-text">{{ error }}</text>
        <view class="retry-button" @click="loadBomData">
          <text class="retry-text">重新加载</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && bomList.length === 0" class="empty-container">
        <u-empty
            mode="data"
            text="暂无BOM数据"
            textColor="#999"
            iconSize="120"
        ></u-empty>
      </view>

      <!-- BOM列表 -->
      <view v-else class="bom-list">
        <view class="list-header">
          <text class="header-title">物料清单 ({{ bomList.length }}项)</text>
        </view>

        <view class="bom-items">
          <view
              v-for="(item, index) in bomList"
              :key="`${item.materialCode}-${index}`"
              class="bom-item"
          >
            <view class="item-header">
              <view class="material-info">
                <text class="material-name">{{ item.material || '未知物料' }}</text>
                <text class="material-code">编码: {{ item.materialCode || '-' }}</text>
              </view>
              <view class="board-type-badge" :class="getBoardTypeClass(item.boardType)">
                <text class="board-type-text">{{ item.boardType || '-' }}</text>
              </view>
              <view class="step-action-btn material-btn" @click="handleReceiveMaterial">
                <text>领料</text>
              </view>
            </view>

            <view class="item-details">
              <view class="detail-row">
                <text class="detail-label">需求数量:</text>
                <text class="detail-value quantity-value">{{ item.quantity || 0 }} {{ item.unit || '个' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">库存数量:</text>
                <text class="detail-value stock-value" :class="getStockStatusClass(item.currentStock, item.minStockQuantity)">
                  {{ item.currentStock || 0 }} {{ item.unit || '个' }}
                </text>
              </view>
              <view class="detail-row">
                <text class="detail-label">安全库存:</text>
                <text class="detail-value safety-stock-value">{{ item.minStockQuantity || 0 }} {{ item.unit || '个' }}</text>
              </view>
              <view class="detail-row" v-if="item.remark">
                <text class="detail-label">备注:</text>
                <text class="detail-value">{{ item.remark }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getBomByModelAndStyle, showMessage } from '@/api/workOrders.js'

export default {
  name: "BOM",
  data() {
    return {
      // 页面参数
      productName: '',
      styleName: '',

      // BOM数据
      bomList: [],

      // 状态管理
      loading: false,
      error: ''
    }
  },

  onLoad(options) {
    // 接收页面参数
    this.productName = decodeURIComponent(options.productName || '')
    this.styleName = decodeURIComponent(options.styleName || '')

    console.log('BOM页面参数:', {
      productName: this.productName,
      styleName: this.styleName
    })

    // 加载BOM数据
    this.loadBomData()
  },

  methods: {
    /** 返回上一页 */
    goBack() {
      uni.navigateBack()
    },

    /** 加载BOM数据 */
    async loadBomData() {
      // 检查必要参数
      if (!this.productName) {
        this.error = '产品名称不能为空'
        return
      }

      this.loading = true
      this.error = ''

      try {
        console.log('开始获取BOM数据，参数:', {
          model: this.productName,
          style: this.styleName
        })

        const response = await getBomByModelAndStyle(this.productName, this.styleName)
        console.log('BOM API响应:', response)

        // 判断API响应是否成功
        if (response && (response.code === 0 || response.code === 200 || response.code === "200")) {
          const bomData = response.data || []

          // 处理BOM数据
          this.bomList = Array.isArray(bomData) ? bomData : []

          console.log('BOM数据处理完成，数量:', this.bomList.length)

          if (this.bomList.length === 0) {
            console.log('未找到BOM数据')
          }
        } else {
          console.error('BOM API响应失败，code:', response?.code, 'message:', response?.message)
          this.error = response?.message || '获取BOM数据失败'
          showMessage(this.error, 'error')
        }
      } catch (error) {
        console.error('BOM API调用异常:', error)
        this.error = '获取BOM数据失败: ' + (error.message || '网络错误')
        showMessage(this.error, 'error')
      } finally {
        this.loading = false
      }
    },

    /** 获取板型样式类 */
    getBoardTypeClass(boardType) {
      const typeMap = {
        '上板': 'board-type-upper',
        '下板': 'board-type-lower'
      }
      return typeMap[boardType] || 'board-type-default'
    },

    /** 获取库存状态样式类 */
    getStockStatusClass(currentStock, minStock) {
      const current = Number(currentStock) || 0
      const min = Number(minStock) || 0

      if (current <= 0) {
        return 'stock-empty'
      } else if (current <= min) {
        return 'stock-low'
      } else {
        return 'stock-normal'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.step-product-btn.product-btn {
  width: 150rpx;         /* 控制宽度 */
  height:50rpx;
  line-height: 50rpx;
  margin-left: 530rpx;
  margin-top: -50rpx;
  background-color: #4caf50;
  color: white;
  border-radius: 16rpx;
  text-align: center;
  font-size: 20rpx;
}
.step-action-btn.material-btn {
  padding: 12rpx 32rpx;
  margin-left: 10rpx;
  background-color: #4caf50;
  color: white;
  border-radius: 16rpx;
  text-align: center;
  font-size: 20rpx;
}

.bom-page {
  margin-top: 45rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e8e8e8;
  padding: 0 20rpx;
  box-sizing: border-box;

  .nav-left {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .back-icon {
      font-size: 36rpx;
      color: #007aff;
      font-weight: bold;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .nav-right {
    width: 80rpx;
  }
}

/* 产品信息卡片 */
.product-info-card {
  background-color: #fff;
  margin: 20rpx 16rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      flex: 1;
    }
  }
}

/* BOM内容区域 */
.bom-content {
  flex: 1;
  padding: 0 16rpx 20rpx;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .error-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .error-text {
    font-size: 28rpx;
    color: #f56c6c;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .retry-button {
    padding: 16rpx 32rpx;
    background-color: #007aff;
    border-radius: 8rpx;

    .retry-text {
      font-size: 28rpx;
      color: #fff;
    }
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  padding: 100rpx 0;
}

/* BOM列表 */
.bom-list {
  .list-header {
    background-color: #fff;
    padding: 20rpx 24rpx;
    border-radius: 16rpx 16rpx 0 0;
    border-bottom: 1rpx solid #f0f0f0;

    .header-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .bom-items {
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;
    overflow: hidden;

    .bom-item {
      padding: 20rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12rpx;

        .material-info {
          flex: 1;
          min-width: 0;

          .material-name {
            display: block;
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 6rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .material-code {
            font-size: 24rpx;
            color: #666;
          }
        }

        .board-type-badge {
          padding: 6rpx 12rpx;
          border-radius: 8rpx;
          flex-shrink: 0;
          margin-left: 20rpx;

          .board-type-text {
            font-size: 22rpx;
            color: #fff;
            font-weight: 500;
          }

          &.board-type-upper {
            background-color: #409eff;
          }

          &.board-type-lower {
            background-color: #67c23a;
          }

          &.board-type-default {
            background-color: #909399;
          }
        }
      }

      .item-details {
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            font-size: 24rpx;
            color: #999;
            width: 120rpx;
            flex-shrink: 0;
          }

          .detail-value {
            font-size: 24rpx;
            color: #666;
            flex: 1;
            word-break: break-all;

            &.quantity-value {
              color: #007aff;
              font-weight: 600;
            }

            &.stock-value {
              font-weight: 600;

              &.stock-normal {
                color: #67c23a;
              }

              &.stock-low {
                color: #e6a23c;
              }

              &.stock-empty {
                color: #f56c6c;
              }
            }

            &.safety-stock-value {
              color: #909399;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

/* 响应式优化 */
@media screen and (max-width: 750rpx) {
  .bom-page {
    .product-info-card {
      margin: 16rpx 12rpx;
      padding: 20rpx;

      .info-row {
        .info-label {
          width: 140rpx;
          font-size: 26rpx;
        }

        .info-value {
          font-size: 26rpx;
        }
      }
    }

    .bom-content {
      padding: 0 12rpx 20rpx;
    }

    .bom-list {
      .list-header {
        padding: 16rpx 20rpx;

        .header-title {
          font-size: 28rpx;
        }
      }

      .bom-items {
        .bom-item {
          padding: 16rpx 20rpx;

          .item-header {
            .material-info {
              .material-name {
                font-size: 26rpx;
              }

              .material-code {
                font-size: 22rpx;
              }
            }

            .board-type-badge {
              padding: 4rpx 8rpx;
              margin-left: 16rpx;

              .board-type-text {
                font-size: 20rpx;
              }
            }
          }

          .item-details {
            .detail-row {
              .detail-label {
                width: 100rpx;
                font-size: 22rpx;
              }

              .detail-value {
                font-size: 22rpx;
              }
            }
          }
        }
      }
    }
  }
}
</style>