<template>
  <view class="bom-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">BOM清单</view>
      <view class="nav-right"></view>
    </view>

    <!-- 产品信息 -->
    <view class="product-info-card">
      <view class="info-row">
        <text class="info-label">产品名称:</text>
        <text class="info-value">{{ productName || '-' }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">款式名称:</text>
        <text class="info-value">{{ styleName || '-' }}</text>
      </view>
      <view v-if="showMaterialPickingButton" class="info-row">
        <text class="info-label">{{ productBoardType }}型</text>
        <text class="info-label">数量:</text>
        <text class="info-value">{{ quantity || '-' }}</text>
      </view>
      <!-- 只有通过领料按钮进入时才显示领料按钮 -->
      <view v-if="showMaterialPickingButton" class="step-product-btn product-btn" @click="handleReceiveProduct">
        <text>领取半成品</text>
      </view>
    </view>

    <!-- BOM清单内容 -->
    <view class="bom-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="spinner" size="40"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <view class="error-icon">⚠️</view>
        <text class="error-text">{{ error }}</text>
        <view class="retry-button" @click="loadBomData">
          <text class="retry-text">重新加载</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && bomList.length === 0" class="empty-container">
        <u-empty
            mode="data"
            text="暂无BOM数据"
            textColor="#999"
            iconSize="120"
        ></u-empty>
      </view>

      <!-- BOM列表 -->
      <view v-else class="bom-list">
        <view class="list-header">
          <text class="header-title">物料清单 ({{ bomList.length }}项)</text>
        </view>

        <view class="bom-items">
          <view
              v-for="(item, index) in bomList"
              :key="`${item.materialCode}-${index}`"
              class="bom-item"
          >
            <view class="item-header">
              <view class="material-info">
                <text class="material-name">{{ item.material || '未知物料' }}</text>
                <text class="material-code">编码: {{ item.materialCode || '-' }}</text>
              </view>
              <view class="board-type-badge" :class="getBoardTypeClass(item.boardType)">
                <text class="board-type-text">{{ item.boardType || '-' }}</text>
              </view>
              <view v-if="showMaterialPickingButton" class="step-action-btn material-btn" @click="handleReceiveMaterial">
                <text>领料</text>
              </view>
            </view>

            <view class="item-details">
              <view class="detail-row">
                <text class="detail-label">需求数量:</text>
                <text class="detail-value quantity-value">{{ item.quantity || 0 }} {{ item.unit || '个' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">库存数量:</text>
                <text class="detail-value stock-value" :class="getStockStatusClass(item.currentStock, item.minStockQuantity)">
                  {{ item.currentStock || 0 }} {{ item.unit || '个' }}
                </text>
              </view>
              <view class="detail-row">
                <text class="detail-label">安全库存:</text>
                <text class="detail-value safety-stock-value">{{ item.minStockQuantity || 0 }} {{ item.unit || '个' }}</text>
              </view>
              <view class="detail-row" v-if="item.remark">
                <text class="detail-label">备注:</text>
                <text class="detail-value">{{ item.remark }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getBomByModelAndStyle, showMessage, outboundSemiFinishedProduct, getStepTaskDetail } from '@/api/workOrders.js'

export default {
  name: "BOM",
  data() {
    return {
      // 页面参数
      productName: '',
      styleName: '',
      productBoardType: '', // 产品板型
      stepTaskId: '', // 工序任务ID
      quantity: '', // 产品数量
      source: '', // 来源标识

      // BOM数据
      bomList: [],

      // 状态管理
      loading: false,
      error: ''
    }
  },

  computed: {
    // 是否显示领料按钮（只有通过领料按钮进入时才显示）
    showMaterialPickingButton() {
      return this.source === 'material_picking'
    }
  },

  onLoad(options) {
    // 接收页面参数
    this.productName = decodeURIComponent(options.productName || '')
    this.styleName = decodeURIComponent(options.styleName || '')
    this.productBoardType = decodeURIComponent(options.boardType || '')
    this.stepTaskId = options.stepTaskId || ''
    this.quantity = options.quantity || ''
    this.source = options.source || ''

    console.log('BOM页面参数:', {
      productName: this.productName,
      styleName: this.styleName,
      productBoardType: this.productBoardType,
      stepTaskId: this.stepTaskId,
      quantity: this.quantity,
      source: this.source,
      showMaterialPickingButton: this.showMaterialPickingButton
    })

    // 加载BOM数据
    this.loadBomData()
  },

  methods: {
    /** 返回上一页 */
    goBack() {
      uni.navigateBack()
    },

    /** 加载BOM数据 */
    async loadBomData() {
      // 检查必要参数
      if (!this.productName) {
        this.error = '产品名称不能为空'
        return
      }

      this.loading = true
      this.error = ''

      try {
        console.log('开始获取BOM数据，参数:', {
          model: this.productName,
          style: this.styleName
        })

        const response = await getBomByModelAndStyle(this.productName, this.styleName)
        console.log('BOM API响应:', response)

        // 判断API响应是否成功
        if (response && (response.code === 0 || response.code === 200 || response.code === "200")) {
          const bomData = response.data || []

          // 处理BOM数据
          this.bomList = Array.isArray(bomData) ? bomData : []

          console.log('BOM数据处理完成，数量:', this.bomList.length)

          if (this.bomList.length === 0) {
            console.log('未找到BOM数据')
          }
        } else {
          console.error('BOM API响应失败，code:', response?.code, 'message:', response?.message)
          this.error = response?.message || '获取BOM数据失败'
          showMessage(this.error, 'error')
        }
      } catch (error) {
        console.error('BOM API调用异常:', error)
        this.error = '获取BOM数据失败: ' + (error.message || '网络错误')
        showMessage(this.error, 'error')
      } finally {
        this.loading = false
      }
    },

    /** 获取板型样式类 */
    getBoardTypeClass(boardType) {
      const typeMap = {
        '上板': 'board-type-upper',
        '下板': 'board-type-lower'
      }
      return typeMap[boardType] || 'board-type-default'
    },

    /** 获取库存状态样式类 */
    getStockStatusClass(currentStock, minStock) {
      const current = Number(currentStock) || 0
      const min = Number(minStock) || 0

      if (current <= 0) {
        return 'stock-empty'
      } else if (current <= min) {
        return 'stock-low'
      } else {
        return 'stock-normal'
      }
    },

    /** 处理领取半成品按钮点击 */
    handleReceiveProduct() {
      // 检查必要参数
      if (!this.stepTaskId) {
        uni.showToast({
          title: '工序任务ID不存在',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (!this.quantity) {
        uni.showToast({
          title: '产品数量不存在',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (!this.productName) {
        uni.showToast({
          title: '产品名称不存在',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (!this.productBoardType) {
        uni.showToast({
          title: '产品板型不存在',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 根据产品板型显示选择对话框
      this.showBoardTypeSelection()
    },

    /** 显示板型选择对话框 */
    async showBoardTypeSelection() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '获取领取信息...'
        })

        // 先调用 getStepTaskDetail API 获取已领取数量信息
        console.log('开始获取工序任务详细信息，stepTaskId:', this.stepTaskId)
        const response = await getStepTaskDetail(this.stepTaskId)
        console.log('工序任务详细信息API响应:', response)

        uni.hideLoading()

        let onBoardCount = 0
        let downBoardCount = 0
        let oneBoardCount = 0

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          const stepTaskDetail = response.data
          onBoardCount = stepTaskDetail?.onBoard || 0
          downBoardCount = stepTaskDetail?.downBoard || 0
          oneBoardCount = stepTaskDetail?.oneBoard || 0

          console.log('获取到的已领取数量:', {
            onBoard: onBoardCount,
            downBoard: downBoardCount,
            oneBoard: oneBoardCount
          })
        } else {
          console.warn('获取工序任务详细信息失败或响应异常:', response)
          // 即使获取失败，也继续显示选择对话框，但不显示已领取数量
        }

        // 构建板型选择选项，包含已领取数量信息
        this.buildBoardTypeOptionsWithQuantity(onBoardCount, downBoardCount, oneBoardCount)

      } catch (error) {
        uni.hideLoading()
        console.error('获取工序任务详细信息失败:', error)

        // 即使API调用失败，也继续显示选择对话框，但不显示已领取数量
        this.buildBoardTypeOptionsWithQuantity(0, 0, 0)
      }
    },

    /** 构建包含已领取数量信息的板型选择选项 */
    buildBoardTypeOptionsWithQuantity(onBoardCount, downBoardCount, oneBoardCount) {
      let itemList = []
      let boardTypeMapping = [] // 用于映射选项索引到板型

      const targetQuantity = parseInt(this.quantity) || 0

      // 根据产品板型决定显示的选项
      if (this.productBoardType === '单板') {
        // 单板产品只显示单板选项
        const displayText = oneBoardCount > 0
          ? `单板领料 (已领取: ${oneBoardCount})`
          : '单板领料'

        // 检查是否已完成
        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${displayText} - 已完成`)
        } else {
          itemList.push(displayText)
        }
        boardTypeMapping.push('单板')

      } else if (this.productBoardType === '上下板') {
        // 上下板产品显示上板和下板选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板领料 (已领取: ${onBoardCount})`
          : '上板领料'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板领料 (已领取: ${downBoardCount})`
          : '下板领料'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

      } else {
        // 如果板型不明确，显示所有选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板领料 (已领取: ${onBoardCount})`
          : '上板领料'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板领料 (已领取: ${downBoardCount})`
          : '下板领料'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

        // 单板选项
        const singleDisplayText = oneBoardCount > 0
          ? `单板领料 (已领取: ${oneBoardCount})`
          : '单板领料'

        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${singleDisplayText} - 已完成`)
        } else {
          itemList.push(singleDisplayText)
        }
        boardTypeMapping.push('单板')
      }

      console.log('显示板型选择对话框:', {
        productBoardType: this.productBoardType,
        targetQuantity: targetQuantity,
        itemList: itemList,
        boardTypeMapping: boardTypeMapping,
        quantities: { onBoardCount, downBoardCount, oneBoardCount }
      })

      // 检查是否所有相关板型都已完成
      const isAllCompleted = this.checkIfAllCompleted(onBoardCount, downBoardCount, oneBoardCount, targetQuantity)

      if (isAllCompleted) {
        uni.showModal({
          title: '领取状态',
          content: `该产品已领料完成！\n\n订单数量：${targetQuantity}\n已领取情况：\n${this.buildCompletionSummary(onBoardCount, downBoardCount, oneBoardCount)}`,
          showCancel: false,
          confirmText: '确定'
        })
        return
      }

      // 显示板型选择对话框
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedBoardType = boardTypeMapping[res.tapIndex]
          console.log('用户选择的板型:', selectedBoardType)

          // 选择完成后进入扫码流程
          this.startScanCode(selectedBoardType)
        },
        fail: (err) => {
          console.log('用户取消选择板型')
        }
      })
    },

    /** 检查是否所有相关板型都已完成 */
    checkIfAllCompleted(onBoardCount, downBoardCount, oneBoardCount, targetQuantity) {
      if (this.productBoardType === '单板') {
        // 单板产品：单板数量达到目标数量即完成
        return oneBoardCount >= targetQuantity
      } else if (this.productBoardType === '上下板') {
        // 上下板产品：上板和下板数量都达到目标数量才完成
        return onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
      } else {
        // 板型不明确：任意一种方式完成即可
        const isSingleCompleted = oneBoardCount >= targetQuantity
        const isUpperLowerCompleted = onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
        return isSingleCompleted || isUpperLowerCompleted
      }
    },

    /** 构建完成情况摘要 */
    buildCompletionSummary(onBoardCount, downBoardCount, oneBoardCount) {
      const summary = []

      if (onBoardCount > 0) {
        summary.push(`上板：${onBoardCount}`)
      }
      if (downBoardCount > 0) {
        summary.push(`下板：${downBoardCount}`)
      }
      if (oneBoardCount > 0) {
        summary.push(`单板：${oneBoardCount}`)
      }

      return summary.length > 0 ? summary.join('\n') : '暂无领取记录'
    },

    /** 开始扫码流程 */
    startScanCode(boardType) {
      console.log('开始扫码，板型:', boardType)

      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          console.log('扫码成功，结果:', res.result)
          // 扫码成功，处理扫码结果
          this.processScanResult(res.result, boardType)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          // 扫码失败处理
          this.handleScanError(err)
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData, boardType) {
      try {
        console.log('处理扫码结果:', {
          scanData: scanData,
          boardType: boardType,
          productName: this.productName,
          quantity: this.quantity
        })

        // 解析扫码数据，提取purchase_no
        const purchaseNo = this.extractPurchaseNo(scanData)
        console.log('解析出的purchaseNo:', purchaseNo)

        if (!purchaseNo) {
          uni.showToast({
            title: `扫码数据格式错误，无法解析采购单号。\n扫码内容：${scanData}`,
            icon: 'none',
            duration: 5000
          })
          return
        }

        // 构建确认内容
        const content = `确定要领取 ${this.productName} 的${boardType}半成品吗？\n数量：${this.quantity}\n采购单号：${purchaseNo}`

        // 显示扫码结果确认弹窗
        uni.showModal({
          title: '扫码结果确认',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户确认，执行半成品出库操作
              this.performSemiFinishedOutbound(purchaseNo, boardType)
            }
          }
        })
      } catch (error) {
        console.error('处理扫码结果失败:', error)
        uni.showToast({
          title: '处理扫码结果失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        })
      }
    },

    /** 从扫码数据中提取purchase_no */
    extractPurchaseNo(scanData) {
      try {
        // 扫码数据格式示例：purchase_no:PO20250729142 或 label_type:purchase|purchase_no:PO20250729142
        if (!scanData || typeof scanData !== 'string') {
          return null
        }

        // 使用正则表达式提取purchase_no的值
        const purchaseNoMatch = scanData.match(/purchase_no:([^|]+)/i)

        if (purchaseNoMatch && purchaseNoMatch[1]) {
          const purchaseNo = purchaseNoMatch[1].trim()
          console.log('正则匹配解析出的purchase_no:', purchaseNo)
          return purchaseNo
        }

        // 如果正则匹配失败，尝试手动解析
        const parts = scanData.split('|')
        for (const part of parts) {
          if (part.includes('purchase_no:')) {
            const purchaseNo = part.split('purchase_no:')[1]?.trim()
            if (purchaseNo) {
              console.log('手动解析出的purchase_no:', purchaseNo)
              return purchaseNo
            }
          }
        }

        // 如果都失败了，检查是否整个字符串就是采购单号
        if (scanData.startsWith('PO') && scanData.length > 5) {
          console.log('直接使用扫码数据作为purchase_no:', scanData)
          return scanData
        }

        return null
      } catch (error) {
        console.error('解析purchase_no失败:', error)
        return null
      }
    },
    /** 执行半成品出库操作 */
    async performSemiFinishedOutbound(purchaseNo, boardType) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '领料中...'
        })

        // 准备API参数
        const formData = {
          quantity: this.quantity,           // 从页面参数获取的数量
          purchaseNo: purchaseNo,           // 从扫码结果解析的采购单号
          itemName: this.productName,       // 从页面参数获取的产品名称
          boardType: boardType,             // 用户选择的板型（"上板"/"下板"/"单板"）
          stepTaskId: this.stepTaskId       // 从页面参数获取的工序任务ID
        }

        console.log('半成品出库API调用参数:', formData)

        // 调用半成品出库API
        const response = await outboundSemiFinishedProduct(formData)

        console.log('半成品出库API响应:', response)
        console.log('响应码:', response.code)
        console.log('响应消息:', response.message)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && (response.code === 0 || response.code === 200)) {
          // 显示成功提示
          uni.showToast({
            title: '半成品领取成功',
            icon: 'success',
            duration: 2000
          })

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)

        } else {
          console.error('半成品出库失败详情:', {
            code: response?.code,
            message: response?.message,
            data: response?.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response?.message || response?.msg || '半成品领取失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
        }

      } catch (error) {
        uni.hideLoading()

        console.error('半成品出库操作异常:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
      }
    },

    /** 处理扫码失败 */
    handleScanError(error) {
      console.error('扫码失败:', error)

      let errorMessage = '扫码失败'

      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '用户取消扫码'
        } else if (error.errMsg.includes('fail')) {
          errorMessage = '扫码功能异常，请重试'
        }
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    },

    /** 处理物料领料按钮点击 */
    handleReceiveMaterial() {
      uni.showToast({
        title: '物料领料功能待开发',
        icon: 'none',
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.step-product-btn.product-btn {
  width: 150rpx;         /* 控制宽度 */
  height:50rpx;
  line-height: 50rpx;
  margin-left: 530rpx;
  margin-top: -50rpx;
  background-color: #4caf50;
  color: white;
  border-radius: 16rpx;
  text-align: center;
  font-size: 20rpx;
}
.step-action-btn.material-btn {
  padding: 12rpx 32rpx;
  margin-left: 10rpx;
  background-color: #4caf50;
  color: white;
  border-radius: 16rpx;
  text-align: center;
  font-size: 20rpx;
}

.bom-page {
  margin-top: 45rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e8e8e8;
  padding: 0 20rpx;
  box-sizing: border-box;

  .nav-left {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .back-icon {
      font-size: 36rpx;
      color: #007aff;
      font-weight: bold;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .nav-right {
    width: 80rpx;
  }
}

/* 产品信息卡片 */
.product-info-card {
  background-color: #fff;
  margin: 20rpx 16rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      flex: 1;
    }
  }
}

/* BOM内容区域 */
.bom-content {
  flex: 1;
  padding: 0 16rpx 20rpx;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .error-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .error-text {
    font-size: 28rpx;
    color: #f56c6c;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .retry-button {
    padding: 16rpx 32rpx;
    background-color: #007aff;
    border-radius: 8rpx;

    .retry-text {
      font-size: 28rpx;
      color: #fff;
    }
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  padding: 100rpx 0;
}

/* BOM列表 */
.bom-list {
  .list-header {
    background-color: #fff;
    padding: 20rpx 24rpx;
    border-radius: 16rpx 16rpx 0 0;
    border-bottom: 1rpx solid #f0f0f0;

    .header-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .bom-items {
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;
    overflow: hidden;

    .bom-item {
      padding: 20rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12rpx;

        .material-info {
          flex: 1;
          min-width: 0;

          .material-name {
            display: block;
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 6rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .material-code {
            font-size: 24rpx;
            color: #666;
          }
        }

        .board-type-badge {
          padding: 6rpx 12rpx;
          border-radius: 8rpx;
          flex-shrink: 0;
          margin-left: 20rpx;

          .board-type-text {
            font-size: 22rpx;
            color: #fff;
            font-weight: 500;
          }

          &.board-type-upper {
            background-color: #409eff;
          }

          &.board-type-lower {
            background-color: #67c23a;
          }

          &.board-type-default {
            background-color: #909399;
          }
        }
      }

      .item-details {
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            font-size: 24rpx;
            color: #999;
            width: 120rpx;
            flex-shrink: 0;
          }

          .detail-value {
            font-size: 24rpx;
            color: #666;
            flex: 1;
            word-break: break-all;

            &.quantity-value {
              color: #007aff;
              font-weight: 600;
            }

            &.stock-value {
              font-weight: 600;

              &.stock-normal {
                color: #67c23a;
              }

              &.stock-low {
                color: #e6a23c;
              }

              &.stock-empty {
                color: #f56c6c;
              }
            }

            &.safety-stock-value {
              color: #909399;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

/* 响应式优化 */
@media screen and (max-width: 750rpx) {
  .bom-page {
    .product-info-card {
      margin: 16rpx 12rpx;
      padding: 20rpx;

      .info-row {
        .info-label {
          width: 140rpx;
          font-size: 26rpx;
        }

        .info-value {
          font-size: 26rpx;
        }
      }
    }

    .bom-content {
      padding: 0 12rpx 20rpx;
    }

    .bom-list {
      .list-header {
        padding: 16rpx 20rpx;

        .header-title {
          font-size: 28rpx;
        }
      }

      .bom-items {
        .bom-item {
          padding: 16rpx 20rpx;

          .item-header {
            .material-info {
              .material-name {
                font-size: 26rpx;
              }

              .material-code {
                font-size: 22rpx;
              }
            }

            .board-type-badge {
              padding: 4rpx 8rpx;
              margin-left: 16rpx;

              .board-type-text {
                font-size: 20rpx;
              }
            }
          }

          .item-details {
            .detail-row {
              .detail-label {
                width: 100rpx;
                font-size: 22rpx;
              }

              .detail-value {
                font-size: 22rpx;
              }
            }
          }
        }
      }
    }
  }
}
</style>