<template>
  <view class="defect-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">提交缺陷</view>
    </view>

    <!-- 工序信息 -->
    <view class="step-info-card">
      <view class="step-name">
        <text class="label">工序步骤名</text>
        <text class="value">{{ stepName }}</text>
      </view>
<!--      <view class="step-id">-->
<!--        <text class="label">工序ID:</text>-->
<!--        <text class="value">{{ stepId }}</text>-->
<!--      </view>-->
    </view>

    <!-- 缺陷列表 -->
    <view class="defect-list-container">
      <view class="section-title">可选缺陷类型</view>

      <view v-if="loading" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="error" class="error-container">
        <text class="error-text">{{ error }}</text>
        <view class="retry-button" @click="retryLoad">重试</view>
      </view>

      <view v-else-if="defectiveNames.length === 0" class="empty-container">
        <text class="empty-text">暂无可选缺陷类型</text>
      </view>

      <view v-else class="defect-list">
        <view
          v-for="(defect, index) in defectiveNames"
          :key="index"
          class="defect-item"
        >
          <view class="defect-name">{{ defect }}</view>
          <view class="defect-count">
            <view class="count-control minus" @click="decreaseCount(index)">-</view>
            <text class="count-value">{{ defectCounts[index] || 0 }}</text>
            <view class="count-control plus" @click="increaseCount(index)">+</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <view
        class="submit-button"
        :class="{ 'disabled': submitting || defectiveNames.length === 0 }"
        @click="submitDefects"
      >
        <text v-if="submitting">提交中...</text>
        <text v-else>提交缺陷</text>
      </view>
    </view>
  </view>
</template>

<script>
import { addDefects, showMessage, getProcessRouteInfo, getStepTaskDetail } from '@/api/workOrders.js'

export default {
  name: "defect",
  data() {
    return {
      // 页面参数
      stepId: null,
      stepTaskId: null,
      stepName: null,
      defectiveNames: [],

      // 缺陷数量
      defectCounts: {},

      // 状态管理
      loading: false,
      error: '',
      submitting: false
    }
  },

  computed: {
    // 是否有选择的缺陷
    hasSelectedDefects() {
      return Object.values(this.defectCounts).some(count => count > 0)
    },

    // 获取所有缺陷数据的总结信息
    defectSummary() {
      const totalTypes = this.defectiveNames.length
      const selectedTypes = Object.values(this.defectCounts).filter(count => count > 0).length
      const totalCount = Object.values(this.defectCounts).reduce((sum, count) => sum + count, 0)

      return {
        totalTypes,
        selectedTypes,
        totalCount
      }
    }
  },

  onLoad(options) {
    // 接收stepId参数
    this.stepId = options.stepId || null
    this.stepTaskId = options.stepTaskId || null

    // console.log('缺陷页面参数:', {
    //   stepId: this.stepId,
    //   stepTaskId: this.stepTaskId
    // })

    // 检查必要参数
    if (!this.stepId) {
      this.error = '工序ID不能为空'
      return
    }

    if (!this.stepTaskId) {
      this.error = '工序任务ID不能为空'
      return
    }

    // 调用API获取工序详细信息
    this.loadStepDetails()
  },

  methods: {
    /** 返回上一页 */
    goBack() {
      uni.navigateBack()
    },

    /** 加载工序详细信息 */
    async loadStepDetails() {
      try {
        this.loading = true
        this.error = ''

       // console.log('开始调用getProcessRouteInfo API，stepId:', this.stepId)

        // 调用根据工序ID查询工序详细信息API
        const response = await getProcessRouteInfo(this.stepId)
       // console.log('查询工序详细信息API响应:', response)

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          let defectiveNames = response.data?.defectiveNames || []

          // 设置工序名称
          this.stepName = response.data?.procedureName || ''
          //console.log('获取到的工序名称:', this.stepName)

         // console.log('获取到的缺陷信息:', defectiveNames)

          // 处理defectiveNames，如果是字符串则转换为数组
          if (typeof defectiveNames === 'string') {
            // 如果是逗号分隔的字符串，转换为数组
            defectiveNames = defectiveNames.split(',').map(name => name.trim()).filter(name => name)
            //console.log('转换后的缺陷信息数组:', defectiveNames)
          } else if (!Array.isArray(defectiveNames)) {
            // 如果既不是字符串也不是数组，设置为空数组
            defectiveNames = []
           // console.log('defectiveNames格式异常，设置为空数组')
          }

          // 设置缺陷名称列表
          this.defectiveNames = defectiveNames

          // 获取工序详细信息成功后，加载已有的缺陷信息
          await this.loadDefectInfo()
        } else {
          this.error = response?.message || '获取工序详细信息失败'
          showMessage(this.error, 'error')
        }
      } catch (error) {
        //console.error('加载工序详细信息失败:', error)
        this.error = '加载工序详细信息失败: ' + (error.message || '网络错误')
        showMessage(this.error, 'error')
      } finally {
        this.loading = false
      }
    },

    /** 加载已有的缺陷信息 */
    async loadDefectInfo() {
      try {
        //console.log('开始调用getStepTaskDetail API，stepTaskId:', this.stepTaskId)

        // 调用根据stepTaskId查看详细信息API
        const response = await getStepTaskDetail(this.stepTaskId)
        //console.log('查询工序任务详细信息API响应:', response)

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          const defectInfo = response.data?.defectInfo || {}
          //console.log('获取到的已有缺陷信息:', defectInfo)

          // 初始化缺陷数量，如果有已有数据则使用已有数据
          this.initDefectCounts(defectInfo)
        } else {
         // console.log('未获取到缺陷信息或响应异常，使用默认初始化')
          // 如果没有已有数据或响应异常，则使用默认初始化
          this.initDefectCounts()
        }
      } catch (error) {
       // console.error('加载缺陷信息失败:', error)
       // console.log('加载缺陷信息失败，使用默认初始化')
        // 如果API调用失败，则使用默认初始化
        this.initDefectCounts()
      }
    },

    /** 初始化缺陷数量 */
    initDefectCounts(existingDefectInfo = {}) {
      const counts = {}
      this.defectiveNames.forEach((defectName, index) => {
        // 如果存在已有的缺陷信息，则使用已有数据，否则默认为0
        counts[index] = existingDefectInfo[defectName] || 0
      })
      this.defectCounts = counts
      //console.log('初始化缺陷数量完成:', counts)
    },

    /** 增加缺陷数量 */
    increaseCount(index) {
      const currentCount = this.defectCounts[index] || 0
      this.$set(this.defectCounts, index, currentCount + 1)
    },

    /** 减少缺陷数量 */
    decreaseCount(index) {
      const currentCount = this.defectCounts[index] || 0
      if (currentCount > 0) {
        this.$set(this.defectCounts, index, currentCount - 1)
      }
    },

    /** 重新加载数据 */
    retryLoad() {
      // 重新调用API获取工序详细信息
      this.loadStepDetails()
    },

    /** 显示确认对话框 */
    showConfirmDialog() {
      const summary = this.defectSummary
      let confirmMessage = `即将提交缺陷数据：\n\n`
      confirmMessage += `• 缺陷类型总数：${summary.totalTypes} 种\n`
      confirmMessage += `• 已选择类型：${summary.selectedTypes} 种\n`
      confirmMessage += `• 缺陷总数量：${summary.totalCount} 个\n\n`
     // confirmMessage += `注意：将提交所有缺陷类型的数据（包括数量为0的记录）\n\n`
      confirmMessage += `确认要提交吗？`

      uni.showModal({
        title: '确认提交缺陷',
        content: confirmMessage,
        confirmText: '确认提交',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.doSubmitDefects()
          }
        }
      })
    },

    /** 提交缺陷 */
    async submitDefects() {
      // 检查缺陷类型列表
      if (this.defectiveNames.length === 0) {
        showMessage('暂无可提交的缺陷类型', 'warning')
        return
      }

      // 检查工序ID
      if (!this.stepId) {
        showMessage('工序ID不能为空', 'error')
        return
      }

      // 显示确认对话框
      this.showConfirmDialog()
    },

    /** 执行实际的提交操作 */
    async doSubmitDefects() {
      try {
        this.submitting = true

        // 构建缺陷数据 - 包含所有缺陷类型（包括数量为0的）
        const defects = {}
        this.defectiveNames.forEach((name, index) => {
          const count = this.defectCounts[index] || 0
          defects[name] = count
        })

        console.log('提交缺陷数据:', {
          stepTaskId: this.stepTaskId,
          defects: defects
        })

        // 调用添加缺陷API
        const response = await addDefects({
          stepTaskId: this.stepTaskId,
          defects: defects
        })

       // console.log('添加缺陷API响应:', response)

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          showMessage('缺陷提交成功', 'success')

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          showMessage(response?.message || '缺陷提交失败', 'error')
        }
      } catch (error) {
       // console.error('提交缺陷失败:', error)
        showMessage('提交缺陷失败: ' + (error.message || '未知错误'), 'error')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.defect-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e8e8e8;
  padding: 0 24rpx;
  margin-top: 45rpx;

  .nav-left {
    width: 60rpx;
    display: flex;
    align-items: center;

    .back-icon {
      font-size: 40rpx;
      color: #333;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

/* 工序信息卡片 */
.step-info-card {
  margin: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .step-name, .step-id {
    display: flex;
    margin-bottom: 16rpx;

    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #666;
    }

    .value {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .step-id {
    margin-bottom: 0;
  }
}

/* 缺陷列表 */
.defect-list-container {
  margin: 0 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .section-title {
    padding: 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .loading-container, .error-container, .empty-container {
    padding: 60rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-text, .error-text, .empty-text {
      font-size: 28rpx;
      color: #999;
    }

    .retry-button {
      margin-top: 24rpx;
      padding: 12rpx 24rpx;
      background-color: #f0f0f0;
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #666;
    }
  }

  .defect-list {
    padding: 0 24rpx;

    .defect-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .defect-name {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }

      .defect-count {
        display: flex;
        align-items: center;

        .count-control {
          width: 60rpx;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          border-radius: 8rpx;
          font-size: 36rpx;
          color: #333;

          &.minus {
            color: #ff4d4f;
          }

          &.plus {
            color: #52c41a;
          }
        }

        .count-value {
          width: 80rpx;
          text-align: center;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}

/* 提交按钮 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);

  .submit-button {
    height: 88rpx;
    background-color: #e6a23c;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;

    &.disabled {
      background-color: #f3d19e;
      opacity: 0.8;
    }
  }
}
</style>