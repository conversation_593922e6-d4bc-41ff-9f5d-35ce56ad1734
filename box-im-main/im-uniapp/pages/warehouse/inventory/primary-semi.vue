<!--
此页面已于2024-03-21停用
原功能：一级半成品仓库管理页面
功能说明：
- 管理一级半成品库存
- 显示生产中、已完成的统计
- 显示当前工序和生产状态
停用原因：功能整合优化
-->

<template>
	<view class="page primary-semi">
		<nav-bar>初级半成品仓库</nav-bar>
		
		<view class="content">
			<view class="warehouse-stats">
				<view class="stat-card">
					<text class="stat-title">半成品总数</text>
					<text class="stat-value">{{ totalCount }}</text>
				</view>
				<view class="stat-card">
					<text class="stat-title">库存总量</text>
					<text class="stat-value">{{ totalStock }}</text>
				</view>
				<view class="stat-card">
					<text class="stat-title">生产中</text>
					<text class="stat-value processing">{{ processingCount }}</text>
				</view>
				<view class="stat-card">
					<text class="stat-title">已完成</text>
					<text class="stat-value completed">{{ completedCount }}</text>
				</view>
			</view>
			
			<view class="search-section">
				<view class="search-bar">
					<input 
						class="search-input" 
						placeholder="搜索半成品名称" 
						v-model="searchKeyword" 
						@confirm="onSearch"
					/>
					<button class="search-btn" @click="onSearch">搜索</button>
				</view>
			</view>
			
			<view class="semi-products-list">
				<view v-if="loading" class="loading-state">
					<text class="loading-text">加载中...</text>
				</view>
				
				<view v-else-if="semiProductsList.length === 0" class="empty-state">
					<text class="empty-text">暂无半成品数据</text>
				</view>
				
				<view v-else>
					<view 
						class="semi-product-item" 
						v-for="item in semiProductsList" 
						:key="item.semiProductId"
						@click="onItemClick(item)"
					>
						<view class="item-header">
							<text class="item-name">{{ item.semiProductName }}</text>
							<view class="item-type">
								<text class="type-text">{{ item.materialType }}</text>
							</view>
						</view>
						<view class="item-info">
							<view class="info-row">
								<text class="info-label">当前库存：</text>
								<text class="info-value stock-value" :class="getStockClass(item)">{{ item.currentStock }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">库存数量：</text>
								<text class="info-value">{{ item.stockQuantity }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">入库数量：</text>
								<text class="info-value">{{ item.inboundQuantity }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">出库数量：</text>
								<text class="info-value">{{ item.outboundQuantity }}</text>
							</view>
						</view>
						<view class="process-info">
							<view class="info-row">
								<text class="info-label">当前工序：</text>
								<text class="info-value process-step">{{ item.currentStep }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">生产状态：</text>
								<view class="process-status" :class="getProcessStatusClass(item)">
									<text class="status-text">{{ item.processStatus }}</text>
								</view>
							</view>
						</view>
						<view class="item-footer">
							<view class="footer-left">
								<text class="region-info">区域ID：{{ item.regionId || '未分配' }}</text>
							</view>
							<view class="footer-right">
								<view class="progress-indicator" :class="getProgressClass(item)">
									<text class="progress-text">{{ getProgressText(item) }}</text>
								</view>
							</view>
						</view>
						<view class="update-time">
							<text class="time-text">更新时间：{{ item.updatedTime }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="totalPages > 1" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="onPageChange(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="onPageChange(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			semiProductsList: [],
			loading: false,
			searchKeyword: '',
			currentPage: 1,
			pageSize: 10,
			totalCount: 0,
			totalPages: 0,
			totalStock: 0,
			processingCount: 0,
			completedCount: 0
		}
	},
	onLoad() {
		this.loadSemiProductsList()
	},
	methods: {
		async loadSemiProductsList() {
			this.loading = true
			try {
				const params = {
					pageNum: this.currentPage,
					pageSize: this.pageSize
				}

				// 添加搜索条件 - 支持多字段搜索
				if (this.searchKeyword) {
					console.log('一级半成品搜索关键词:', this.searchKeyword);
					params.semiProductName = this.searchKeyword;
					// 同时支持材料名称字段的模糊搜索
					params.materialName = this.searchKeyword;
				}

				console.log('一级半成品API请求参数:', params);

				// 直接使用uni.request处理不同服务器的API
				const response = await this.apiRequest({
					url: 'http://************:8090/wms/semiFinishedProduct/page',
					method: 'GET',
					data: params
				})

				console.log('一级半成品API响应:', response);

				if (response && response.code === 0) {
					const data = response.data
					this.semiProductsList = data.records || []
					this.totalCount = data.total || 0
					this.totalPages = data.pages || 0

					console.log('一级半成品数据加载成功，数量:', this.semiProductsList.length);

					// 计算统计数据
					this.calculateStats()
				} else {
					console.warn('一级半成品API返回异常:', response);
					uni.showToast({
						title: response?.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载半成品列表失败:', error)
				uni.showToast({
					title: error.message || '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 创建专用的API请求方法
		apiRequest(options) {
			return new Promise((resolve, reject) => {
				uni.request({
					url: options.url,
					method: options.method || 'GET',
					data: options.data || {},
					timeout: 30000,
					success(res) {
						if (res.statusCode === 200) {
							resolve(res.data)
						} else {
							reject(new Error(`请求失败 (${res.statusCode})`))
						}
					},
					fail(error) {
						console.error('API请求失败:', error)
						reject(new Error('网络请求失败'))
					}
				})
			})
		},
		
		calculateStats() {
			// 计算各种统计数据
			this.totalStock = this.semiProductsList.reduce((sum, item) => sum + (item.currentStock || 0), 0)
			this.processingCount = this.semiProductsList.filter(item => item.processStatus !== '已完成').length
			this.completedCount = this.semiProductsList.filter(item => item.processStatus === '已完成').length
		},
		
		onSearch() {
			this.currentPage = 1
			this.loadSemiProductsList()
		},
		
		onPageChange(page) {
			if (page >= 1 && page <= this.totalPages) {
				this.currentPage = page
				this.loadSemiProductsList()
			}
		},
		
		getStockClass(item) {
			// 根据库存情况返回样式类
			if (item.currentStock === 0) {
				return 'stock-empty'
			} else if (item.currentStock < 10) {
				return 'stock-low'
			} else {
				return 'stock-normal'
			}
		},
		
		getProcessStatusClass(item) {
			// 根据生产状态返回样式类
			switch (item.processStatus) {
				case '已完成':
					return 'status-completed'
				case '生产中':
					return 'status-processing'
				case '待开始':
					return 'status-pending'
				default:
					return 'status-unknown'
			}
		},
		
		getProgressClass(item) {
			// 根据生产状态返回进度样式
			return item.processStatus === '已完成' ? 'progress-completed' : 'progress-processing'
		},
		
		getProgressText(item) {
			// 根据生产状态返回进度文本
			switch (item.processStatus) {
				case '已完成':
					return '100%'
				case '生产中':
					return '进行中'
				case '待开始':
					return '0%'
				default:
					return '未知'
			}
		},
		
		onItemClick(item) {
			// 点击半成品项，显示操作菜单
			const menuItems = ['查看详情', '生产进度', '质量检查', '库存调整', '工序管理']
			
			uni.showActionSheet({
				itemList: menuItems,
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							this.viewItemDetail(item)
							break
						case 1:
							this.viewProductionProgress(item)
							break
						case 2:
							this.qualityCheck(item)
							break
						case 3:
							this.adjustStock(item)
							break
						case 4:
							this.manageProcess(item)
							break
					}
				}
			})
		},
		
		viewItemDetail(item) {
			// 查看详情
			const content = `半成品名称：${item.semiProductName}
材料类型：${item.materialType}
当前库存：${item.currentStock}
库存数量：${item.stockQuantity}
入库数量：${item.inboundQuantity}
出库数量：${item.outboundQuantity}
当前工序：${item.currentStep}
生产状态：${item.processStatus}
区域ID：${item.regionId || '未分配'}
更新时间：${item.updatedTime}`
			
			uni.showModal({
				title: '半成品详情',
				content: content,
				showCancel: false
			})
		},
		
		viewProductionProgress(item) {
			// 查看生产进度
			const progressInfo = `半成品：${item.semiProductName}
当前工序：${item.currentStep}
生产状态：${item.processStatus}
完成进度：${this.getProgressText(item)}`
			
			uni.showModal({
				title: '生产进度',
				content: progressInfo,
				showCancel: false
			})
		},
		
		qualityCheck(item) {
			// 质量检查
			uni.showModal({
				title: '质量检查',
				content: `是否要对 ${item.semiProductName} 进行质量检查？`,
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '质量检查功能开发中',
							icon: 'none'
						})
					}
				}
			})
		},
		
		adjustStock(item) {
			// 库存调整
			uni.showModal({
				title: '库存调整',
				content: `是否要调整 ${item.semiProductName} 的库存？`,
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '库存调整功能开发中',
							icon: 'none'
						})
					}
				}
			})
		},
		
		manageProcess(item) {
			// 工序管理
			uni.showModal({
				title: '工序管理',
				content: `当前工序：${item.currentStep}
是否要管理该半成品的生产工序？`,
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '工序管理功能开发中',
							icon: 'none'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.primary-semi {
	.content {
		padding: 20rpx;
		
		.warehouse-stats {
			display: flex;
			gap: 15rpx;
			margin-bottom: 30rpx;
			flex-wrap: wrap;
			
			.stat-card {
				flex: 1;
				min-width: 160rpx;
				background: #fff;
				padding: 25rpx;
				border-radius: 10rpx;
				text-align: center;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.stat-title {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-bottom: 8rpx;
				}
				
				.stat-value {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
					
					&.processing {
						color: #ff9500;
					}
					
					&.completed {
						color: #34c759;
					}
				}
			}
		}
		
		.search-section {
			background: #fff;
			padding: 30rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			
			.search-bar {
				display: flex;
				gap: 20rpx;
				
				.search-input {
					flex: 1;
					padding: 20rpx;
					border: 1px solid #ddd;
					border-radius: 10rpx;
					background: #f8f8f8;
				}
				
				.search-btn {
					padding: 20rpx 30rpx;
					background: #007aff;
					color: #fff;
					border: none;
					border-radius: 10rpx;
				}
			}
		}
		
		.semi-products-list {
			.loading-state, .empty-state {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
				
				.loading-text, .empty-text {
					color: #999;
					font-size: 28rpx;
				}
			}
			
			.semi-product-item {
				background: #fff;
				margin-bottom: 20rpx;
				padding: 30rpx;
				border-radius: 10rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;
					
					.item-name {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						flex: 1;
					}
					
					.item-type {
						padding: 8rpx 16rpx;
						border-radius: 20rpx;
						background: #ff9500;
						
						.type-text {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}
				
				.item-info {
					margin-bottom: 15rpx;
					
					.info-row {
						display: flex;
						margin-bottom: 8rpx;
						
						.info-label {
							font-size: 26rpx;
							color: #666;
							width: 160rpx;
						}
						
						.info-value {
							font-size: 26rpx;
							color: #333;
							flex: 1;
							
							&.stock-value {
								font-weight: bold;
								
								&.stock-empty {
									color: #ff3b30;
								}
								
								&.stock-low {
									color: #ff9500;
								}
								
								&.stock-normal {
									color: #34c759;
								}
							}
						}
					}
				}
				
				.process-info {
					margin-bottom: 15rpx;
					padding: 15rpx;
					background: #f8f9fa;
					border-radius: 8rpx;
					
					.info-row {
						display: flex;
						align-items: center;
						margin-bottom: 8rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.info-label {
							font-size: 26rpx;
							color: #666;
							width: 160rpx;
						}
						
						.info-value {
							font-size: 26rpx;
							color: #333;
							flex: 1;
							
							&.process-step {
								color: #007aff;
								font-weight: bold;
							}
						}
						
						.process-status {
							padding: 4rpx 12rpx;
							border-radius: 12rpx;
							font-size: 22rpx;
							
							&.status-completed {
								background: #34c759;
								color: #fff;
							}
							
							&.status-processing {
								background: #ff9500;
								color: #fff;
							}
							
							&.status-pending {
								background: #8e8e93;
								color: #fff;
							}
							
							&.status-unknown {
								background: #f0f0f0;
								color: #666;
							}
							
							.status-text {
								font-size: 22rpx;
							}
						}
					}
				}
				
				.item-footer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 15rpx;
					
					.footer-left {
						.region-info {
							font-size: 24rpx;
							color: #666;
						}
					}
					
					.footer-right {
						.progress-indicator {
							padding: 6rpx 12rpx;
							border-radius: 15rpx;
							
							&.progress-completed {
								background: #34c759;
							}
							
							&.progress-processing {
								background: #ff9500;
							}
							
							.progress-text {
								color: #fff;
								font-size: 22rpx;
								font-weight: bold;
							}
						}
					}
				}
				
				.update-time {
					border-top: 1px solid #f0f0f0;
					padding-top: 15rpx;
					
					.time-text {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
		}
		
		.pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 30rpx;
			margin-top: 30rpx;
			padding: 30rpx;
			
			.page-btn {
				padding: 20rpx 40rpx;
				background: #007aff;
				color: #fff;
				border: none;
				border-radius: 10rpx;
				font-size: 26rpx;
				
				&:disabled {
					background: #ccc;
					color: #999;
				}
			}
			
			.page-info {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
}
</style> 