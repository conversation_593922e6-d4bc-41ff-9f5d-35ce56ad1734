<template>
	<view class="page purchase-management">
		<nav-bar>采购管理</nav-bar>
		
		<view class="content">
			<view class="purchase-menu">
				<view class="menu-item" @click="navToPurchaseOrders">
					<view class="menu-icon">
						<text class="iconfont">📋</text>
					</view>
					<view class="menu-info">
						<text class="menu-title">采购订单管理</text>
						<text class="menu-desc">管理采购订单、审核流程</text>
					</view>
					<view class="menu-arrow">
						<text class="iconfont">></text>
					</view>
				</view>
				
				<view class="menu-item" @click="navToItemManagement">
					<view class="menu-icon">
						<text class="iconfont">📦</text>
					</view>
					<view class="menu-info">
						<text class="menu-title">物品信息管理</text>
						<text class="menu-desc">管理物品信息、规格库存</text>
					</view>
					<view class="menu-arrow">
						<text class="iconfont">></text>
					</view>
				</view>
				
				<view class="menu-item" @click="navToSupplierManagement">
					<view class="menu-icon">
						<text class="iconfont">🏢</text>
					</view>
					<view class="menu-info">
						<text class="menu-title">供应商管理</text>
						<text class="menu-desc">管理供应商信息、合作关系</text>
					</view>
					<view class="menu-arrow">
						<text class="iconfont">></text>
					</view>
				</view>
			</view>
			
			<view class="stats-overview">
				<view class="stats-card">
					<text class="stats-title">待处理订单</text>
					<text class="stats-value">{{ pendingOrders }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">本月采购</text>
					<text class="stats-value">{{ monthlyOrders }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">总采购额</text>
					<text class="stats-value">¥{{ totalAmount }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">供应商数量</text>
					<text class="stats-value">{{ supplierCount }}</text>
				</view>
			</view>
			
			<view class="quick-actions">
				<text class="section-title">快捷操作</text>
				<view class="action-grid">
					<view class="action-item" @click="quickAddOrder">
						<view class="action-icon">
							<text>➕</text>
						</view>
						<text class="action-text">新增采购订单</text>
					</view>
					<view class="action-item" @click="quickViewPendingApprovals">
						<view class="action-icon">
							<text>✅</text>
						</view>
						<text class="action-text">待审核订单</text>
					</view>
					<view class="action-item" @click="quickAddSupplier">
						<view class="action-icon">
							<text>🏢</text>
						</view>
						<text class="action-text">新增供应商</text>
					</view>
					<view class="action-item" @click="quickInventoryAlert">
						<view class="action-icon">
							<text>⚠️</text>
						</view>
						<text class="action-text">库存预警</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			pendingOrders: 5,
			monthlyOrders: 12,
			totalAmount: '156,800',
			supplierCount: 25
		}
	},
	onLoad() {
		this.loadStats()
	},
	methods: {
		navToPurchaseOrders() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/purchase-orders'
			})
		},
		
		navToItemManagement() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/item-management'
			})
		},
		
		navToSupplierManagement() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/supplier-management'
			})
		},
		
		quickAddOrder() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/purchase-application'
			})
		},
		
		quickViewPendingApprovals() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/purchase-orders?filter=pending'
			})
		},
		
		quickAddSupplier() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/supplier-management?action=add'
			})
		},
		
		quickInventoryAlert() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/item-management?filter=low-stock'
			})
		},
		
		loadStats() {
			// 加载统计数据
			// 这里可以调用API获取真实数据
		}
	}
}
</script>

<style scoped lang="scss">
.purchase-management {
	.content {
		padding: 20rpx;
		
		.purchase-menu {
			background: #fff;
			border-radius: 10rpx;
			margin-bottom: 30rpx;
			
			.menu-item {
				display: flex;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1px solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				&:active {
					background: #f8f8f8;
				}
				
				.menu-icon {
					width: 80rpx;
					height: 80rpx;
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border-radius: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;
					
					text {
						font-size: 40rpx;
					}
				}
				
				.menu-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					
					.menu-title {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 8rpx;
					}
					
					.menu-desc {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.menu-arrow {
					color: #ccc;
					font-size: 28rpx;
				}
			}
		}
		
		.stats-overview {
			display: flex;
			gap: 15rpx;
			flex-wrap: wrap;
			margin-bottom: 30rpx;
			
			.stats-card {
				flex: 1;
				min-width: 200rpx;
				background: #fff;
				padding: 25rpx;
				border-radius: 10rpx;
				text-align: center;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.stats-title {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-bottom: 8rpx;
				}
				
				.stats-value {
					font-size: 32rpx;
					font-weight: bold;
					color: #007aff;
				}
			}
		}
		
		.quick-actions {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			
			.section-title {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}
			
			.action-grid {
				display: flex;
				flex-wrap: wrap;
				gap: 20rpx;
				
				.action-item {
					flex: 1;
					min-width: 150rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 20rpx;
					border: 1px solid #f0f0f0;
					border-radius: 10rpx;
					
					&:active {
						background: #f8f8f8;
					}
					
					.action-icon {
						width: 60rpx;
						height: 60rpx;
						background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
						border-radius: 30rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-bottom: 10rpx;
						
						text {
							font-size: 30rpx;
						}
					}
					
					.action-text {
						font-size: 22rpx;
						color: #666;
						text-align: center;
					}
				}
			}
		}
	}
}
</style> 