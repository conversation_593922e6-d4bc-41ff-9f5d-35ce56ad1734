<template>
	<view class="page purchase-orders">
		<nav-bar>采购订单管理</nav-bar>
		
		<view class="content">
			<view class="purchase-stats">
				<view class="stat-item">
					<text class="stat-label">总订单</text>
					<text class="stat-value">{{ totalOrders }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">待审核</text>
					<text class="stat-value pending">{{ pendingOrders }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">本月金额</text>
					<text class="stat-value amount">¥{{ monthlyAmount }}</text>
				</view>
			</view>
			
			<view class="purchase-list">
				<view v-if="loading" class="loading-state">
					<text class="loading-text">加载中...</text>
				</view>
				
				<view v-else-if="purchaseList.length === 0" class="empty-state">
					<text class="empty-text">暂无采购订单</text>
				</view>
				
				<view v-else>
					<view 
						class="purchase-item" 
						v-for="item in purchaseList" 
						:key="item.id"
						@click="onItemClick(item)"
					>
						<view class="item-header">
							<text class="order-no">订单号：{{ item.purchaseNo }}</text>
							<view class="status-badge" :class="getStatusClass(item.status)">
								<text class="status-text">{{ getStatusText(item.status) }}</text>
							</view>
						</view>
						
						<view class="item-info">
							<view class="info-row">
								<text class="info-label">物品ID：</text>
								<text class="info-value">{{ item.itemId }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">供应商ID：</text>
								<text class="info-value">{{ item.supplierId }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">采购金额：</text>
								<text class="info-value amount">¥{{ item.subtotal }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">数量：</text>
								<text class="info-value">{{ item.quantity }} {{ item.unit }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">单价：</text>
								<text class="info-value">¥{{ item.price }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">申请人：</text>
								<text class="info-value">{{ item.applicant }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">申请时间：</text>
								<text class="info-value">{{ formatDate(item.applyTime) }}</text>
							</view>
							<view class="info-row" v-if="item.approver">
								<text class="info-label">审核人：</text>
								<text class="info-value">{{ item.approver }}</text>
							</view>
							<view class="info-row" v-if="item.approveTime">
								<text class="info-label">审核时间：</text>
								<text class="info-value">{{ formatDate(item.approveTime) }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">期望交付：</text>
								<text class="info-value">{{ item.expectedDate }}</text>
							</view>
							<view class="info-row" v-if="item.remark">
								<text class="info-label">备注：</text>
								<text class="info-value">{{ item.remark }}</text>
							</view>
						</view>
						
						<view class="item-footer">
							<view class="footer-left">
								<text class="create-time">创建时间：{{ formatDate(item.createTime) }}</text>
							</view>
							<view class="footer-right">
								<button class="action-btn" @click.stop="viewDetail(item)">查看详情</button>
								<button 
									v-if="item.status === 0" 
									class="action-btn primary" 
									@click.stop="approveOrder(item)"
								>
									审核
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="totalPages > 1" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="onPageChange(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="onPageChange(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
		
		<!-- 新增采购订单按钮 -->
		<view class="fab-btn" @click="addNewOrder">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			purchaseList: [],
			loading: false,
			currentPage: 1,
			pageSize: 10,
			totalPages: 0,
			totalOrders: 0,
			pendingOrders: 0,
			monthlyAmount: '0'
		}
	},
	onLoad(options) {
		this.loadPurchaseList()
	},
	methods: {
		async loadPurchaseList() {
			this.loading = true
			
			try {
				const response = await uni.request({
					url: `http://************:8090/wms/purchase/listByPage`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						pageNum: this.currentPage,
						pageSize: this.pageSize
					}
				})
				
				if (response.data.code === 0) {
					const data = response.data.data
					this.purchaseList = data.records || []
					this.totalOrders = data.total || 0
					this.totalPages = data.pages || 1
					
					// 计算统计数据
					this.calculateStats()
				} else {
					uni.showToast({
						title: response.data.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载采购订单失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		calculateStats() {
			// 计算待审核订单数量
			this.pendingOrders = this.purchaseList.filter(item => item.status === 0).length
			
			// 计算本月采购金额（这里简化处理，实际应该根据时间筛选）
			const totalAmount = this.purchaseList.reduce((sum, item) => {
				return sum + parseFloat(item.subtotal || 0)
			}, 0)
			this.monthlyAmount = totalAmount.toFixed(2)
		},
		
		onPageChange(page) {
			if (page >= 1 && page <= this.totalPages) {
				this.currentPage = page
				this.loadPurchaseList()
			}
		},
		
		getStatusClass(status) {
			switch (status) {
				case 0:
					return 'status-pending'
				case 1:
					return 'status-approved'
				case 2:
					return 'status-rejected'
				case 3:
					return 'status-completed'
				default:
					return 'status-default'
			}
		},
		
		getStatusText(status) {
			switch (status) {
				case 0:
					return '待审核'
				case 1:
					return '已通过'
				case 2:
					return '已拒绝'
				case 3:
					return '已完成'
				default:
					return '未知状态'
			}
		},
		
		formatDate(dateString) {
			if (!dateString) return ''
			const date = new Date(dateString)
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			})
		},
		
		onItemClick(item) {
			this.viewDetail(item)
		},
		
		viewDetail(item) {
			const content = `订单号：${item.purchaseNo}
物品ID：${item.itemId}
供应商ID：${item.supplierId}
数量：${item.quantity} ${item.unit}
单价：¥${item.price}
总金额：¥${item.subtotal}
申请人：${item.applicant}
申请时间：${this.formatDate(item.applyTime)}
${item.approver ? `审核人：${item.approver}` : ''}
${item.approveTime ? `审核时间：${this.formatDate(item.approveTime)}` : ''}
期望交付：${item.expectedDate}
状态：${this.getStatusText(item.status)}
${item.remark ? `备注：${item.remark}` : ''}`
			
			uni.showModal({
				title: '采购订单详情',
				content: content,
				showCancel: false
			})
		},
		
		approveOrder(item) {
			uni.showActionSheet({
				itemList: ['审核通过', '审核拒绝'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 审核通过
						this.updateOrderStatus(item, 1, '审核通过')
					} else if (res.tapIndex === 1) {
						// 审核拒绝
						uni.showModal({
							title: '审核拒绝',
							content: '请输入拒绝原因',
							editable: true,
							placeholderText: '请输入拒绝原因',
							success: (modalRes) => {
								if (modalRes.confirm) {
									this.updateOrderStatus(item, 2, modalRes.content || '审核拒绝')
								}
							}
						})
					}
				}
			})
		},
		
		async updateOrderStatus(item, status, remark) {
			try {
				const response = await uni.request({
					url: `http://************:8090/wms/purchase/audit`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						id: item.id,
						status: status,
						remark: remark
					}
				})
				
				if (response.data.code === 0) {
					uni.showToast({
						title: status === 1 ? '审核通过' : '审核拒绝',
						icon: 'success'
					})
					// 重新加载数据
					this.loadPurchaseList()
				} else {
					uni.showToast({
						title: response.data.message || '操作失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('更新订单状态失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			}
		},
		
		addNewOrder() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/purchase-application',
				fail: (error) => {
					console.error('页面跳转失败:', error)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.purchase-orders {
	.content {
		padding: 20rpx;
		padding-bottom: 120rpx; // 为悬浮按钮留出空间
		
		.purchase-stats {
			display: flex;
			gap: 15rpx;
			margin-bottom: 20rpx;
			
			.stat-item {
				flex: 1;
				background: #fff;
				padding: 20rpx;
				border-radius: 10rpx;
				text-align: center;
				
				.stat-label {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-bottom: 8rpx;
				}
				
				.stat-value {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					
					&.pending {
						color: #ff9500;
					}
					
					&.amount {
						color: #34c759;
					}
				}
			}
		}
		
		.purchase-list {
			.loading-state, .empty-state {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
			
				.loading-text, .empty-text {
					color: #999;
					font-size: 28rpx;
				}
			}
			
			.purchase-item {
				background: #fff;
				margin-bottom: 20rpx;
				padding: 30rpx;
				border-radius: 10rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;
					
					.order-no {
						font-size: 30rpx;
						font-weight: bold;
						color: #333;
					}
					
					.status-badge {
						padding: 6rpx 16rpx;
						border-radius: 15rpx;
						font-size: 22rpx;
					
						&.status-pending {
							background: #ff9500;
							color: #fff;
						}
					
						&.status-approved {
							background: #34c759;
							color: #fff;
						}
						
						&.status-rejected {
							background: #ff3b30;
							color: #fff;
						}
						
						&.status-completed {
							background: #5856d6;
							color: #fff;
						}
						
						&.status-default {
							background: #8e8e93;
							color: #fff;
						}
					}
				}
				
				.item-info {
					margin-bottom: 20rpx;
					
					.info-row {
						display: flex;
						margin-bottom: 8rpx;
						
						.info-label {
							font-size: 26rpx;
							color: #666;
							width: 140rpx;
							flex-shrink: 0;
						}
						
						.info-value {
							font-size: 26rpx;
							color: #333;
							flex: 1;
							word-break: break-all;
							
							&.amount {
								color: #34c759;
								font-weight: bold;
							}
						}
					}
				}
				
				.item-footer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					border-top: 1px solid #f0f0f0;
					padding-top: 15rpx;
					
					.footer-left {
						.create-time {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.footer-right {
						display: flex;
						gap: 15rpx;
						
						.action-btn {
							padding: 12rpx 24rpx;
							border: 1px solid #ddd;
							border-radius: 20rpx;
							background: #fff;
							color: #666;
							font-size: 24rpx;
							
							&.primary {
								background: #007aff;
								color: #fff;
								border-color: #007aff;
							}
						}
					}
				}
			}
		}
		
		.pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 30rpx;
			margin-top: 30rpx;
			padding: 30rpx;
			
			.page-btn {
				padding: 20rpx 40rpx;
				background: #007aff;
				color: #fff;
				border: none;
				border-radius: 10rpx;
				font-size: 26rpx;
				
				&:disabled {
					background: #ccc;
					color: #999;
				}
			}
		
			.page-info {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	.fab-btn {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background: #007aff;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0,122,255,0.3);
		z-index: 999;
		
		.fab-icon {
			color: #fff;
			font-size: 40rpx;
			font-weight: bold;
		}
	}
}
</style> 