<template>
	<view class="page item-management">
		<nav-bar>物品信息管理</nav-bar>
		
		<view class="content">
			<view class="search-section">
				<view class="search-bar">
					<input 
						class="search-input" 
						placeholder="搜索物品名称或编号" 
						v-model="searchKeyword" 
						@confirm="onSearch"
					/>
					<button class="search-btn" @click="onSearch">搜索</button>
				</view>
				
				<view class="filter-section">
					<view class="filter-row">
						<text class="filter-label">分类：</text>
						<picker 
							:value="categoryOptions.indexOf(selectedCategory)" 
							:range="categoryOptions" 
							@change="onCategoryChange"
						>
							<view class="picker-text">{{ selectedCategory }}</view>
						</picker>
					</view>
					<view class="filter-row">
						<text class="filter-label">库存状态：</text>
						<picker 
							:value="stockStatusOptions.findIndex(item => item.value === selectedStockStatus)" 
							:range="stockStatusOptions" 
							range-key="name"
							@change="onStockStatusChange"
						>
							<view class="picker-text">{{ stockStatusOptions.find(item => item.value === selectedStockStatus)?.name || '全部' }}</view>
						</picker>
					</view>
				</view>
			</view>
			
			<view class="stats-overview">
				<view class="stats-card">
					<text class="stats-title">总物品数</text>
					<text class="stats-value">{{ totalItems }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">库存充足</text>
					<text class="stats-value sufficient">{{ sufficientItems }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">库存不足</text>
					<text class="stats-value insufficient">{{ insufficientItems }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">缺货</text>
					<text class="stats-value out-of-stock">{{ outOfStockItems }}</text>
				</view>
			</view>
			
			<view class="item-list">
				<view v-if="loading" class="loading-state">
					<text class="loading-text">加载中...</text>
				</view>
				
				<view v-else-if="displayItemList.length === 0" class="empty-state">
					<text class="empty-text">暂无物品信息</text>
				</view>
				
				<view v-else>
					<view 
						class="item-card" 
						v-for="item in displayItemList" 
						:key="item.id"
						@click="onItemClick(item)"
					>
						<view class="item-header">
							<text class="item-name">{{ item.itemName }}</text>
							<view class="stock-status" :class="getStockStatusClass(item.stockStatus)">
								<text class="status-text">{{ getStockStatusText(item.stockStatus) }}</text>
							</view>
						</view>
						
						<view class="item-details">
							<view class="info-row">
								<text class="info-label">编号：</text>
								<text class="info-value">{{ item.itemCode }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">分类：</text>
								<text class="info-value">{{ item.category }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">规格：</text>
								<text class="info-value">{{ item.specification }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">单位：</text>
								<text class="info-value">{{ item.unit }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">品牌：</text>
								<text class="info-value">{{ item.brand }}</text>
							</view>
							<view class="info-row" v-if="item.unitPrice > 0">
								<text class="info-label">单价：</text>
								<text class="info-value price">¥{{ item.unitPrice }}</text>
							</view>
						</view>
						
						<view class="stock-info">
							<view class="stock-row">
								<text class="stock-label">当前库存：</text>
								<text class="stock-value" :class="{ 'low-stock': item.currentStock <= item.minStock }">
									{{ item.currentStock }}
								</text>
							</view>
							<view class="stock-row">
								<text class="stock-label">最低库存：</text>
								<text class="stock-value">{{ item.minStock }}</text>
							</view>
							<view class="stock-row">
								<text class="stock-label">最高库存：</text>
								<text class="stock-value">{{ item.maxStock }}</text>
							</view>
						</view>
						
						<view class="item-actions">
							<button class="action-btn" @click.stop="editItem(item)">编辑</button>
							<button class="action-btn" @click.stop="viewHistory(item)">库存记录</button>
							<button class="action-btn primary" @click.stop="createPurchaseOrder(item)">
								采购申请
							</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="totalPages > 1" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="onPageChange(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="onPageChange(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
		
		<!-- 新增物品按钮 -->
		<view class="fab-btn" @click="addNewItem">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			itemList: [],
			filteredItemList: [],
			displayItemList: [],
			loading: false,
			searchKeyword: '',
			selectedCategory: '全部',
			selectedStockStatus: 'all',
			currentPage: 1,
			pageSize: 10,
			totalPages: 0,
			totalItems: 0,
			sufficientItems: 0,
			insufficientItems: 0,
			outOfStockItems: 0,
			categoryOptions: ['全部', '原料', '零部件', '成品', '工具设备', '消耗品'],
			stockStatusOptions: [
				{ name: '全部', value: 'all' },
				{ name: '库存充足', value: 'sufficient' },
				{ name: '库存不足', value: 'low-stock' },
				{ name: '缺货', value: 'out-of-stock' }
			]
		}
	},
	onLoad() {
		this.loadItemList()
	},
	methods: {
		async loadItemList() {
			this.loading = true
			
			try {
				const response = await uni.request({
					url: `http://************:8090/wms/item/listAll`,
					method: 'GET',
					header: {
						'Content-Type': 'application/json'
					}
				})
				
				if (response.data.code === 0) {
					// 适配API数据结构
					this.itemList = response.data.data.map(item => ({
						id: item.id,
						itemName: item.itemName,
						itemCode: `ITEM${String(item.id).padStart(4, '0')}`, // 生成物品编码
						category: item.category,
						specification: item.specification || '无',
						unit: item.unit || '个',
						unitPrice: 0, // API中没有价格数据，设为0
						brand: item.brand || '无',
						currentStock: Math.floor(Math.random() * 1000), // 模拟库存数据
						minStock: Math.floor(Math.random() * 50) + 10,
						maxStock: Math.floor(Math.random() * 500) + 200,
						stockStatus: this.getStockStatus(Math.floor(Math.random() * 1000), Math.floor(Math.random() * 50) + 10),
						createTime: item.createTime,
						updateTime: item.updateTime
					}))
					
					// 计算统计数据
					this.calculateStats()
					
					// 应用筛选
					this.applyFilters()
				} else {
					uni.showToast({
						title: response.data.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载物品列表失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		getStockStatus(currentStock, minStock) {
			if (currentStock === 0) {
				return 'out-of-stock'
			} else if (currentStock <= minStock) {
				return 'low-stock'
			} else {
				return 'sufficient'
			}
		},
		
		calculateStats() {
			this.totalItems = this.itemList.length
			this.sufficientItems = this.itemList.filter(item => item.stockStatus === 'sufficient').length
			this.insufficientItems = this.itemList.filter(item => item.stockStatus === 'low-stock').length
			this.outOfStockItems = this.itemList.filter(item => item.stockStatus === 'out-of-stock').length
		},
		
		applyFilters() {
			let filteredList = [...this.itemList]
			
			// 按分类筛选
			if (this.selectedCategory !== '全部') {
				filteredList = filteredList.filter(item => item.category === this.selectedCategory)
			}
			
			// 按库存状态筛选
			if (this.selectedStockStatus !== 'all') {
				filteredList = filteredList.filter(item => item.stockStatus === this.selectedStockStatus)
			}
			
			// 按搜索关键词筛选
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase()
				filteredList = filteredList.filter(item => 
					item.itemName.toLowerCase().includes(keyword) ||
					item.itemCode.toLowerCase().includes(keyword)
				)
			}
			
			// 更新显示列表和分页
			this.filteredItemList = filteredList
			this.totalPages = Math.ceil(filteredList.length / this.pageSize)
			this.currentPage = 1
			this.updateDisplayList()
		},
		
		updateDisplayList() {
			const startIndex = (this.currentPage - 1) * this.pageSize
			const endIndex = startIndex + this.pageSize
			this.displayItemList = this.filteredItemList.slice(startIndex, endIndex)
		},
		
		onSearch() {
			this.currentPage = 1
			this.loadItemList()
		},
		
		onCategoryChange(e) {
			this.selectedCategory = this.categoryOptions[e.detail.value]
			this.applyFilters()
		},
		
		onStockStatusChange(e) {
			this.selectedStockStatus = this.stockStatusOptions[e.detail.value].value
			this.applyFilters()
		},
		
		onPageChange(page) {
			if (page >= 1 && page <= this.totalPages) {
				this.currentPage = page
				this.updateDisplayList()
			}
		},
		
		getStockStatusClass(status) {
			switch (status) {
				case 'sufficient':
					return 'status-sufficient'
				case 'low-stock':
					return 'status-low'
				case 'out-of-stock':
					return 'status-out'
				default:
					return 'status-default'
			}
		},
		
		getStockStatusText(status) {
			switch (status) {
				case 'sufficient':
					return '库存充足'
				case 'low-stock':
					return '库存不足'
				case 'out-of-stock':
					return '缺货'
				default:
					return '未知状态'
			}
		},
		
		onItemClick(item) {
			this.viewItemDetail(item)
		},
		
		viewItemDetail(item) {
			const content = `物品编号：${item.itemCode}
分类：${item.category}
规格：${item.specification}
单位：${item.unit}
单价：¥${item.unitPrice}
当前库存：${item.currentStock}
最低库存：${item.minStock}
最高库存：${item.maxStock}
库存状态：${item.stockStatus}`
			
			uni.showModal({
				title: item.itemName,
				content: content,
				showCancel: false
			})
		},
		
		editItem(item) {
			uni.showToast({
				title: '编辑功能开发中',
				icon: 'none'
			})
		},
		
		viewHistory(item) {
			uni.showToast({
				title: '库存记录功能开发中',
				icon: 'none'
			})
		},
		
		createPurchaseOrder(item) {
			uni.showModal({
				title: '创建采购申请',
				content: `是否为 ${item.itemName} 创建采购申请？`,
				success: (res) => {
					if (res.confirm) {
						uni.navigateTo({
							url: `/pages/warehouse/purchase/purchase-application?itemId=${item.id}&itemName=${item.itemName}`
						})
					}
				}
			})
		},
		
		addNewItem() {
			uni.showToast({
				title: '新增物品功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped lang="scss">
.item-management {
	.content {
		padding: 20rpx;
		padding-bottom: 120rpx;
		
		.search-section {
			background: #fff;
			padding: 20rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			
			.search-bar {
				display: flex;
				gap: 20rpx;
				margin-bottom: 20rpx;
				
				.search-input {
					flex: 1;
					padding: 20rpx;
					border: 1px solid #ddd;
					border-radius: 10rpx;
					background: #f8f8f8;
				}
				
				.search-btn {
					padding: 20rpx 30rpx;
					background: #007aff;
					color: #fff;
					border: none;
					border-radius: 10rpx;
				}
			}
			
			.filter-section {
				.filter-row {
					display: flex;
					align-items: center;
					margin-bottom: 15rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.filter-label {
						font-size: 26rpx;
						color: #333;
						width: 120rpx;
					}
					
					.picker-text {
						flex: 1;
						padding: 15rpx 20rpx;
						border: 1px solid #ddd;
						border-radius: 8rpx;
						background: #f8f8f8;
						font-size: 26rpx;
						color: #333;
					}
				}
			}
		}
		
		.stats-overview {
			display: flex;
			gap: 15rpx;
			flex-wrap: wrap;
			margin-bottom: 20rpx;
			
			.stats-card {
				flex: 1;
				min-width: 160rpx;
				background: #fff;
				padding: 20rpx;
				border-radius: 10rpx;
				text-align: center;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.stats-title {
					display: block;
					font-size: 22rpx;
					color: #666;
					margin-bottom: 8rpx;
				}
				
				.stats-value {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					
					&.sufficient {
						color: #34c759;
					}
					
					&.insufficient {
						color: #ff9500;
					}
					
					&.out-of-stock {
						color: #ff3b30;
					}
				}
			}
		}
		
		.item-list {
			.loading-state, .empty-state {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
			
				.loading-text, .empty-text {
					color: #999;
					font-size: 28rpx;
				}
			}
			
			.item-card {
				background: #fff;
				margin-bottom: 20rpx;
				padding: 30rpx;
				border-radius: 10rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin-bottom: 20rpx;
					
					.item-name {
						flex: 1;
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 5rpx;
					}
					
					.stock-status {
						padding: 6rpx 16rpx;
						border-radius: 15rpx;
						font-size: 22rpx;
						
						&.status-sufficient {
							background: #34c759;
							color: #fff;
						}
						
						&.status-insufficient {
							background: #ff9500;
							color: #fff;
						}
						
						&.status-out-of-stock {
							background: #ff3b30;
							color: #fff;
						}
					}
				}
				
				.item-details {
					margin-bottom: 20rpx;
					
					.info-row {
						display: flex;
						margin-bottom: 8rpx;
						
						.info-label {
							font-size: 26rpx;
							color: #666;
							width: 100rpx;
						}
						
						.info-value {
							font-size: 26rpx;
							color: #333;
							flex: 1;
							
							&.price {
								color: #34c759;
								font-weight: bold;
							}
						}
					}
				}
				
				.stock-info {
					background: #f8f8f8;
					padding: 20rpx;
					border-radius: 8rpx;
					margin-bottom: 20rpx;
					
					.stock-row {
						display: flex;
						justify-content: space-between;
						margin-bottom: 8rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.stock-label {
							font-size: 24rpx;
							color: #666;
						}
						
						.stock-value {
							font-size: 24rpx;
							color: #333;
							font-weight: bold;
							
							&.low-stock {
								color: #ff3b30;
							}
						}
					}
				}
				
				.item-actions {
					display: flex;
					gap: 15rpx;
					border-top: 1px solid #f0f0f0;
					padding-top: 20rpx;
					
					.action-btn {
						flex: 1;
						padding: 15rpx 20rpx;
						border: 1px solid #ddd;
						border-radius: 8rpx;
						background: #fff;
						color: #666;
						font-size: 24rpx;
						text-align: center;
						
						&.primary {
							background: #007aff;
							color: #fff;
							border-color: #007aff;
						}
					}
				}
			}
		}
		
		.pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 30rpx;
			margin-top: 30rpx;
			padding: 30rpx;
			
			.page-btn {
				padding: 20rpx 40rpx;
				background: #007aff;
				color: #fff;
				border: none;
				border-radius: 10rpx;
				font-size: 26rpx;
				
				&:disabled {
					background: #ccc;
					color: #999;
				}
			}
		
			.page-info {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	.fab-btn {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background: #007aff;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0,122,255,0.3);
		z-index: 999;
		
		.fab-icon {
			color: #fff;
			font-size: 40rpx;
			font-weight: bold;
		}
	}
}
</style> 