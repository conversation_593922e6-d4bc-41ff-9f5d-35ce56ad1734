<template>
	<view class="page purchase-application">
		<nav-bar>{{ isEdit ? '修改采购申请' : '新增采购申请' }}</nav-bar>
		
		<view class="content">
			<form @submit="onSubmit">
				<!-- 基本信息 -->
				<view class="form-section">
					<view class="section-title">采购申请信息</view>
					
					<view class="form-item">
						<text class="label required">申请单号</text>
						<uni-easyinput 
							class="input" 
							:placeholder="isEdit ? '修改申请单号' : '系统自动生成或手动输入'" 
							v-model="formData.purchaseNo"
							:maxlength="50"
						/>
					</view>
					
					<view class="form-item">
						<text class="label required">申请人</text>
						<uni-easyinput 
							class="input" 
							placeholder="请输入申请人姓名" 
							v-model="formData.applicant"
							:maxlength="50"
						/>
					</view>
					
					<view class="form-item">
						<text class="label required">物品名称</text>
						<picker 
							:value="selectedItemIndex" 
							:range="itemOptions" 
							range-key="displayName"
							@change="onItemChange"
						>
							<view class="picker-value">
								{{ selectedItem ? selectedItem.displayName : '请选择物品' }}
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<text class="label">推荐供应商</text>
						<picker 
							:value="selectedSupplierIndex" 
							:range="supplierOptions" 
							range-key="supplierName"
							@change="onSupplierChange"
						>
							<view class="picker-value">
								{{ selectedSupplier ? selectedSupplier.supplierName : '请选择供应商' }}
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<text class="label required">申请数量</text>
						<view class="input-group">
							<uni-easyinput 
								class="input flex-input" 
								type="number" 
								placeholder="请输入数量" 
								v-model="formData.quantity"
								@input="calculateTotal"
							/>
							<text class="unit">{{ formData.unit || '件' }}</text>
						</view>
					</view>
					
					<view class="form-item">
						<text class="label">计量单位</text>
						<uni-easyinput 
							class="input" 
							placeholder="请输入计量单位" 
							v-model="formData.unit"
							:maxlength="10"
						/>
					</view>
					
					<view class="form-item">
						<text class="label required">预估单价</text>
						<view class="input-group">
							<uni-easyinput 
								class="input flex-input" 
								type="digit" 
								placeholder="请输入单价" 
								v-model="formData.price"
								@input="calculateTotal"
							/>
							<text class="unit">元</text>
						</view>
					</view>
					
					<view class="form-item">
						<text class="label">预估金额</text>
						<view class="total-amount">¥{{ totalAmount }}</view>
					</view>
					
					<view class="form-item">
						<text class="label required">期望到货时间</text>
						<picker 
							mode="date" 
							:value="formData.expectedDate" 
							@change="onDateChange"
						>
							<view class="picker-value">
								{{ formData.expectedDate || '请选择到货时间' }}
							</view>
						</picker>
					</view>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<button class="submit-btn draft" @click="saveDraft" type="button">保存草稿</button>
					<button class="submit-btn primary" @click="submitApplication" type="button">
						{{ isEdit ? '更新申请' : '提交申请' }}
					</button>
				</view>
			</form>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isEdit: false, // 是否为编辑模式
			editId: null, // 编辑时的订单ID
			formData: {
				purchaseNo: '',
				applicant: '',
				itemId: 0,
				supplierId: 0,
				quantity: '',
				unit: '件',
				price: '',
				expectedDate: '',
				supplierName: '' // 用于显示，不提交
			},
			itemOptions: [], // 物品列表
			selectedItemIndex: -1,
			selectedItem: null,
			loading: false,
			supplierOptions: [], // 供应商列表
			selectedSupplierIndex: -1,
			selectedSupplier: null
		}
	},
	computed: {
		totalAmount() {
			const quantity = parseFloat(this.formData.quantity) || 0
			const price = parseFloat(this.formData.price) || 0
			return (quantity * price).toFixed(2)
		}
	},
	onLoad(options) {
		// 检查是否为编辑模式
		if (options.id) {
			this.isEdit = true
			this.editId = options.id
			this.loadEditData(options.id)
		} else {
			this.isEdit = false
			this.generatePurchaseNo()
		}
		
		this.loadItemList()
		this.loadSupplierList()
	},
	methods: {
		// 加载编辑数据
		async loadEditData(id) {
			uni.showLoading({
				title: '加载中...'
			})
			
			try {
				const response = await uni.request({
					url: `http://192.168.1.87:8090/wms/purchase/getPurchaseOrderById?id=${id}`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					}
				})
				
				uni.hideLoading()
				
				if (response.data && response.data.code === 0 && response.data.data) {
					const data = response.data.data
					
					// 填充表单数据
					this.formData = {
						purchaseNo: data.purchaseNo || '',
						applicant: data.applicant || '',
						itemId: data.itemId || 0,
						supplierId: data.supplierId || 0,
						quantity: data.quantity?.toString() || '',
						unit: data.unit || '件',
						price: data.price?.toString() || '',
						expectedDate: data.expectedDate || '',
						supplierName: data.supplierName || ''
					}
					
					// 等待物品列表加载完成后设置选中项
					this.$nextTick(() => {
						this.setSelectedItem()
						this.setSelectedSupplier()
					})
				} else {
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					})
					// 加载失败，返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}
			} catch (error) {
				uni.hideLoading()
				console.error('加载编辑数据异常:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}
		},
		
		// 设置选中的物品
		setSelectedItem() {
			if (this.formData.itemId && this.itemOptions.length > 0) {
				const itemIndex = this.itemOptions.findIndex(item => item.id === this.formData.itemId)
				if (itemIndex !== -1) {
					this.selectedItemIndex = itemIndex
					this.selectedItem = this.itemOptions[itemIndex]
				}
			}
		},
		
		// 生成采购单号
		generatePurchaseNo() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0')
			this.formData.purchaseNo = `PO${year}${month}${day}${time}`
		},
		
		// 加载物品列表
		async loadItemList() {
			this.loading = true
			try {
				const response = await uni.request({
					url: 'http://192.168.1.87:8090/wms/item/listAll',
					method: 'GET',
					header: {
						'Content-Type': 'application/json'
					}
				})
				
				if (response.data && response.data.code === 0) {
					// 处理物品数据，组合名称和分类
					this.itemOptions = (response.data.data || []).map(item => ({
						...item,
						displayName: `${item.itemName} (${item.category})`
					}))
					console.log('物品列表加载成功:', this.itemOptions)
					
					// 如果是编辑模式，设置选中项
					if (this.isEdit) {
						this.setSelectedItem()
					}
				} else {
					console.error('加载物品列表失败:', response.data)
					uni.showToast({
						title: response.data?.message || '加载物品列表失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载物品列表异常:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 物品选择改变
		onItemChange(e) {
			const itemIndex = e.detail.value
			this.selectedItemIndex = itemIndex
			this.selectedItem = this.itemOptions[itemIndex]
			
			if (this.selectedItem) {
				this.formData.itemId = this.selectedItem.id
				this.formData.unit = this.selectedItem.unit || '件'
			}
		},
		
		// 供应商选择改变
		onSupplierChange(e) {
			const supplierIndex = e.detail.value
			this.selectedSupplierIndex = supplierIndex
			this.selectedSupplier = this.supplierOptions[supplierIndex]
			
			if (this.selectedSupplier) {
				this.formData.supplierId = this.selectedSupplier.id
				this.formData.supplierName = this.selectedSupplier.supplierName
			}
		},
		
		// 日期选择改变
		onDateChange(e) {
			this.formData.expectedDate = e.detail.value
		},
		
		calculateTotal() {
			// 自动计算总金额，已通过computed属性实现
		},
		
		validateForm() {
			if (!this.formData.purchaseNo.trim()) {
				uni.showToast({
					title: '请输入申请单号',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.applicant.trim()) {
				uni.showToast({
					title: '请输入申请人',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.itemId) {
				uni.showToast({
					title: '请选择物品',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.quantity || parseFloat(this.formData.quantity) <= 0) {
				uni.showToast({
					title: '请输入有效的申请数量',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.price || parseFloat(this.formData.price) <= 0) {
				uni.showToast({
					title: '请输入有效的预估单价',
					icon: 'none'
				})
				return false
			}
			
			if (!this.formData.expectedDate) {
				uni.showToast({
					title: '请选择期望到货时间',
					icon: 'none'
				})
				return false
			}
			
			return true
		},
		
		saveDraft() {
			// 保存草稿逻辑
			uni.showLoading({
				title: '保存中...'
			})
			
			setTimeout(() => {
				uni.hideLoading()
				uni.showToast({
					title: '草稿保存成功',
					icon: 'success'
				})
			}, 1000)
		},
		
		submitApplication() {
			if (!this.validateForm()) {
				return
			}
			
			const actionText = this.isEdit ? '更新' : '提交'
			uni.showModal({
				title: `确认${actionText}`,
				content: `确定要${actionText}这个采购申请吗？`,
				success: (res) => {
					if (res.confirm) {
						this.doSubmit()
					}
				}
			})
		},
		
		async doSubmit() {
			const actionText = this.isEdit ? '更新中...' : '提交中...'
			uni.showLoading({
				title: actionText
			})
			
			try {
				// 构建提交数据
				const submitData = {
					purchaseNo: this.formData.purchaseNo,
					applicant: this.formData.applicant,
					itemId: parseInt(this.formData.itemId),
					supplierId: this.formData.supplierId,
					quantity: parseFloat(this.formData.quantity),
					unit: this.formData.unit,
					price: parseFloat(this.formData.price),
					expectedDate: this.formData.expectedDate
				}
				
				// 根据模式设置id字段
				if (this.isEdit && this.editId) {
					// 编辑模式：传递实际的id值
					submitData.id = parseInt(this.editId)
				} else {
					// 新增模式：id设置为null
					submitData.id = null
				}
				
				console.log('提交数据:', submitData)
				
				const response = await uni.request({
					url: 'http://192.168.1.87:8090/wms/purchase/addOrUpdatePurchaseOrder',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: submitData
				})
				
				uni.hideLoading()
				
				if (response.data && response.data.code === 0) {
					const successText = this.isEdit ? '更新成功' : '申请提交成功'
					uni.showToast({
						title: successText,
						icon: 'success'
					})
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					const errorText = this.isEdit ? '更新失败' : '提交失败'
					uni.showToast({
						title: response.data?.message || errorText,
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('提交异常:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			}
		},
		
		// 加载供应商列表
		async loadSupplierList() {
			try {
				const response = await uni.request({
					url: 'http://192.168.1.87:8090/wms/supplier/listAll',
					method: 'GET',
					header: {
						'Content-Type': 'application/json'
					}
				})
				
				if (response.data && response.data.code === 0) {
					this.supplierOptions = response.data.data || []
					console.log('供应商列表加载成功:', this.supplierOptions)
					
					// 如果是编辑模式，设置选中的供应商
					if (this.isEdit) {
						this.$nextTick(() => {
							this.setSelectedSupplier()
						})
					}
				} else {
					console.error('加载供应商列表失败:', response.data)
					uni.showToast({
						title: response.data?.message || '加载供应商列表失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载供应商列表异常:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			}
		},
		
		// 设置选中的供应商
		setSelectedSupplier() {
			if (this.formData.supplierId && this.supplierOptions.length > 0) {
				const supplierIndex = this.supplierOptions.findIndex(supplier => supplier.id === this.formData.supplierId)
				if (supplierIndex !== -1) {
					this.selectedSupplierIndex = supplierIndex
					this.selectedSupplier = this.supplierOptions[supplierIndex]
				}
			}
		},
		
		scanZoneCode() {
			// 实现扫码逻辑
		},
		
		handleZoneCodeScan(code) {
			// 处理扫描到的区域码
		},
		
		confirmZoneSelection(zone) {
			// 确认选择区域
		}
	}
}
</script>

<style scoped lang="scss">
.purchase-application {
	.content {
		padding: 20rpx;
		padding-bottom: 40rpx;
		
		.form-section {
			background: #fff;
			margin-bottom: 20rpx;
			padding: 30rpx;
			border-radius: 10rpx;
			
			.section-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
				padding-bottom: 15rpx;
				border-bottom: 2px solid #007aff;
			}
			
			.form-item {
				margin-bottom: 30rpx;
				
				.label {
					display: block;
					font-size: 28rpx;
					color: #333;
					margin-bottom: 15rpx;
					
					&.required::after {
						content: '*';
						color: #ff3b30;
						margin-left: 5rpx;
					}
				}
				
				.input {
					width: 100%;
					border: 1px solid #ddd;
					border-radius: 8rpx;
					background: #fff;
					font-size: 26rpx;
					box-sizing: border-box;
				}
				
				// uni-easyinput 组件样式调整
				:deep(.uni-easyinput__content) {
					border: 1px solid #ddd;
					border-radius: 8rpx;
					background: #fff;
				}
				
				:deep(.uni-easyinput__content-input) {
					padding: 20rpx;
					font-size: 26rpx;
				}
				
				.input-group {
					display: flex;
					align-items: center;
					
					.flex-input {
						flex: 1;
						margin-right: 10rpx;
					}
					
					.unit {
						font-size: 24rpx;
						color: #666;
						width: 60rpx;
						text-align: center;
					}
				}
				
				.picker-value {
					width: 100%;
					padding: 20rpx;
					border: 1px solid #ddd;
					border-radius: 8rpx;
					background: #f8f8f8;
					font-size: 26rpx;
					color: #333;
					box-sizing: border-box;
					
					&:empty::before {
						content: '请选择';
						color: #999;
					}
				}
				
				.total-amount {
					font-size: 30rpx;
					font-weight: bold;
					color: #34c759;
					padding: 20rpx;
					background: #f0f8ff;
					border-radius: 8rpx;
					text-align: center;
				}
			}
		}
		
		.submit-section {
			display: flex;
			gap: 20rpx;
			margin-top: 40rpx;
			
			.submit-btn {
				flex: 1;
				padding: 25rpx;
				border: none;
				border-radius: 10rpx;
				font-size: 30rpx;
				font-weight: bold;
				
				&.draft {
					background: #f0f0f0;
					color: #666;
				}
				
				&.primary {
					background: #007aff;
					color: #fff;
				}
			}
		}
	}
}
</style> 