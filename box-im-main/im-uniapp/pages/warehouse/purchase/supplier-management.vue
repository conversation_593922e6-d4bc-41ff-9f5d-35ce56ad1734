<template>
	<view class="page supplier-management">
		<nav-bar>供应商管理</nav-bar>
		
		<view class="content">
			<view class="search-section">
				<view class="search-bar">
					<input 
						class="search-input" 
						placeholder="搜索供应商名称" 
						v-model="searchKeyword" 
						@confirm="onSearch"
					/>
					<button class="search-btn" @click="onSearch">搜索</button>
				</view>
			</view>
			
			<view class="stats-overview">
				<view class="stats-card">
					<text class="stats-title">总供应商</text>
					<text class="stats-value">{{ totalSuppliers }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">合作中</text>
					<text class="stats-value active">{{ activeSuppliers }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">暂停合作</text>
					<text class="stats-value suspended">{{ suspendedSuppliers }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">本月新增</text>
					<text class="stats-value new">{{ newSuppliers }}</text>
				</view>
			</view>
			
			<view class="supplier-list">
				<view v-if="loading" class="loading-state">
					<text class="loading-text">加载中...</text>
				</view>
				
				<view v-else-if="displaySupplierList.length === 0" class="empty-state">
					<text class="empty-text">暂无供应商信息</text>
				</view>
				
				<view v-else>
					<view 
						class="supplier-card" 
						v-for="supplier in displaySupplierList" 
						:key="supplier.id"
						@click="onSupplierClick(supplier)"
					>
						<view class="supplier-header">
							<view class="supplier-info">
								<text class="supplier-name">{{ supplier.supplierName }}</text>
								<text class="supplier-code">编号：{{ supplier.supplierCode }}</text>
							</view>
							<view class="supplier-status">
								<text class="status-text">{{ supplier.isDeleted === 0 ? '正常' : '已删除' }}</text>
							</view>
						</view>
						
						<view class="supplier-details">
							<view class="detail-row">
								<text class="detail-label">创建时间：</text>
								<text class="detail-value">{{ supplier.createTime }}</text>
							</view>
							<view class="detail-row">
								<text class="detail-label">更新时间：</text>
								<text class="detail-value">{{ supplier.updateTime }}</text>
							</view>
						</view>
						
						<view class="supplier-actions">
							<button class="action-btn" @click.stop="editSupplier(supplier)">编辑</button>
							<button class="action-btn primary" @click.stop="createOrder(supplier)">
								新建订单
							</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="totalPages > 1" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="onPageChange(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="onPageChange(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
		
		<!-- 新增供应商按钮 -->
		<view class="fab-btn" @click="addNewSupplier">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			supplierList: [],
			filteredSupplierList: [],
			displaySupplierList: [],
			loading: false,
			searchKeyword: '',
			currentPage: 1,
			pageSize: 10,
			totalPages: 0,
			totalSuppliers: 0,
			activeSuppliers: 0,
			suspendedSuppliers: 0,
			newSuppliers: 0
		}
	},
	onLoad() {
		this.loadSupplierList()
	},
	methods: {
		async loadSupplierList() {
			this.loading = true
			
			try {
				const response = await uni.request({
					url: `http://************:8090/wms/supplier/listAll`,
					method: 'GET',
					header: {
						'Content-Type': 'application/json'
					}
				})
				
				if (response.data.code === 0) {
					// 只使用API返回的真实数据
					this.supplierList = response.data.data.map(supplier => ({
						id: supplier.id,
						supplierName: supplier.supplierName,
						supplierCode: `SUP${String(supplier.id).padStart(4, '0')}`, // 生成供应商编码
						createTime: supplier.createTime,
						updateTime: supplier.updateTime,
						isDeleted: supplier.isDeleted
					}))
					
					// 计算统计数据
					this.calculateStats()
					
					// 应用筛选
					this.applyFilters()
				} else {
					uni.showToast({
						title: response.data.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载供应商列表失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		calculateStats() {
			this.totalSuppliers = this.supplierList.length
			// 由于API只返回基本信息，其他统计数据设为0或基于现有数据计算
			this.activeSuppliers = this.supplierList.filter(supplier => supplier.isDeleted === 0).length
			this.suspendedSuppliers = 0
			this.newSuppliers = 0
		},
		
		applyFilters() {
			let filteredList = [...this.supplierList]
			
			// 按搜索关键词筛选
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase()
				filteredList = filteredList.filter(supplier => 
					supplier.supplierName.toLowerCase().includes(keyword) ||
					supplier.supplierCode.toLowerCase().includes(keyword)
				)
			}
			
			// 更新显示列表和分页
			this.filteredSupplierList = filteredList
			this.totalPages = Math.ceil(filteredList.length / this.pageSize)
			this.currentPage = 1
			this.updateDisplayList()
		},
		
		updateDisplayList() {
			const startIndex = (this.currentPage - 1) * this.pageSize
			const endIndex = startIndex + this.pageSize
			this.displaySupplierList = this.filteredSupplierList.slice(startIndex, endIndex)
		},
		
		onSearch() {
			this.applyFilters()
		},
		
		onPageChange(page) {
			if (page >= 1 && page <= this.totalPages) {
				this.currentPage = page
				this.updateDisplayList()
			}
		},
		
		onSupplierClick(supplier) {
			this.viewSupplierDetail(supplier)
		},
		
		viewSupplierDetail(supplier) {
			const content = `供应商编号：${supplier.supplierCode}
供应商名称：${supplier.supplierName}
创建时间：${supplier.createTime}
更新时间：${supplier.updateTime}
状态：${supplier.isDeleted === 0 ? '正常' : '已删除'}`
			
			uni.showModal({
				title: '供应商详情',
				content: content,
				showCancel: false
			})
		},
		
		editSupplier(supplier) {
			uni.showToast({
				title: '编辑功能开发中',
				icon: 'none'
			})
		},
		
		createOrder(supplier) {
			uni.showModal({
				title: '新建订单',
				content: `是否为 ${supplier.supplierName} 创建新订单？`,
				success: (res) => {
					if (res.confirm) {
						uni.navigateTo({
							url: `/pages/warehouse/purchase/purchase-application?supplierId=${supplier.id}&supplierName=${supplier.supplierName}`
						})
					}
				}
			})
		},
		
		addNewSupplier() {
			uni.showToast({
				title: '新增供应商功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped lang="scss">
.supplier-management {
	.content {
		padding: 20rpx;
		padding-bottom: 120rpx;
		
		.search-section {
			background: #fff;
			padding: 20rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			
			.search-bar {
				display: flex;
				gap: 20rpx;
				margin-bottom: 20rpx;
				
				.search-input {
					flex: 1;
					padding: 20rpx;
					border: 1px solid #ddd;
					border-radius: 10rpx;
					background: #f8f8f8;
				}
				
				.search-btn {
					padding: 20rpx 30rpx;
					background: #007aff;
					color: #fff;
					border: none;
					border-radius: 10rpx;
				}
			}
		}
		
		.stats-overview {
			display: flex;
			gap: 15rpx;
			flex-wrap: wrap;
			margin-bottom: 20rpx;
			
			.stats-card {
				flex: 1;
				min-width: 160rpx;
				background: #fff;
				padding: 20rpx;
				border-radius: 10rpx;
				text-align: center;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.stats-title {
					display: block;
					font-size: 22rpx;
					color: #666;
					margin-bottom: 8rpx;
				}
				
				.stats-value {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					
					&.active {
						color: #34c759;
					}
					
					&.suspended {
						color: #ff9500;
					}
					
					&.new {
						color: #007aff;
					}
				}
			}
		}
		
		.supplier-list {
			.loading-state, .empty-state {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 400rpx;
			
				.loading-text, .empty-text {
					color: #999;
					font-size: 28rpx;
				}
			}
			
			.supplier-card {
				background: #fff;
				margin-bottom: 20rpx;
				padding: 30rpx;
				border-radius: 10rpx;
				box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
				
				.supplier-header {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin-bottom: 20rpx;
					
					.supplier-info {
						flex: 1;
						
						.supplier-name {
							display: block;
							font-size: 32rpx;
							font-weight: bold;
							color: #333;
							margin-bottom: 5rpx;
						}
						
						.supplier-code {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.supplier-status {
						padding: 6rpx 16rpx;
						border-radius: 15rpx;
						font-size: 22rpx;
						
						&.status-active {
							background: #34c759;
							color: #fff;
						}
						
						&.status-suspended {
							background: #ff9500;
							color: #fff;
						}
						
						&.status-terminated {
							background: #ff3b30;
							color: #fff;
						}
					}
				}
				
				.supplier-details {
					background: #f8f8f8;
					padding: 20rpx;
					border-radius: 8rpx;
					margin-bottom: 20rpx;
					
					.detail-row {
						display: flex;
						margin-bottom: 8rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.detail-label {
							font-size: 24rpx;
							color: #666;
							width: 100rpx;
						}
						
						.detail-value {
							font-size: 24rpx;
							color: #333;
							flex: 1;
						}
					}
				}
				
				.supplier-actions {
					display: flex;
					gap: 15rpx;
					border-top: 1px solid #f0f0f0;
					padding-top: 20rpx;
					
					.action-btn {
						flex: 1;
						padding: 15rpx 20rpx;
						border: 1px solid #ddd;
						border-radius: 8rpx;
						background: #fff;
						color: #666;
						font-size: 22rpx;
						text-align: center;
						
						&.primary {
							background: #007aff;
							color: #fff;
							border-color: #007aff;
						}
					}
				}
			}
		}
		
		.pagination {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 30rpx;
			margin-top: 30rpx;
			padding: 30rpx;
			
			.page-btn {
				padding: 20rpx 40rpx;
				background: #007aff;
				color: #fff;
				border: none;
				border-radius: 10rpx;
				font-size: 26rpx;
				
				&:disabled {
					background: #ccc;
					color: #999;
				}
			}
		
			.page-info {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	.fab-btn {
		position: fixed;
		right: 30rpx;
		bottom: 30rpx;
		width: 100rpx;
		height: 100rpx;
		background: #007aff;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0,122,255,0.3);
		z-index: 999;
		
		.fab-icon {
			color: #fff;
			font-size: 40rpx;
			font-weight: bold;
		}
	}
}
</style> 