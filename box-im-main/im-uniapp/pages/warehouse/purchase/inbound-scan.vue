<template>
	<view class="page inbound-scan">
		<nav-bar>入库扫码</nav-bar>
		
		<view class="content">
			<!-- 扫码区域 -->
			<view class="scan-section">
				<view class="scan-container">
					<view class="scan-frame">
						<view class="scan-line" :class="{ scanning: isScanning }"></view>
						<view class="scan-corners">
							<view class="corner top-left"></view>
							<view class="corner top-right"></view>
							<view class="corner bottom-left"></view>
							<view class="corner bottom-right"></view>
						</view>
					</view>
					<text class="scan-tip">请将二维码/条形码放入框内扫描</text>
				</view>
				
				<view class="scan-actions">
					<button class="scan-btn primary" @click="startScan" :disabled="isScanning">
						{{ isScanning ? '扫描中...' : '开始扫码' }}
					</button>
				</view>
			</view>
			
			<!-- 扫码结果 -->
			<view class="scan-result" v-if="scanResult">
				<view class="result-header">
					<text class="result-title">扫码结果</text>
					<button class="clear-btn" @click="clearResult">清除</button>
				</view>
				<view class="result-content">
					<text class="result-text">{{ scanResult }}</text>
					<text class="result-type" v-if="scanLabelType">标签类型：{{ getLabelTypeText(scanLabelType) }}</text>
				</view>
			</view>
			
			<!-- 区域绑定信息 -->
			<view class="zone-info" v-if="zoneInfo">
				<view class="zone-header">
					<text class="zone-title">区域信息</text>
					<view class="zone-status" :class="{ 'bound': zoneInfo.zoneBound }">
						<text class="status-text">{{ zoneInfo.zoneBound ? '已绑定' : '未绑定' }}</text>
					</view>
				</view>
				
				<view class="zone-content" v-if="zoneInfo.zoneBound">
					<view class="zone-item">
						<text class="zone-label">区域代码：</text>
						<text class="zone-value">{{ zoneInfo.zoneCode }}</text>
					</view>
					<view class="zone-item">
						<text class="zone-label">区域名称：</text>
						<text class="zone-value">{{ zoneInfo.zoneName }}</text>
					</view>
				</view>
				
				<!-- 区域选择 -->
				<view class="zone-selection" v-if="!zoneInfo.zoneBound">
					<view class="selection-header">
						<text class="selection-title">选择存储区域</text>
					</view>
					
					<view class="selection-actions">
						<button class="selection-btn primary" @click="showZoneSelector">
							手动选择区域
						</button>
						<button class="selection-btn secondary" @click="scanZoneCode">
							扫码选择区域
						</button>
					</view>
				</view>
			</view>
			
			<!-- 订单信息 -->
			<view class="order-info" v-if="orderInfo">
				<view class="info-header">
					<text class="info-title">采购订单信息</text>
					<view class="status-badge" :class="getStatusClass(orderInfo.status)">
						<text class="status-text">{{ getStatusText(orderInfo.status) }}</text>
					</view>
				</view>
				
				<view class="info-content">
					<view class="info-item">
						<text class="info-label">订单号：</text>
						<text class="info-value">{{ orderInfo.purchaseOrderNo }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">区域代码：</text>
						<text class="info-value">{{ orderInfo.zoneCode || '未绑定' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">二维码：</text>
						<text class="info-value">{{ orderInfo.qrCode || '无' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">操作员：</text>
						<text class="info-value">{{ orderInfo.operator || '无' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">备注：</text>
						<text class="info-value">{{ orderInfo.remark || '无' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">创建时间：</text>
						<text class="info-value">{{ formatTime(orderInfo.createTime) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">更新时间：</text>
						<text class="info-value">{{ formatTime(orderInfo.updateTime) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 入库历史 -->
			<view class="inbound-history" v-if="orderInfo && historyList.length > 0">
				<view class="history-header">
					<text class="history-title">入库历史</text>
				</view>
				
				<view class="history-list">
					<view class="history-item" v-for="item in historyList" :key="item.id">
						<view class="history-info">
							<text class="history-quantity">{{ item.quantity }} {{ orderInfo.unit }}</text>
							<text class="history-location" v-if="item.location">{{ item.location }}</text>
							<text class="history-time">{{ formatTime(item.createTime) }}</text>
						</view>
						<text class="history-operator">{{ item.operatorName }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 手动输入弹窗 -->
		<view class="manual-input-modal" v-if="showManualInput" @click="closeManualInput">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">手动输入</text>
					<text class="close-btn" @click="closeManualInput">×</text>
				</view>
				
				<view class="modal-body">
					<view class="input-item">
						<text class="input-label">请输入订单号或物品编码：</text>
						<input 
							class="modal-input" 
							placeholder="请输入订单号或物品编码" 
							v-model="manualCode"
							@confirm="handleManualInput"
						/>
					</view>
				</view>
				
				<view class="modal-footer">
					<button class="modal-btn cancel" @click="closeManualInput">取消</button>
					<button class="modal-btn confirm" @click="handleManualInput">确认</button>
				</view>
			</view>
		</view>
		
		<!-- 区域选择弹窗 -->
		<view class="zone-selector-modal" v-if="showZoneSelectorModal" @click="closeZoneSelector">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择存储区域</text>
					<text class="close-btn" @click="closeZoneSelector">×</text>
				</view>
				
				<view class="modal-body">
					<view class="zone-list">
						<view 
							class="zone-option" 
							v-for="zone in availableZones" 
							:key="zone.id"
							:class="{ active: selectedZone && selectedZone.id === zone.id }"
							@click="selectZone(zone)"
						>
							<text class="zone-code">{{ zone.zoneCode }}</text>
							<text class="zone-name">{{ zone.zoneName }}</text>
						</view>
					</view>
				</view>
				
				<view class="modal-footer">
					<button class="modal-btn cancel" @click="closeZoneSelector">取消</button>
					<button class="modal-btn confirm" @click="confirmZoneSelection" :disabled="!selectedZone">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isScanning: false,
			flashOn: false,
			scanResult: '',
			scanLabelType: '', // 扫码标签类型
			zoneInfo: null, // 区域信息
			orderInfo: null,
			historyList: [],
			showManualInput: false,
			manualCode: '',
			showZoneSelectorModal: false, // 重命名以避免与方法名冲突
			availableZones: [], // 可选区域列表
			selectedZone: null, // 选中的区域
			currentPurchaseNo: '', // 当前扫描的采购单号，用于区域绑定
			orderExists: true, // 标记订单存在
			userStore: null // 添加userStore
		}
	},
	
	onLoad() {
		console.log('=== 页面 onLoad 开始 ===')
		
		// 初始化 userStore
		this.userStore = this.userStore || getApp().$vm.userStore
		
		console.log('this.userStore:', this.userStore)
		console.log('this.$store:', this.$store)
		console.log('全局属性 userStore:', this.userStore)
		console.log('全局属性检查:', {
			userStore: this.userStore,
			chatStore: this.chatStore,
			friendStore: this.friendStore,
			groupStore: this.groupStore,
			configStore: this.configStore
		})
		
		// 确保用户信息已加载
		this.ensureUserInfo()
		this.loadAvailableZones()
		
		console.log('=== 页面 onLoad 完成 ===')
	},
	
	methods: {
		// 确保用户信息已加载
		async ensureUserInfo() {
			console.log('=== 开始确保用户信息已加载 ===')
			
			// 如果 userStore 不存在，尝试从全局获取
			if (!this.userStore) {
				this.userStore = getApp().$vm.userStore
				console.log('从全局获取 userStore:', this.userStore)
			}
			
			console.log('当前 userStore:', this.userStore)
			console.log('当前 userStore.userInfo:', this.userStore?.userInfo)
			console.log('当前用户手机号:', this.userStore?.userInfo?.phone)
			
			if (!this.userStore?.userInfo?.phone) {
				console.log('用户手机号为空，尝试加载用户信息...')
				try {
					if (this.userStore?.loadUser) {
						console.log('调用 userStore.loadUser()...')
						await this.userStore.loadUser()
						console.log('loadUser 完成，重新检查用户信息:')
						console.log('userStore.userInfo:', this.userStore.userInfo)
						console.log('用户手机号:', this.userStore.userInfo?.phone)
					} else {
						console.error('userStore.loadUser 方法不存在')
					}
				} catch (error) {
					console.error('加载用户信息失败:', error)
				}
			} else {
				console.log('用户手机号已存在:', this.userStore.userInfo.phone)
			}
			console.log('=== 用户信息确保完成 ===')
		},
		
		// 开始扫码
		startScan() {
			this.isScanning = true
			
			// 调用uni-app的扫码API
			uni.scanCode({
				success: (res) => {
					this.scanResult = res.result
					this.handleScanResult(res.result)
				},
				fail: (error) => {
					console.error('扫码失败:', error)
					uni.showToast({
						title: '扫码失败',
						icon: 'none'
					})
				},
				complete: () => {
					this.isScanning = false
				}
			})
		},
		
		// 处理扫码结果
		handleScanResult(result) {
			this.scanResult = result
			
			// 解析二维码内容，判断label_type
			this.parseScanResult(result)
		},
		
		// 解析扫码结果
		parseScanResult(result) {
			try {
				let scanData = {}
				
				// 首先尝试解析竖线分隔的键值对格式: label_type:warehouse_zone|zone_code:TEST_ZONE_A01_001
				if (result.includes('|') && result.includes(':')) {
					const pairs = result.split('|')
					pairs.forEach(pair => {
						const [key, value] = pair.split(':')
						if (key && value) {
							scanData[key.trim()] = value.trim()
						}
					})
				} else {
					// 尝试解析JSON格式的二维码
					try {
						scanData = JSON.parse(result)
					} catch (jsonError) {
						// 既不是键值对格式也不是JSON格式，按原逻辑处理
						this.searchOrderByCode(result)
						return
					}
				}
				
				if (scanData.label_type) {
					this.scanLabelType = scanData.label_type
					
					// 根据label_type进行不同处理
					switch (scanData.label_type) {
						case 'warehouse_zone':
							this.handleWarehouseZoneScan(scanData)
							break
						case 'purchase':
							this.handlePurchaseScan(scanData)
							break
						case 'production':
							this.handleProductionScan(scanData)
							break
						case 'mixed_outbound':
							this.handleMixedOutboundScan(scanData)
							break
						default:
							this.handleUnknownLabelType(scanData)
							break
					}
				} else {
					// 没有label_type，按原逻辑处理
					this.searchOrderByCode(result)
				}
			} catch (error) {
				console.error('解析扫码结果失败:', error)
				// 解析失败，按原逻辑处理
				this.searchOrderByCode(result)
			}
		},
		
		// 处理仓库区域二维码
		async handleWarehouseZoneScan(scanData) {
			if (scanData.zone_code) {
				// 如果有当前采购单号，则进行绑定
				if (this.currentPurchaseNo) {
					await this.writePurchaseZone(this.currentPurchaseNo, scanData.zone_code, this.orderExists)
				} else {
					// 直接设置区域信息
					this.zoneInfo = {
						zoneBound: true,
						zoneCode: scanData.zone_code,
						zoneName: scanData.zone_name || '仓库区域'
					}
					
					uni.showToast({
						title: `已识别区域: ${scanData.zone_code}`,
						icon: 'success'
					})
				}
			} else {
				uni.showToast({
					title: '仓库区域码无效',
					icon: 'none'
				})
			}
		},
		
		// 处理采购部二维码
		handlePurchaseScan(scanData) {
			console.log('处理采购部二维码 - 扫码数据:', scanData)
			
			// 验证扫码数据是否包含必要信息
			if (!scanData || typeof scanData !== 'object') {
				uni.showToast({
					title: '采购部二维码数据无效',
					icon: 'none'
				})
				return
			}
			
			// 调用采购入库列表接口检查区域绑定
			this.checkPurchaseInboundBinding(scanData)
		},
		
		// 处理生产车间二维码
		handleProductionScan(scanData) {
			uni.showToast({
				title: '生产车间标签',
				icon: 'none'
			})
			// 可以在这里处理生产车间相关逻辑
		},
	
	// 处理出库二维码
	handleMixedOutboundScan(scanData) {
		uni.showToast({
			title: '出库标签',
			icon: 'none'
		})
		// 可以在这里处理出库相关逻辑
	},
	
	// 处理未知标签类型
	handleUnknownLabelType(scanData) {
		uni.showToast({
			title: '未知标签类型',
			icon: 'none'
		})
	},
	
	// 检查采购入库绑定情况
	async checkPurchaseInboundBinding(scanData) {
		try {
			uni.showLoading({ title: '检查区域绑定...' })
			
			// 提取采购订单号，优先级：purchase_no > purchaseOrderNo > purchase_order_no > code
			const purchaseOrderNo = scanData.purchase_no || 
								   scanData.purchaseOrderNo || 
								   scanData.purchase_order_no || 
								   scanData.code
			
			console.log('检查区域绑定 - 扫码数据:', scanData)
			console.log('检查区域绑定 - 采购订单号:', purchaseOrderNo)
			
			if (!purchaseOrderNo) {
				uni.showToast({
					title: '无法获取采购订单号',
					icon: 'none'
				})
				return
			}
			
			// 保存当前采购单号，用于后续区域绑定
			this.currentPurchaseNo = purchaseOrderNo
			
			console.log('检查区域绑定 - 请求参数: pageNum=1, pageSize=10')
			
			const response = await uni.request({
				url: `http://192.168.1.87:8090/wms/purchaseInBound/getPurchaseInboundList?pageNum=1&pageSize=10`,
				method: 'POST'
			})
			
			console.log('检查区域绑定 - 响应数据:', response)
			
			if (response.data && response.data.code === 0) {
				const data = response.data.data
				
				// 检查是否有记录，并查找匹配的订单
				if (data && data.records && data.records.length > 0) {
					const matchedOrder = data.records.find(record => 
						record.purchaseOrderNo === purchaseOrderNo
					)
					
					if (matchedOrder) {
						console.log('检查区域绑定 - 找到匹配订单:', matchedOrder)
						
						// 标记订单存在
						this.orderExists = true
						
						// 检查zoneCode字段是否有数据（区域代码字段）
						if (matchedOrder.zoneCode) {
							// 已绑定区域
							this.zoneInfo = {
								zoneBound: true,
								zoneCode: matchedOrder.zoneCode,
								zoneName: '仓库区域'
							}
							
							uni.showToast({
								title: `已绑定区域: ${matchedOrder.zoneCode}`,
								icon: 'success'
							})
							
							// 设置订单信息
							this.orderInfo = matchedOrder
						} else {
							// 找到订单但未绑定区域，需要选择区域
							this.zoneInfo = {
								zoneBound: false
							}
							
							// 设置订单信息
							this.orderInfo = matchedOrder
							
							uni.showToast({
								title: '该订单未绑定存储区域，请扫描区域二维码进行绑定',
								icon: 'none'
							})
						}
					} else {
						// 没有找到匹配的订单
						this.orderExists = false
						this.zoneInfo = null // 清除区域信息
						
						uni.showToast({
							title: '没有找到采购信息，无法绑定区域编码',
							icon: 'none',
							duration: 3000
						})
					}
				} else {
					// 没有找到相关记录，直接提示无法绑定
					this.orderExists = false
					this.zoneInfo = null // 清除区域信息
					
					uni.showToast({
						title: '没有找到采购信息，无法绑定区域编码',
						icon: 'none',
						duration: 3000
					})
				}
			} else {
				const errorMessage = response.data?.message || response.data?.msg || '检查区域绑定失败'
				console.error('检查区域绑定失败 - 服务器响应:', response.data)
				uni.showToast({
					title: errorMessage,
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('检查区域绑定失败 - 网络错误:', error)
			uni.showToast({
				title: `网络错误: ${error.message || '请检查网络连接'}`,
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	},
	
	// 根据扫码结果查询订单信息
	async searchOrderByCode(code) {
		try {
			uni.showLoading({ title: '查询中...' })
			
			const response = await uni.request({
				url: `http://192.168.1.87:8090/wms/purchaseInBound/getPurchaseInboundList?pageNum=1&pageSize=10`,
				method: 'POST'
			})
			
			if (response.data && response.data.code === 0) {
				const data = response.data.data
				
				// 检查是否有记录，并根据采购订单号筛选
				if (data && data.records && data.records.length > 0) {
					// 查找匹配的订单
					const matchedOrder = data.records.find(record => 
						record.purchaseOrderNo === code
					)
					
					if (matchedOrder) {
						this.orderInfo = matchedOrder
						
						// 如果订单有区域代码，显示区域信息
						if (matchedOrder.zoneCode) {
							this.zoneInfo = {
								zoneBound: true,
								zoneCode: matchedOrder.zoneCode,
								zoneName: '仓库区域'
							}
						}
					} else {
						uni.showToast({
							title: '未找到匹配的订单',
							icon: 'none'
						})
					}
				} else {
					uni.showToast({
						title: '未找到相关订单',
						icon: 'none'
					})
				}
			} else {
				uni.showToast({
					title: response.data?.message || response.data?.msg || '查询失败',
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('查询订单失败:', error)
			uni.showToast({
				title: '查询失败',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	},
	
	// 加载可用区域列表
	async loadAvailableZones() {
		try {
			const response = await uni.request({
				url: 'http://192.168.1.87:8090/wms/purchaseInBound/getZoneCodes',
				method: 'GET'
			})
			
			console.log('加载区域列表响应:', response)
			
			if (response.data && response.data.code === 0) {
				// 转换数据格式以适配现有的界面结构
				this.availableZones = (response.data.data || []).map(zone => ({
					id: zone.zoneCode, // 使用zoneCode作为id
					zoneCode: zone.zoneCode,
					zoneName: zone.zoneName
				}))
				console.log('可用区域列表:', this.availableZones)
			} else {
				console.error('获取区域列表失败:', response.data)
			}
		} catch (error) {
			console.error('加载区域列表失败:', error)
		}
	},
	
	// 显示区域选择器
	showZoneSelector() {
		this.showZoneSelectorModal = true
		// 重新加载区域列表
		this.loadAvailableZones()
	},
	
	// 关闭区域选择器
	closeZoneSelector() {
		this.showZoneSelectorModal = false
		this.selectedZone = null
	},
	
	// 选择区域
	selectZone(zone) {
		this.selectedZone = zone
	},
	
	// 确认区域选择
	async confirmZoneSelection() {
		if (!this.selectedZone) {
			uni.showToast({
				title: '请选择区域',
				icon: 'none'
			})
			return
		}
		
		if (!this.currentPurchaseNo) {
			uni.showToast({
				title: '缺少采购单号信息',
				icon: 'none'
			})
			return
		}
		
		try {
			uni.showLoading({ title: '绑定区域中...' })
			
			console.log('=== confirmZoneSelection 开始 ===')
			console.log('选中的区域:', this.selectedZone)
			console.log('当前采购单号:', this.currentPurchaseNo)
			
			// 确保用户信息已加载
			console.log('调用 ensureUserInfo 前的状态:')
			console.log('userStore:', this.userStore)
			console.log('userStore.userInfo:', this.userStore?.userInfo)
			console.log('phone:', this.userStore?.userInfo?.phone)
			
			await this.ensureUserInfo()
			
			console.log('调用 ensureUserInfo 后的状态:')
			console.log('userStore:', this.userStore)
			console.log('userStore.userInfo:', this.userStore?.userInfo)
			console.log('phone:', this.userStore?.userInfo?.phone)
			
			// 获取当前用户手机号作为操作员
			const operator = this.userStore?.userInfo?.phone || ''
			console.log('获取到的 operator:', operator)
			console.log('operator 类型:', typeof operator)
			console.log('operator 长度:', operator.length)
			
			if (!operator) {
				console.error('无法获取操作员信息 - userStore 状态:')
				console.error('userStore:', this.userStore)
				console.error('userStore.userInfo:', this.userStore?.userInfo)
				uni.showToast({
					title: '无法获取操作员信息，请重新登录',
					icon: 'none'
				})
				return
			}
			
			console.log('手动选择区域绑定 - 参数:', {
				purchaseOrderNo: this.currentPurchaseNo,
				zoneCode: this.selectedZone.zoneCode,
				operator: operator
			})
			
			// 调用bindPurchaseZone接口
			const response = await uni.request({
				url: 'http://192.168.1.87:8090/wms/purchaseInBound/bindPurchaseZone',
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				data: {
					purchaseOrderNo: this.currentPurchaseNo,
					zoneCode: this.selectedZone.zoneCode,
					operator: operator
				}
			})
			
			console.log('手动选择区域绑定 - 响应:', response)
			
			if (response.data && response.data.code === 0) {
				// 绑定成功，更新区域信息
				this.zoneInfo = {
					zoneBound: true,
					zoneCode: this.selectedZone.zoneCode,
					zoneName: this.selectedZone.zoneName
				}
				
				this.closeZoneSelector()
				
				uni.showToast({
					title: '区域绑定成功',
					icon: 'success'
				})
				
				// 重新查询订单信息
				this.searchOrderByCode(this.currentPurchaseNo)
			} else {
				const errorMessage = response.data?.message || response.data?.msg || '区域绑定失败'
				uni.showToast({
					title: errorMessage,
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('区域绑定失败:', error)
			uni.showToast({
				title: '网络错误',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	},
	
	// 扫码选择区域
	scanZoneCode() {
		uni.scanCode({
			success: (res) => {
				// 处理区域二维码扫描结果
				this.handleZoneCodeScan(res.result)
			},
			fail: (error) => {
				console.error('扫码失败:', error)
				uni.showToast({
					title: '扫码失败',
					icon: 'none'
				})
			}
		})
	},
	
	// 处理区域码扫描结果
	async handleZoneCodeScan(result) {
		try {
			let zoneData = {}
			
			// 解析竖线分隔的键值对格式: label_type:warehouse_zone|zone_code:TEST_ZONE_A01_001
			if (result.includes('|') && result.includes(':')) {
				const pairs = result.split('|')
				pairs.forEach(pair => {
					const [key, value] = pair.split(':')
					if (key && value) {
						zoneData[key.trim()] = value.trim()
					}
				})
			} else {
				// 尝试解析JSON格式
				try {
					zoneData = JSON.parse(result)
				} catch (jsonError) {
					uni.showToast({
						title: '区域码格式错误',
						icon: 'none'
					})
					return
				}
			}
			
			console.log('区域码扫描结果:', zoneData)
			
			// 检查是否是仓库区域标签
			if (zoneData.label_type === 'warehouse_zone' && zoneData.zone_code) {
				// 如果有当前采购单号，则进行绑定
				if (this.currentPurchaseNo) {
					await this.writePurchaseZone(this.currentPurchaseNo, zoneData.zone_code, this.orderExists)
				} else {
					// 自动绑定扫描到的区域（在区域选择弹窗中使用）
					const zone = {
						id: zoneData.zone_id || zoneData.zone_code, // 如果没有zone_id，使用zone_code作为id
						zoneCode: zoneData.zone_code,
						zoneName: zoneData.zone_name || '仓库区域'
					}
					
					this.selectedZone = zone
					this.confirmZoneSelection()
				}
			} else {
				uni.showToast({
					title: '无效的区域码',
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('处理区域码扫描失败:', error)
			uni.showToast({
				title: '区域码处理失败',
				icon: 'none'
			})
		}
	},
	
	// 写入采购区域绑定
	async writePurchaseZone(purchaseOrderNo, zoneCode, orderExists = false) {
		try {
			uni.showLoading({ title: '绑定区域中...' })
			
			console.log('=== writePurchaseZone 开始 ===')
			console.log('写入采购区域绑定 - 参数:', { purchaseOrderNo, zoneCode, orderExists })
			
			// 确保用户信息已加载
			console.log('调用 ensureUserInfo 前的状态:')
			console.log('userStore:', this.userStore)
			console.log('userStore.userInfo:', this.userStore?.userInfo)
			console.log('phone:', this.userStore?.userInfo?.phone)
			
			await this.ensureUserInfo()
			
			console.log('调用 ensureUserInfo 后的状态:')
			console.log('userStore:', this.userStore)
			console.log('userStore.userInfo:', this.userStore?.userInfo)
			console.log('phone:', this.userStore?.userInfo?.phone)
			
			// 获取当前用户手机号作为操作员
			const operator = this.userStore?.userInfo?.phone || ''
			console.log('获取到的 operator:', operator)
			console.log('operator 类型:', typeof operator)
			console.log('operator 长度:', operator.length)
			
			if (!operator) {
				console.error('无法获取操作员信息 - userStore 状态:')
				console.error('userStore:', this.userStore)
				console.error('userStore.userInfo:', this.userStore?.userInfo)
				uni.showToast({
					title: '无法获取操作员信息，请重新登录',
					icon: 'none'
				})
				return
			}
			
			// 使用新的bindPurchaseZone接口，总是传递purchaseOrderNo和zoneCode
			const requestData = {
				purchaseOrderNo: purchaseOrderNo,
				zoneCode: zoneCode,
				operator: operator
			}
			
			console.log('写入采购区域绑定 - 请求数据:', requestData)
			
			const response = await uni.request({
				url: 'http://192.168.1.87:8090/wms/purchaseInBound/bindPurchaseZone',
				method: 'POST',
				header: {
					'Content-Type': 'application/x-www-form-urlencoded'
				},
				data: requestData
			})
			
			console.log('写入采购区域绑定 - 响应:', response)
			
			if (response.data && response.data.code === 0) {
				// 绑定成功，更新区域信息
				this.zoneInfo = {
					zoneBound: true,
					zoneCode: zoneCode,
					zoneName: '仓库区域'
				}
				
				uni.showToast({
					title: '区域绑定成功',
					icon: 'success'
				})
				
				// 重新查询订单信息
				this.searchOrderByCode(purchaseOrderNo)
			} else {
				const errorMessage = response.data?.message || response.data?.msg || '区域绑定失败'
				uni.showToast({
					title: errorMessage,
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('写入采购区域绑定失败:', error)
			uni.showToast({
				title: '网络错误',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	},
	
	// 获取标签类型文本
	getLabelTypeText(labelType) {
		const typeMap = {
			'warehouse_zone': '仓库区域',
			'purchase': '采购部',
			'production': '生产车间',
			'mixed_outbound': '出库'
		}
		return typeMap[labelType] || '未知类型'
	},
	
	// 获取状态样式类
	getStatusClass(status) {
		const statusMap = {
			0: 'pending',
			1: 'partial', 
			2: 'completed'
		}
		return statusMap[status] || 'pending'
	}, 
	
	// 获取状态文本
	getStatusText(status) {
		const statusMap = {
			0: '待处理',
			1: '处理中',
			2: '已完成'
		}
		return statusMap[status] || '待处理'
	},
	
	// 格式化时间
	formatTime(timeString) {
		if (!timeString) return ''
		const date = new Date(timeString)
		return date.toLocaleString('zh-CN')
	},
	
	// 清除扫码结果
	clearResult() {
		this.scanResult = ''
		this.scanLabelType = ''
		this.zoneInfo = null
		this.orderInfo = null
		this.historyList = []
		this.currentPurchaseNo = '' // 清除当前采购单号
		this.orderExists = false // 重置订单存在状态
	},
	
	// 手动输入
	manualInput() {
		this.showManualInput = true
	},
	
	// 关闭手动输入弹窗
	closeManualInput() {
		this.showManualInput = false
		this.manualCode = ''
	},
	
	// 处理手动输入
	handleManualInput() {
		if (!this.manualCode.trim()) {
			uni.showToast({
				title: '请输入订单号或物品编码',
				icon: 'none'
			})
			return
		}
		
		this.handleScanResult(this.manualCode.trim())
		this.closeManualInput()
	},
	
	// 切换闪光灯
	toggleFlash() {
		this.flashOn = !this.flashOn
		// 这里可以调用设备API控制闪光灯
	}
	}
}
</script>

<style scoped lang="scss">
.inbound-scan {
	.content {
		padding: 20rpx;
		
		.scan-section {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.scan-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-bottom: 30rpx;
				
				.scan-frame {
					position: relative;
					width: 400rpx;
					height: 400rpx;
					border: 2px solid #007aff;
					border-radius: 10rpx;
					margin-bottom: 20rpx;
					overflow: hidden;
					
					.scan-line {
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 4rpx;
						background: linear-gradient(90deg, transparent, #007aff, transparent);
						
						&.scanning {
							animation: scanning 2s linear infinite;
						}
					}
					
					.scan-corners {
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						
						.corner {
							position: absolute;
							width: 40rpx;
							height: 40rpx;
							
							&.top-left {
								top: -2rpx;
								left: -2rpx;
								border-top: 4rpx solid #007aff;
								border-left: 4rpx solid #007aff;
							}
							
							&.top-right {
								top: -2rpx;
								right: -2rpx;
								border-top: 4rpx solid #007aff;
								border-right: 4rpx solid #007aff;
							}
							
							&.bottom-left {
								bottom: -2rpx;
								left: -2rpx;
								border-bottom: 4rpx solid #007aff;
								border-left: 4rpx solid #007aff;
							}
							
							&.bottom-right {
								bottom: -2rpx;
								right: -2rpx;
								border-bottom: 4rpx solid #007aff;
								border-right: 4rpx solid #007aff;
							}
						}
					}
				}
				
				.scan-tip {
					font-size: 24rpx;
					color: #666;
					text-align: center;
				}
			}
			
			.scan-actions {
				display: flex;
				gap: 20rpx;
				justify-content: center;
				flex-wrap: wrap;
				
				.scan-btn {
					padding: 20rpx 30rpx;
					border: none;
					border-radius: 10rpx;
					font-size: 28rpx;
					
					&.primary {
						background: #007aff;
						color: #fff;
						
						&:disabled {
							background: #ccc;
						}
					}
					
					&.secondary {
						background: #f0f0f0;
						color: #333;
					}
				}
			}
		}
		
		.scan-result {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.result-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.result-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.clear-btn {
					padding: 10rpx 20rpx;
					background: #f0f0f0;
					color: #666;
					border: none;
					border-radius: 5rpx;
					font-size: 24rpx;
				}
			}
			
			.result-content {
				.result-text {
					font-size: 28rpx;
					color: #007aff;
					word-break: break-all;
				}
				
				.result-type {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-top: 10rpx;
				}
			}
		}
		
		.zone-info {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.zone-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.zone-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
				}
				
				.zone-status {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 22rpx;
					background: #fff3cd;
					color: #856404;
					
					&.bound {
						background: #d4edda;
						color: #155724;
					}
				}
			}
			
			.zone-content {
				margin-bottom: 20rpx;
				
				.zone-item {
					display: flex;
					margin-bottom: 10rpx;
					
					.zone-label {
						width: 160rpx;
						font-size: 26rpx;
						color: #666;
					}
					
					.zone-value {
						flex: 1;
						font-size: 26rpx;
						color: #333;
						font-weight: bold;
					}
				}
			}
		}
		
		.order-info {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.info-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.info-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.status-badge {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 22rpx;
					
					&.pending {
						background: #fff3cd;
						color: #856404;
					}
					
					&.partial {
						background: #d1ecf1;
						color: #0c5460;
					}
					
					&.completed {
						background: #d4edda;
						color: #155724;
					}
				}
			}
			
			.info-content {
				.info-item {
					display: flex;
					margin-bottom: 15rpx;
					
					.info-label {
						min-width: 160rpx;
						font-size: 26rpx;
						color: #666;
					}
					
					.info-value {
						flex: 1;
						font-size: 26rpx;
						color: #333;
						
						&.pending {
							color: #ff6b35;
							font-weight: bold;
						}
					}
				}
			}
		}
		
		.inbound-history {
			background: #fff;
			border-radius: 10rpx;
			padding: 30rpx;
			
			.history-header {
				margin-bottom: 20rpx;
				
				.history-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
				}
			}
			
			.history-list {
				.history-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1px solid #f0f0f0;
					
					&:last-child {
						border-bottom: none;
					}
					
					.history-info {
						flex: 1;
						
						.history-quantity {
							font-size: 28rpx;
							font-weight: bold;
							color: #007aff;
							margin-right: 20rpx;
						}
						
						.history-location {
							font-size: 24rpx;
							color: #666;
							margin-right: 20rpx;
						}
						
						.history-time {
							font-size: 22rpx;
							color: #999;
						}
					}
					
					.history-operator {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
	}
	
	.manual-input-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		
		.modal-content {
			width: 80%;
			max-width: 600rpx;
			background: #fff;
			border-radius: 10rpx;
			
			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1px solid #f0f0f0;
				
				.modal-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.close-btn {
					font-size: 40rpx;
					color: #999;
				}
			}
			
			.modal-body {
				padding: 30rpx;
				
				.input-item {
					.input-label {
						display: block;
						font-size: 26rpx;
						color: #333;
						margin-bottom: 15rpx;
					}
					
					.modal-input {
						width: 100%;
						padding: 20rpx;
						border: 1px solid #ddd;
						border-radius: 5rpx;
						font-size: 26rpx;
						box-sizing: border-box;
					}
				}
			}
			
			.modal-footer {
				display: flex;
				gap: 20rpx;
				padding: 30rpx;
				border-top: 1px solid #f0f0f0;
				
				.modal-btn {
					flex: 1;
					padding: 25rpx;
					border: none;
					border-radius: 10rpx;
					font-size: 28rpx;
					
					&.cancel {
						background: #f0f0f0;
						color: #666;
					}
					
					&.confirm {
						background: #007aff;
						color: #fff;
					}
				}
			}
		}
	}
	
	.zone-selector-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		
		.modal-content {
			width: 80%;
			max-width: 600rpx;
			max-height: 70%;
			background: #fff;
			border-radius: 10rpx;
			display: flex;
			flex-direction: column;
			
			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1px solid #f0f0f0;
				
				.modal-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.close-btn {
					font-size: 40rpx;
					color: #999;
				}
			}
			
			.modal-body {
				flex: 1;
				padding: 20rpx;
				overflow-y: auto;
				
				.zone-list {
					.zone-option {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 25rpx;
						border: 1px solid #f0f0f0;
						border-radius: 8rpx;
						margin-bottom: 15rpx;
						
						&.active {
							border-color: #007aff;
							background: #f0f8ff;
						}
						
						.zone-code {
							font-size: 26rpx;
							font-weight: bold;
							color: #333;
						}
						
						.zone-name {
							font-size: 24rpx;
							color: #666;
						}
					}
				}
			}
			
			.modal-footer {
				display: flex;
				gap: 20rpx;
				padding: 30rpx;
				border-top: 1px solid #f0f0f0;
				
				.modal-btn {
					flex: 1;
					padding: 25rpx;
					border: none;
					border-radius: 10rpx;
					font-size: 28rpx;
					
					&.cancel {
						background: #f0f0f0;
						color: #666;
					}
					
					&.confirm {
						background: #007aff;
						color: #fff;
						
						&:disabled {
							background: #ccc;
							color: #999;
						}
					}
				}
			}
		}
	}
}

@keyframes scanning {
	0% {
		transform: translateY(0);
	}
	100% {
		transform: translateY(396rpx);
	}
}
</style> 