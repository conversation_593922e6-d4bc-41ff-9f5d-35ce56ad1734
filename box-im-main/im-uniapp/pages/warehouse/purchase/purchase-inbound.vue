<template>
	<view class="page purchase-inbound">
		<nav-bar>采购入库</nav-bar>
	</view>
</template>

<script>
export default {
	onLoad() {
		// 直接跳转到入库扫码页面
		uni.redirectTo({
			url: '/pages/warehouse/purchase/inbound-scan',
			fail: (error) => {
				console.error('页面跳转失败:', error)
				uni.showToast({
					title: '页面跳转失败',
					icon: 'none'
				})
			}
		})
	}
}
</script>

<style scoped lang="scss">
.purchase-inbound {
	// 页面内容已移除，直接跳转到扫码页面
}
</style> 