/**
 * 库存管理API接口模块
 */
import request from '@/common/request.js'

// 库存服务器地址
const WAREHOUSE_BASE_URL = 'http://192.168.1.87:8090'

// 创建专用的仓库请求方法
const warehouseRequest = (options) => {
  return new Promise((resolve, reject) => {
    // 获取登录信息和token
    const loginInfo = uni.getStorageSync("loginInfo");
    const header = options.header || {};
    
    // 添加认证token
    if (loginInfo && loginInfo.accessToken) {
      header.accessToken = loginInfo.accessToken;
    }
    
    // 调试信息
    const fullUrl = WAREHOUSE_BASE_URL + options.url;
    console.log('========== 仓库API请求开始 ==========');
    console.log('完整URL:', fullUrl);
    console.log('请求方法:', options.method || 'GET');
    console.log('请求参数:', options.data || {});
    console.log('请求头:', header);
    
    uni.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      timeout: 30000,
      success(res) {
        console.log('请求成功，状态码:', res.statusCode);
        console.log('响应数据:', res.data);
        
        if (res.statusCode === 200) {
          if (res.data.code === 0 || res.data.code === 200) {
            console.log('业务处理成功');
            resolve(res.data)
          } else {
            console.error('业务处理失败，code:', res.data.code, 'message:', res.data.message);
            uni.showToast({
              title: res.data.message || '操作失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else {
          console.error('HTTP请求失败，状态码:', res.statusCode);
          reject(new Error(`请求失败 (${res.statusCode})`))
        }
      },
      fail(error) {
        console.error('========== 仓库API请求失败 ==========');
        console.error('请求URL:', fullUrl);
        console.error('错误信息:', error);
        console.error('错误详情:', JSON.stringify(error));
        reject(new Error('网络请求失败'))
      }
    })
  })
}

// 仓库数据展示接口
export const warehouseApi = {
  // 仓库统计接口
  statistics: {
    // 获取原料仓库统计
    getRawMaterialStats() {
      return warehouseRequest({
        url: '/wms/rawMaterialWarehouse/statistics',
        method: 'GET'
      })
    },
    // 获取零部件仓库统计
    getComponentStats() {
      return warehouseRequest({
        url: '/wms/componentWarehouse/statistics',
        method: 'GET'
      })
    },
    // 获取一级半成品统计
    getSemiFinishedStats() {
      return warehouseRequest({
        url: '/wms/semiFinishedProduct/statistics',
        method: 'GET'
      })
    },
    // 获取二级半成品统计
    getSemiFinishedTwoStats() {
      return warehouseRequest({
        url: '/wms/semiFinishedProductTwo/statistics',
        method: 'GET'
      })
    },
    // 获取成品仓库统计
    getProductStats() {
      return warehouseRequest({
        url: '/wms/productWarehouse/statistics',
        method: 'GET'
      })
    }
  },

  // 原料仓库
  rawMaterial: {
    // 分页查询
    getPage(params) {
      return warehouseRequest({
        url: '/wms/rawMaterialWarehouse/page',
        method: 'GET',
        data: params
      })
    },
    // 新增
    add(data) {
      return warehouseRequest({
        url: '/wms/rawMaterialWarehouse',
        method: 'POST',
        data
      })
    },
    // 修改
    update(data) {
      return warehouseRequest({
        url: '/wms/rawMaterialWarehouse',
        method: 'PUT',
        data
      })
    },
    // 删除
    delete(id) {
      return warehouseRequest({
        url: `/wms/rawMaterialWarehouse/${id}`,
        method: 'DELETE'
      })
    }
  },

  // 零部件仓库
  component: {
    getPage(params) {
      return warehouseRequest({
        url: '/wms/componentWarehouse/page',
        method: 'GET',
        data: params
      })
    },
    add(data) {
      return warehouseRequest({
        url: '/wms/componentWarehouse',
        method: 'POST',
        data
      })
    },
    update(data) {
      return warehouseRequest({
        url: '/wms/componentWarehouse',
        method: 'PUT',
        data
      })
    },
    delete(id) {
      return warehouseRequest({
        url: `/wms/componentWarehouse/${id}`,
        method: 'DELETE'
      })
    }
  },

  // 一级半成品
  semiFinished: {
    getPage(params) {
      return warehouseRequest({
        url: '/wms/semiFinishedProduct/page',
        method: 'GET',
        data: params
      })
    },
    add(data) {
      return warehouseRequest({
        url: '/wms/semiFinishedProduct',
        method: 'POST',
        data
      })
    },
    update(data) {
      return warehouseRequest({
        url: '/wms/semiFinishedProduct',
        method: 'PUT',
        data
      })
    },
    delete(id) {
      return warehouseRequest({
        url: `/wms/semiFinishedProduct/${id}`,
        method: 'DELETE'
      })
    }
  },

  // 二级半成品
  semiFinishedTwo: {
    getPage(params) {
      return warehouseRequest({
        url: '/wms/semiFinishedProductTwo/page',
        method: 'GET',
        data: params
      })
    },
    add(data) {
      return warehouseRequest({
        url: '/wms/semiFinishedProductTwo',
        method: 'POST',
        data
      })
    },
    update(data) {
      return warehouseRequest({
        url: '/wms/semiFinishedProductTwo',
        method: 'PUT',
        data
      })
    },
    delete(id) {
      return warehouseRequest({
        url: `/wms/semiFinishedProductTwo/${id}`,
        method: 'DELETE'
      })
    }
  },

  // 成品仓库
  product: {
    getPage(params) {
      return warehouseRequest({
        url: '/wms/productWarehouse/page',
        method: 'GET',
        data: params
      })
    },
    add(data) {
      return warehouseRequest({
        url: '/wms/productWarehouse',
        method: 'POST',
        data
      })
    },
    update(data) {
      return warehouseRequest({
        url: '/wms/productWarehouse',
        method: 'PUT',
        data
      })
    },
    delete(id) {
      return warehouseRequest({
        url: `/wms/productWarehouse/${id}`,
        method: 'DELETE'
      })
    }
  }
}

// 出入库操作接口
export const inventoryOperationApi = {
  // 入库操作
  inbound(data) {
    return warehouseRequest({
      url: '/inventory/operation/inbound',
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  // 出库操作
  outbound(data) {
    return warehouseRequest({
      url: '/inventory/operation/outbound',
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  // 移库操作
  transfer(data) {
    return warehouseRequest({
      url: '/inventory/operation/transfer',
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  }
}

// 查看分布功能接口
export const inventoryDetailApi = {
  // 查询物料在不同区域的分布情况
  getDistribution(params) {
    return warehouseRequest({
      url: '/system/inventoryDetail/list',
      method: 'GET',
      data: params
    })
  },
  // 新增：获取库存操作日志
  getOperationLogs(params) {
    return warehouseRequest({
      url: '/inventory/operation/logs',
      method: 'GET',
      data: params
    })
  }
}

// 新增：基础数据查询接口
export const warehouseBaseApi = {
	// 获取仓库信息列表
	getWarehouseList: (params) => {
		return warehouseRequest({
			url: '/system/warehouse/list',
			method: 'GET',
			data: params
		});
	},
	
	// 获取仓库区域列表
	getZoneList: (params) => {
		return warehouseRequest({
			url: '/system/zone/list',
			method: 'GET',
			data: params
		});
	}
};

// 注意：warehouseApi, inventoryOperationApi, inventoryDetailApi, warehouseBaseApi 
// 都已经使用 export const 导出，不需要再次导出 