<template>
	<uni-popup ref="popup" type="bottom" :mask-click="true">
		<view class="timeline-modal">
			<!-- 头部 -->
			<view class="modal-header">
				<view class="header-content">
					<view class="header-icon">
						<uni-icons type="calendar-filled" size="28" color="#3A84FF"></uni-icons>
					</view>
					<text class="header-title">出入库记录</text>
				</view>
				<view class="close-btn" @click="close">
					<uni-icons type="close" size="24" color="#666"></uni-icons>
				</view>
			</view>
			
			<!-- 物料信息卡片 -->
			<view class="material-info-card" v-if="materialInfo">
				<view class="info-header">
					<view class="info-icon">
						<uni-icons :type="materialIcon" size="24" color="#fff"></uni-icons>
					</view>
					<view class="info-detail">
						<text class="material-name">{{ materialInfo.name }}</text>
						<text class="material-id">物料编码：{{ materialInfo.id }}</text>
					</view>
					<view class="current-stock">
						<text class="stock-label">当前库存</text>
						<text class="stock-value">{{ materialInfo.currentStock || 0 }}</text>
					</view>
				</view>
			</view>
			
			<!-- 时间线内容 -->
			<scroll-view class="timeline-container" scroll-y="true" :show-scrollbar="false">
				<view v-if="loading" class="loading-state">
					<uni-load-more status="loading"></uni-load-more>
				</view>
				
				<view v-else-if="logs.length === 0" class="empty-state">
					<view class="empty-icon-wrapper">
						<uni-icons type="calendar" size="80" color="#ddd"></uni-icons>
					</view>
					<text class="empty-text">暂无操作记录</text>
					<text class="empty-hint">该物料近期没有出入库记录</text>
				</view>
				
				<view v-else class="timeline-list">
					<!-- 按日期分组 -->
					<view v-for="(group, index) in groupedLogs" :key="index" class="date-group">
						<view class="date-header">
							<view class="date-line"></view>
							<text class="date-text">{{ group.date }}</text>
							<view class="date-line"></view>
						</view>
						
						<!-- 时间线项目 -->
						<view class="timeline-items">
							<view 
								v-for="(log, logIndex) in group.logs" 
								:key="log.logId"
								class="timeline-item"
								:class="getTimelineItemClass(log.operationType)"
							>
								<!-- 时间线连接线 -->
								<view class="timeline-line" v-if="logIndex < group.logs.length - 1"></view>
								
								<!-- 时间点 -->
								<view class="timeline-point">
									<view class="point-outer">
										<view class="point-inner"></view>
									</view>
									<text class="time-text">{{ formatTime(log.operationTime) }}</text>
								</view>
								
								<!-- 内容卡片 -->
								<view class="timeline-content">
									<view class="content-header">
										<view class="operation-type">
											<uni-icons :type="getOperationIcon(log.operationType)" size="20" color="#fff"></uni-icons>
											<text class="type-text">{{ getOperationTypeText(log.operationType) }}</text>
										</view>
										<view class="quantity-change" :class="getQuantityClass(log.operationType)">
											<text class="quantity-text">{{ formatQuantityChange(log.quantityChange) }}</text>
										</view>
									</view>
									
									<view class="content-body">
										<view class="info-item" v-if="log.operationReason">
											<text class="info-label">原因：</text>
											<text class="info-value">{{ log.operationReason }}</text>
										</view>
										<view class="info-item" v-if="log.operator">
											<text class="info-label">操作员：</text>
											<text class="info-value">{{ log.operator }}</text>
										</view>
										<view class="info-item" v-if="log.sourceDocument">
											<text class="info-label">单据号：</text>
											<text class="info-value">{{ log.sourceDocument }}</text>
										</view>
										<view class="info-item">
											<text class="info-label">结余：</text>
											<text class="info-value stock">{{ log.quantityAfter }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 分页加载更多 -->
				<view v-if="!loading && hasMore" class="load-more" @click="loadMore">
					<text class="load-more-text">加载更多</text>
					<uni-icons type="bottom" size="16" color="#999"></uni-icons>
				</view>
			</scroll-view>
		</view>
	</uni-popup>
</template>

<script>
export default {
	name: 'StockHistoryTimeline',
	props: {
		warehouseType: {
			type: String,
			default: 'raw'
		}
	},
	data() {
		return {
			materialInfo: null,
			logs: [],
			loading: false,
			hasMore: false,
			currentPage: 1,
			pageSize: 15, // 性能优化：降低页面大小
			// 性能优化相关
			_computedCache: new Map(), // 使用Map提高查找性能
			visibleRange: { start: 0, end: 20 }, // 虚拟滚动范围
			scrollTimer: null // 滚动防抖定时器
		}
	},
	computed: {
		materialIcon() {
			const iconMap = {
				'raw': 'gear',
				'parts': 'cart',
				'semi1': 'settings',
				'semi2': 'refreshempty',
				'finished': 'gift'
			};
			return iconMap[this.warehouseType] || 'cube';
		},
		
		// 按日期分组的日志 - 性能优化：缓存计算结果
		groupedLogs() {
			const cacheKey = `grouped_${this.logs.length}_${this.logs[0]?.logId || 'empty'}`;
			
			if (this._computedCache.has(cacheKey)) {
				return this._computedCache.get(cacheKey);
			}
			
			const groups = {};
			this.logs.forEach(log => {
				const date = this.formatDate(log.operationTime);
				if (!groups[date]) {
					groups[date] = {
						date: date,
						logs: []
					};
				}
				groups[date].logs.push(log);
			});
			
			// 转换为数组并排序
			const result = Object.values(groups).sort((a, b) => {
				return new Date(b.date) - new Date(a.date);
			});
			
			// 缓存结果
			this._computedCache.set(cacheKey, result);
			return result;
		}
	},
	methods: {
		open(materialInfo, logs = []) {
			console.log('Timeline modal opening with:', materialInfo, logs);
			this.materialInfo = materialInfo;
			this.logs = logs;
			this.currentPage = 1;
			this.hasMore = logs.length >= this.pageSize;
			this.loading = logs.length === 0; // 如果没有日志数据，显示加载状态
			
			// 性能优化：清除缓存，重新开始
			this._computedCache.clear();
			
			this.$refs.popup.open();
		},
		
		close() {
			this.$refs.popup.close();
		},
		
		loadMore() {
			// 触发父组件的加载更多事件
			this.$emit('load-more', this.currentPage + 1);
		},
		
		addLogs(newLogs) {
			this.logs = [...this.logs, ...newLogs];
			this.currentPage++;
			this.hasMore = newLogs.length >= this.pageSize;
			this.loading = false; // 关闭加载状态
			
			// 性能优化：清除相关缓存
			this._computedCache.clear();
		},
		
		getTimelineItemClass(type) {
			if (!type) return '';
			const typeLower = type.toLowerCase();
			if (typeLower.includes('in') || typeLower.includes('入')) {
				return 'inbound-item';
			} else if (typeLower.includes('out') || typeLower.includes('出')) {
				return 'outbound-item';
			} else if (typeLower.includes('trans') || typeLower.includes('转')) {
				return 'transfer-item';
			}
			return '';
		},
		
		getOperationIcon(type) {
			if (!type) return 'circle';
			const typeLower = type.toLowerCase();
			if (typeLower.includes('in') || typeLower.includes('入')) {
				return 'download';
			} else if (typeLower.includes('out') || typeLower.includes('出')) {
				return 'upload';
			} else if (typeLower.includes('trans') || typeLower.includes('转')) {
				return 'refreshempty';
			}
			return 'circle';
		},
		
		getQuantityClass(type) {
			if (!type) return '';
			const typeLower = type.toLowerCase();
			if (typeLower.includes('in') || typeLower.includes('入')) {
				return 'quantity-in';
			} else if (typeLower.includes('out') || typeLower.includes('出')) {
				return 'quantity-out';
			} else if (typeLower.includes('trans') || typeLower.includes('转')) {
				return 'quantity-transfer';
			}
			return '';
		},
		
		// 获取操作类型的中文显示文本
		getOperationTypeText(type) {
			if (!type) return '未知操作';
			
			const typeLower = type.toLowerCase();
			
			// 常见的英文类型映射
			const typeMap = {
				'inbound': '入库',
				'outbound': '出库',
				'transfer': '调拨',
				'transfer_in': '调入',
				'transfer_out': '调出',
				'purchase_in': '采购入库',
				'sales_out': '销售出库',
				'production_in': '生产入库',
				'production_out': '生产出库',
				'adjustment_in': '盘盈',
				'adjustment_out': '盘亏',
				'return_in': '退货入库',
				'return_out': '退货出库',
				'scrap_out': '报废出库',
				'consume_out': '消耗出库',
				'allocate_in': '分配入库',
				'allocate_out': '分配出库'
			};
			
			// 直接匹配映射表
			if (typeMap[typeLower]) {
				return typeMap[typeLower];
			}
			
			// 包含关键词的模糊匹配
			if (typeLower.includes('in') || typeLower.includes('入')) {
				return '入库';
			} else if (typeLower.includes('out') || typeLower.includes('出')) {
				return '出库';
			} else if (typeLower.includes('trans') || typeLower.includes('transfer') || typeLower.includes('调') || typeLower.includes('转')) {
				return '调拨';
			} else if (typeLower.includes('purchase') || typeLower.includes('采购')) {
				return '采购';
			} else if (typeLower.includes('sales') || typeLower.includes('销售')) {
				return '销售';
			} else if (typeLower.includes('production') || typeLower.includes('生产')) {
				return '生产';
			} else if (typeLower.includes('adjustment') || typeLower.includes('盘')) {
				return '盘点';
			} else if (typeLower.includes('return') || typeLower.includes('退')) {
				return '退货';
			} else if (typeLower.includes('scrap') || typeLower.includes('报废')) {
				return '报废';
			}
			
			// 如果都没匹配上，返回原始类型（可能已经是中文）
			return type;
		},
		
		formatQuantityChange(quantity) {
			if (quantity > 0) {
				return `+${quantity}`;
			}
			return quantity;
		},
		
		formatDate(dateStr) {
			if (!dateStr) return '未知日期';
			try {
				const date = new Date(dateStr);
				const today = new Date();
				const yesterday = new Date(today);
				yesterday.setDate(yesterday.getDate() - 1);
				
				// 格式化为年月日
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const dateString = `${year}-${month}-${day}`;
				
				// 判断是否为今天或昨天
				if (dateString === this.formatDateToString(today)) {
					return '今天';
				} else if (dateString === this.formatDateToString(yesterday)) {
					return '昨天';
				}
				
				return `${month}月${day}日`;
			} catch (error) {
				return dateStr.substring(0, 10);
			}
		},
		
		formatDateToString(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		formatTime(dateStr) {
			if (!dateStr) return '00:00';
			try {
				const date = new Date(dateStr);
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			} catch (error) {
				return dateStr.substring(11, 16);
			}
		}
	},
	// 性能优化：组件销毁时清理
	beforeDestroy() {
		if (this.scrollTimer) {
			clearTimeout(this.scrollTimer);
		}
		this._computedCache.clear();
		this.logs = [];
	}
}
</script>

<style lang="scss" scoped>
.timeline-modal {
	background: #f8f9fa;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	height: 85vh;
	display: flex;
	flex-direction: column;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	
	.header-content {
		display: flex;
		align-items: center;
		gap: 16rpx;
		
		.header-icon {
			width: 56rpx;
			height: 56rpx;
			background: linear-gradient(135deg, #e6f2ff 0%, #cce5ff 100%);
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.header-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #1a1a1a;
		}
	}
	
	.close-btn {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f5f5f5;
		border-radius: 50%;
		
		&:active {
			background: #e8e8e8;
		}
	}
}

.material-info-card {
	margin: 24rpx 24rpx 16rpx;
	padding: 28rpx;
	background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
	border-radius: 24rpx;
	box-shadow: 0 12rpx 32rpx rgba(17, 153, 142, 0.25);
	position: relative;
	overflow: hidden;
	min-height: 160rpx;
	height: auto;
	flex-shrink: 0;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="timelineGrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.08)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23timelineGrain)"/></svg>');
		opacity: 0.4;
	}
	
	.info-header {
		display: flex;
		align-items: center;
		gap: 20rpx;
		position: relative;
		z-index: 1;
		min-height: 80rpx;
		
		.info-icon {
			width: 72rpx;
			height: 72rpx;
			background: rgba(255, 255, 255, 0.2);
			backdrop-filter: blur(10rpx);
			border-radius: 18rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
		}
		
		.info-detail {
			flex: 1;
			min-width: 0;
			
			.material-name {
				display: block;
				font-size: 32rpx;
				font-weight: 600;
				color: white;
				margin-bottom: 8rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			
			.material-id {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.8);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		
		.current-stock {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			gap: 8rpx;
			flex-shrink: 0;
			min-width: 120rpx;
			
			.stock-label {
				font-size: 24rpx;
				color: rgba(255, 255, 255, 0.7);
			}
			
			.stock-value {
				font-size: 40rpx;
				font-weight: 700;
				color: white;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}

.timeline-container {
	flex: 1;
	padding: 0 16rpx 24rpx;
	min-height: 0;
	overflow-y: auto;
}

.loading-state, .empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	
	.empty-icon-wrapper {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f5f5f5;
		border-radius: 50%;
	}
	
	.empty-text {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 12rpx;
	}
	
	.empty-hint {
		font-size: 26rpx;
		color: #999;
	}
}

.timeline-list {
	margin-top: 24rpx;
}

.date-group {
	margin-bottom: 40rpx;
	
	.date-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		
		.date-line {
			flex: 1;
			height: 1rpx;
			background: #e0e0e0;
		}
		
		.date-text {
			margin: 0 24rpx;
			font-size: 28rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

.timeline-items {
	position: relative;
	padding-left: 100rpx;
}

.timeline-item {
	position: relative;
	margin-bottom: 32rpx;
	padding-right: 24rpx;
	margin-right: 8rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.timeline-line {
		position: absolute;
		left: -60rpx;
		top: 40rpx;
		bottom: -32rpx;
		width: 2rpx;
		background: #e0e0e0;
	}
	
	.timeline-point {
		position: absolute;
		left: -100rpx;
		top: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
		
		.point-outer {
			width: 32rpx;
			height: 32rpx;
			border-radius: 50%;
			background: white;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			
			.point-inner {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: #999;
			}
		}
		
		.time-text {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	// 入库样式
	&.inbound-item {
		.point-outer .point-inner {
			background: #34c759;
		}
		
		.operation-type {
			background: linear-gradient(135deg, #34c759 0%, #30a14e 100%);
		}
	}
	
	// 出库样式
	&.outbound-item {
		.point-outer .point-inner {
			background: #ff3b30;
		}
		
		.operation-type {
			background: linear-gradient(135deg, #ff3b30 0%, #d93025 100%);
		}
	}
	
	// 移库样式
	&.transfer-item {
		.point-outer .point-inner {
			background: #ff9500;
		}
		
		.operation-type {
			background: linear-gradient(135deg, #ff9500 0%, #f57c00 100%);
		}
	}
	
	.timeline-content {
		background: white;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
		max-width: 100%;
		
		.content-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 20rpx 20rpx 20rpx;
			background: #fafafa;
			gap: 16rpx;
			
			.operation-type {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				flex-shrink: 0;
				max-width: 55%;
				min-width: 0;
				
				.type-text {
					font-size: 24rpx;
					color: white;
					font-weight: 500;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
			
			.quantity-change {
				font-size: 32rpx;
				font-weight: 700;
				flex-shrink: 0;
				min-width: 80rpx;
				max-width: 40%;
				text-align: right;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				
				&.quantity-in {
					color: #34c759;
				}
				
				&.quantity-out {
					color: #ff3b30;
				}
				
				&.quantity-transfer {
					color: #ff9500;
				}
			}
		}
		
		.content-body {
			padding: 20rpx 20rpx;
			
			.info-item {
				display: flex;
				align-items: center;
				margin-bottom: 12rpx;
				flex-wrap: wrap;
				gap: 8rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.info-label {
					font-size: 26rpx;
					color: #999;
					min-width: 100rpx;
					flex-shrink: 0;
				}
				
				.info-value {
					font-size: 26rpx;
					color: #333;
					flex: 1;
					word-break: break-all;
					line-height: 1.4;
					
					&.stock {
						font-weight: 600;
						color: #3A84FF;
					}
				}
			}
		}
	}
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 24rpx;
	margin-top: 32rpx;
	
	.load-more-text {
		font-size: 26rpx;
		color: #999;
	}
	
	&:active {
		opacity: 0.7;
	}
}

@media (max-width: 750rpx) {
	.timeline-item {
		padding-right: 32rpx;
		margin-right: 12rpx;
		
		.timeline-content {
			.content-header {
				padding: 16rpx 16rpx;
				gap: 12rpx;
				
				.operation-type {
					max-width: 50%;
					padding: 6rpx 12rpx;
					
					.type-text {
						font-size: 22rpx;
					}
				}
				
				.quantity-change {
					font-size: 28rpx;
					min-width: 70rpx;
					max-width: 45%;
				}
			}
			
			.content-body {
				padding: 16rpx 16rpx;
				
				.info-item {
					.info-label {
						font-size: 24rpx;
						min-width: 80rpx;
					}
					
					.info-value {
						font-size: 24rpx;
					}
				}
			}
		}
	}
}

@media (max-width: 600rpx) {
	.timeline-item {
		padding-right: 40rpx;
		margin-right: 16rpx;
		
		.timeline-content {
			.content-header {
				flex-direction: column;
				align-items: flex-start;
				gap: 12rpx;
				
				.operation-type {
					max-width: 100%;
					align-self: flex-start;
				}
				
				.quantity-change {
					max-width: 100%;
					text-align: left;
					align-self: flex-start;
				}
			}
		}
	}
}
</style> 