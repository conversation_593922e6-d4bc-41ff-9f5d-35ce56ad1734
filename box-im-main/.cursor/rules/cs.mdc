---
description: 
globs: 
alwaysApply: true
---
# .cursor
# 用于辅助编写代码的 Cursor 配置文件

prompts:
- name: 安全添加功能 (Add Feature Safely)
    description: 添加新功能，同时检查对现有功能的影响，并保护数据库结构。
    prompt: |
      请根据以下描述添加新功能：

      功能描述：{{user_prompt}}

      请在以下代码（或当前上下文）中实现此功能：
      ```{{language}}
      {{selected_code}}
      ```

      **重要约束：**
      1.  在生成代码之前，请仔细分析此次添加可能对现有其他功能产生的影响。
      2.  **如果发现任何潜在的负面影响或兼容性问题，请先明确指出这些风险点，并暂停执行，询问我是否确认继续修改。** 示例：“警告：此更改可能会影响模块 X 的 Y 功能。具体影响是 Z。请确认是否继续？”
      3.  **除非我在功能描述中明确要求，否则绝对不要修改任何数据库表的结构或字段。**
      4.  只有在我确认（如果需要）或分析表明没有负面影响后，才提供最终的、完整的代码实现。
      请用中文回答。

  - name: 安全修改代码 (Modify Code Safely)
    description: 修改现有代码，检查副作用，保护数据库结构，并在产生影响时请求确认。
    prompt: |
      请根据以下要求修改选定的代码：

      修改要求：{{user_prompt}}

      待修改代码：
      ```{{language}}
      {{selected_code}}
      ```

      **重要约束：**
      1.  执行修改前，请分析此修改是否可能破坏程序的其他部分或引入副作用。
      2.  **如果发现任何潜在的负面影响，请先详细说明，并暂停执行，询问我是否确认继续。** 示例：“警告：此修改可能导致 A 行为改变为 B。请确认是否继续？”
      3.  **除非我在修改要求中明确说明，否则绝对禁止更改数据库相关的结构或字段。**
      4.  只有在我确认（如果需要）或分析表明修改安全后，才提供最终修改后的代码。
      请用中文回答。

  - name: 代码审查助手 (Code Review Assistant)
    description: 扮演代码审查者的角色，检查潜在问题，特别是副作用和数据库更改。
    prompt: |
      请审查以下代码：
      ```{{language}}
      {{selected_code}}
      ```

      请重点关注以下方面：
      1.  **潜在副作用：** 这段代码是否可能无意中影响了系统的其他部分？
      2.  **数据库操作：** 是否有任何修改数据库结构或字段的操作？这些操作是否符合预期（或者是否应该存在）？
      3.  **错误处理：** 异常和错误情况是否得到了妥善处理？
      4.  **可读性和维护性：** 代码是否清晰、易于理解和维护？
      5.  **安全性：** 是否存在明显的安全漏洞？

      请提供你的审查意见和改进建议。请用中文回答。
  - name: 解释代码 (Explain Code)
    description: 解释选定的代码片段
    prompt: |
      请详细解释以下代码的功能、逻辑流程和关键部分：

      ```{{language}}
      {{selected_code}}
      ```
      请用中文回答。

  - name: 生成代码 (Generate Code)
    description: 根据自然语言描述生成代码
    prompt: |
      请根据以下描述生成代码。如果可能，请指出目标语言或框架：

      {{user_prompt}}
      请用中文回答并提供代码。

  - name: 重构代码 (Refactor Code)
    description: 重构选定的代码以提高质量
    prompt: |
      请重构以下选定的代码。请说明你的主要重构目标（例如：提高可读性、性能、模块化、遵循特定设计模式等）。

      ```{{language}}
      {{selected_code}}
      ```

      重构目标：{{user_prompt}}

      请提供重构后的代码，并用中文简要说明所做的更改。

  - name: 查找问题 (Find Issues)
    description: 检查选定代码中的潜在错误或改进点
    prompt: |
      请检查以下代码中是否存在潜在的错误、性能瓶颈、安全漏洞或可以改进的地方。请提供具体的建议和修改方案。

      ```{{language}}
      {{selected_code}}
      ```
      请用中文回答。

  - name: 编写测试 (Write Tests)
    description: 为选定的代码生成测试用例
    prompt: |
      请为以下代码编写测试用例。如果可能，请指定你希望使用的测试框架。

      ```{{language}}
      {{selected_code}}
      ```

      测试框架（可选）：{{user_prompt}}

      请用中文回答并提供测试代码。

  - name: 添加文档注释 (Add Docs)
    description: 为选定的代码添加文档注释
    prompt: |
      请为以下代码添加符合标准格式的文档注释（例如 Javadoc, XML Docs, DocString 等）。

      ```{{language}}
      {{selected_code}}
      ```
      请用中文回答并提供带有文档注释的代码。
