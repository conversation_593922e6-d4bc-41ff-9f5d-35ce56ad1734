# 工序步骤按钮显示逻辑

## 修改内容

成功修改了工序步骤按钮显示逻辑，确保"分类清点数量入库"工序只显示"入库"按钮，不显示"开始"按钮。

## 按钮显示规则

### 1. 领料按钮
**显示条件**: `step.stepName === '领取PCB裸板' && order.orderStatus !== 'PAUSED'`
- 只在"领取PCB裸板"工序显示
- 工单状态不为"暂停中"时显示

### 2. 入库按钮
**显示条件**: `step.stepName === '分类清点数量入库' && order.orderStatus !== 'PAUSED'`
- 只在"分类清点数量入库"工序显示
- 工单状态不为"暂停中"时显示

### 3. 开始按钮（修改后）
**显示条件**: 
```javascript
step.isCompleted === 0 && 
order.orderStatus !== 'PAUSED' && 
step.stepName !== '领取PCB裸板' && 
step.stepName !== '分类清点数量入库'
```
- 工序状态为"未开始"(isCompleted === 0)
- 工单状态不为"暂停中"
- **排除"领取PCB裸板"工序**
- **排除"分类清点数量入库"工序**（新增）

### 4. 完成按钮
**显示条件**: `step.isCompleted === 1 && order.orderStatus !== 'PAUSED'`
- 工序状态为"执行中"(isCompleted === 1)
- 工单状态不为"暂停中"时显示

### 5. 提交缺陷按钮
**显示条件**: 
```javascript
(step.isCompleted === 1 || step.isCompleted === 2) && 
order.orderStatus !== 'COMPLETED' && 
order.orderStatus !== 'PAUSED'
```
- 工序状态为"执行中"或"已完成"
- 工单状态不为"已完成"或"暂停中"

## 工序类型与按钮对应关系

| 工序名称 | 领料按钮 | 入库按钮 | 开始按钮 | 完成按钮 | 提交缺陷按钮 |
|----------|----------|----------|----------|----------|--------------|
| **领取PCB裸板** | ✅ | ❌ | ❌ | ✅* | ✅* |
| **分类清点数量入库** | ❌ | ✅ | ❌ | ✅* | ✅* |
| **其他工序** | ❌ | ❌ | ✅* | ✅* | ✅* |

*注：需要满足相应的状态条件

## 按钮互斥逻辑

### 1. 特殊工序的专用按钮
- **"领取PCB裸板"**: 只显示"领料"按钮，不显示"开始"按钮
- **"分类清点数量入库"**: 只显示"入库"按钮，不显示"开始"按钮

### 2. 状态相关按钮
- **未开始状态**: 显示"开始"按钮（排除特殊工序）
- **执行中状态**: 显示"完成"按钮
- **已完成状态**: 不显示"开始"和"完成"按钮

### 3. 通用按钮
- **提交缺陷按钮**: 在执行中和已完成状态下显示（满足条件时）

## 代码实现

### 修改前的"开始"按钮条件
```vue
<view
  v-if="step.isCompleted === 0 && order.orderStatus !== 'PAUSED' && step.stepName !== '领取PCB裸板'"
  class="step-action-btn start-btn"
>
```

### 修改后的"开始"按钮条件
```vue
<view
  v-if="step.isCompleted === 0 && order.orderStatus !== 'PAUSED' && step.stepName !== '领取PCB裸板' && step.stepName !== '分类清点数量入库'"
  class="step-action-btn start-btn"
>
```

## 验证场景

### 1. "分类清点数量入库"工序
- ✅ 只显示"入库"按钮
- ✅ 不显示"开始"按钮
- ✅ 根据状态显示"完成"和"提交缺陷"按钮

### 2. "领取PCB裸板"工序
- ✅ 只显示"领料"按钮
- ✅ 不显示"开始"按钮
- ✅ 根据状态显示"完成"和"提交缺陷"按钮

### 3. 其他工序
- ✅ 在未开始状态显示"开始"按钮
- ✅ 不显示"领料"和"入库"按钮
- ✅ 根据状态显示"完成"和"提交缺陷"按钮

### 4. 暂停状态
- ✅ 所有工序都不显示操作按钮（领料、入库、开始、完成）
- ✅ 只可能显示"提交缺陷"按钮（如果满足条件）

## 测试清单

- [ ] 测试"分类清点数量入库"工序只显示"入库"按钮
- [ ] 测试"领取PCB裸板"工序只显示"领料"按钮
- [ ] 测试其他工序在未开始状态显示"开始"按钮
- [ ] 测试暂停状态下不显示操作按钮
- [ ] 测试不同工序状态下的按钮显示正确性
- [ ] 测试按钮点击功能正常工作

## 总结

通过在"开始"按钮的显示条件中添加 `&& step.stepName !== '分类清点数量入库'`，成功实现了：

1. **互斥显示**: "分类清点数量入库"工序只显示"入库"按钮，不显示"开始"按钮
2. **逻辑一致**: 与"领取PCB裸板"工序的处理逻辑保持一致
3. **功能完整**: 其他工序的按钮显示逻辑不受影响
4. **用户体验**: 避免了按钮冲突，提供清晰的操作指引

修改后的按钮显示逻辑更加清晰和合理，确保每个工序都有明确的操作入口。
