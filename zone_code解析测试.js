// zone_code 解析功能测试用例
// 可以在浏览器控制台中运行这些测试

// 模拟 extractZoneCode 方法
function extractZoneCode(scanData) {
  try {
    // 扫码数据格式示例：label_type:warehouse_zone|zone_code:SFQ20250601
    if (!scanData || typeof scanData !== 'string') {
      console.error('扫码数据为空或格式错误:', scanData)
      return null
    }

    console.log('开始解析zone_code，原始数据:', scanData)

    // 使用正则表达式提取zone_code的值
    const zoneCodeMatch = scanData.match(/zone_code:([^|]+)/i)

    if (zoneCodeMatch && zoneCodeMatch[1]) {
      const zoneCode = zoneCodeMatch[1].trim()
      console.log('正则匹配成功，解析出的zone_code:', zoneCode)
      return zoneCode
    }

    // 如果正则匹配失败，尝试手动解析
    console.log('正则匹配失败，尝试手动解析')
    const parts = scanData.split('|')
    for (const part of parts) {
      const trimmedPart = part.trim()
      if (trimmedPart.toLowerCase().includes('zone_code:')) {
        const colonIndex = trimmedPart.indexOf(':')
        if (colonIndex !== -1 && colonIndex < trimmedPart.length - 1) {
          const zoneCode = trimmedPart.substring(colonIndex + 1).trim()
          console.log('手动解析成功，解析出的zone_code:', zoneCode)
          return zoneCode
        }
      }
    }

    console.error('无法解析zone_code，扫码数据格式不正确:', scanData)
    return null
  } catch (error) {
    console.error('解析zone_code时发生异常:', error)
    return null
  }
}

// 测试用例
console.log('=== zone_code 解析功能测试 ===')

// 测试用例1：标准格式
const testCase1 = 'label_type:warehouse_zone|zone_code:SFQ20250601'
console.log('\n测试用例1 - 标准格式:')
console.log('输入:', testCase1)
console.log('输出:', extractZoneCode(testCase1))
console.log('期望:', 'SFQ20250601')

// 测试用例2：大小写混合
const testCase2 = 'LABEL_TYPE:warehouse_zone|ZONE_CODE:ABC123456'
console.log('\n测试用例2 - 大小写混合:')
console.log('输入:', testCase2)
console.log('输出:', extractZoneCode(testCase2))
console.log('期望:', 'ABC123456')

// 测试用例3：包含空格
const testCase3 = 'label_type:warehouse_zone | zone_code: XYZ789 '
console.log('\n测试用例3 - 包含空格:')
console.log('输入:', testCase3)
console.log('输出:', extractZoneCode(testCase3))
console.log('期望:', 'XYZ789')

// 测试用例4：多个字段
const testCase4 = 'label_type:warehouse_zone|zone_code:TEST001|other_field:value'
console.log('\n测试用例4 - 多个字段:')
console.log('输入:', testCase4)
console.log('输出:', extractZoneCode(testCase4))
console.log('期望:', 'TEST001')

// 测试用例5：错误格式 - 缺少zone_code
const testCase5 = 'label_type:warehouse_zone|other_field:value'
console.log('\n测试用例5 - 错误格式（缺少zone_code）:')
console.log('输入:', testCase5)
console.log('输出:', extractZoneCode(testCase5))
console.log('期望:', null)

// 测试用例6：空字符串
const testCase6 = ''
console.log('\n测试用例6 - 空字符串:')
console.log('输入:', testCase6)
console.log('输出:', extractZoneCode(testCase6))
console.log('期望:', null)

// 测试用例7：null值
const testCase7 = null
console.log('\n测试用例7 - null值:')
console.log('输入:', testCase7)
console.log('输出:', extractZoneCode(testCase7))
console.log('期望:', null)

// 测试用例8：只有zone_code
const testCase8 = 'zone_code:SIMPLE123'
console.log('\n测试用例8 - 只有zone_code:')
console.log('输入:', testCase8)
console.log('输出:', extractZoneCode(testCase8))
console.log('期望:', 'SIMPLE123')

console.log('\n=== 测试完成 ===')

// 使用说明
console.log('\n使用说明:')
console.log('1. 复制这段代码到浏览器控制台中运行')
console.log('2. 查看每个测试用例的输出是否符合期望')
console.log('3. 如果所有测试用例都通过，说明解析功能正常工作')
