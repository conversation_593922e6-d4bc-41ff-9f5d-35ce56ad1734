# 按钮显示逻辑验证表

## 快速验证清单

### 工序类型与按钮显示对照表

| 工序名称 | 工序状态 | 工单状态 | 领料按钮 | 入库按钮 | 开始按钮 | 完成按钮 | 提交缺陷按钮 |
|----------|----------|----------|----------|----------|----------|----------|--------------|
| **领取PCB裸板** | 未开始(0) | 正常 | ✅ | ❌ | ❌ | ❌ | ❌ |
| **领取PCB裸板** | 执行中(1) | 正常 | ✅ | ❌ | ❌ | ✅ | ✅ |
| **领取PCB裸板** | 已完成(2) | 正常 | ✅ | ❌ | ❌ | ❌ | ✅ |
| **分类清点数量入库** | 未开始(0) | 正常 | ❌ | ✅ | ❌ | ❌ | ❌ |
| **分类清点数量入库** | 执行中(1) | 正常 | ❌ | ✅ | ❌ | ✅ | ✅ |
| **分类清点数量入库** | 已完成(2) | 正常 | ❌ | ✅ | ❌ | ❌ | ✅ |
| **其他工序** | 未开始(0) | 正常 | ❌ | ❌ | ✅ | ❌ | ❌ |
| **其他工序** | 执行中(1) | 正常 | ❌ | ❌ | ❌ | ✅ | ✅ |
| **其他工序** | 已完成(2) | 正常 | ❌ | ❌ | ❌ | ❌ | ✅ |
| **任何工序** | 任何状态 | 暂停中 | ❌ | ❌ | ❌ | ❌ | ❌* |

*注：提交缺陷按钮在暂停状态下也不显示

## 核心修改验证

### 修改前的问题
- "分类清点数量入库"工序会同时显示"开始"按钮和"入库"按钮
- 用户可能会困惑应该点击哪个按钮

### 修改后的效果
- "分类清点数量入库"工序只显示"入库"按钮
- 与"领取PCB裸板"工序的逻辑保持一致
- 用户操作更加清晰明确

## 代码修改对比

### 修改前
```vue
v-if="step.isCompleted === 0 && order.orderStatus !== 'PAUSED' && step.stepName !== '领取PCB裸板'"
```

### 修改后
```vue
v-if="step.isCompleted === 0 && order.orderStatus !== 'PAUSED' && step.stepName !== '领取PCB裸板' && step.stepName !== '分类清点数量入库'"
```

## 测试场景

### 场景1：分类清点数量入库工序
1. **工序状态：未开始**
   - 期望：只显示"入库"按钮
   - 验证：不显示"开始"按钮

2. **工序状态：执行中**
   - 期望：显示"入库"按钮和"完成"按钮
   - 验证：不显示"开始"按钮

3. **工序状态：已完成**
   - 期望：显示"入库"按钮和"提交缺陷"按钮
   - 验证：不显示"开始"和"完成"按钮

### 场景2：领取PCB裸板工序
1. **工序状态：未开始**
   - 期望：只显示"领料"按钮
   - 验证：不显示"开始"按钮

### 场景3：其他工序
1. **工序状态：未开始**
   - 期望：只显示"开始"按钮
   - 验证：不显示"领料"和"入库"按钮

### 场景4：暂停状态
1. **任何工序，任何状态**
   - 期望：不显示任何操作按钮
   - 验证：只可能显示"提交缺陷"按钮（如果满足特定条件）

## 验证步骤

1. **打开任务管理页面**
2. **找到"分类清点数量入库"工序**
   - 检查是否只显示"入库"按钮
   - 确认不显示"开始"按钮
3. **找到"领取PCB裸板"工序**
   - 检查是否只显示"领料"按钮
   - 确认不显示"开始"按钮
4. **找到其他工序**
   - 在未开始状态检查是否显示"开始"按钮
   - 确认不显示"领料"和"入库"按钮
5. **测试暂停状态**
   - 确认暂停状态下不显示操作按钮

## 成功标准

- ✅ "分类清点数量入库"工序只显示"入库"按钮
- ✅ "领取PCB裸板"工序只显示"领料"按钮  
- ✅ 其他工序在未开始状态显示"开始"按钮
- ✅ 暂停状态下不显示操作按钮
- ✅ 按钮功能正常工作
- ✅ 用户界面清晰无歧义

## 总结

通过添加 `&& step.stepName !== '分类清点数量入库'` 条件，成功实现了：

1. **按钮互斥**: 特殊工序有专用按钮，不显示通用的"开始"按钮
2. **逻辑一致**: 与现有的"领取PCB裸板"工序处理方式保持一致
3. **用户体验**: 每个工序都有明确的操作入口，避免混淆
4. **功能完整**: 不影响其他工序的正常功能

修改简单而有效，确保了系统的一致性和用户体验的优化。
