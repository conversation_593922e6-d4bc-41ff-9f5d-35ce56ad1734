# 入库双重功能验证清单

## 功能验证要点

### ✅ 核心功能验证

#### 1. 入库操作验证
- [ ] 点击"入库"按钮显示板型选择
- [ ] 选择板型后进入扫码流程
- [ ] 扫码成功解析zone_code
- [ ] 调用inboundSfp API执行入库
- [ ] 显示入库成功提示

#### 2. 工序开始验证
- [ ] 入库成功后检查工序状态
- [ ] 未开始状态(isCompleted=0)自动启动工序
- [ ] 工序状态变更为执行中(isCompleted=1)
- [ ] 工单状态适当时变更为IN_PROGRESS

### ✅ 状态管理验证

#### 1. 未开始状态的工序
```
初始状态: step.isCompleted = 0
执行入库: inboundSfp API调用成功
自动启动: handleStartStep()被调用
最终状态: step.isCompleted = 1
```

#### 2. 已开始状态的工序
```
初始状态: step.isCompleted = 1
执行入库: inboundSfp API调用成功
状态检查: 发现已开始，跳过启动
最终状态: step.isCompleted = 1 (保持不变)
```

#### 3. 入库失败的情况
```
初始状态: step.isCompleted = 0
执行入库: inboundSfp API调用失败
错误处理: 不执行工序开始
最终状态: step.isCompleted = 0 (保持不变)
```

### ✅ 与领料功能对比验证

| 验证项 | 领料功能 | 入库功能 | 状态 |
|--------|----------|----------|------|
| 双重功能 | 出库 + 工序开始 | 入库 + 工序开始 | [ ] |
| 成功后处理 | 调用handleStartStep | 调用handleStartStep | [ ] |
| 状态检查 | 无额外检查 | 检查step.isCompleted | [ ] |
| 错误处理 | 失败时不启动工序 | 失败时不启动工序 | [ ] |
| 工序状态变更 | 0 → 1 | 0 → 1 | [ ] |
| 工单状态变更 | NEW → IN_PROGRESS | NEW → IN_PROGRESS | [ ] |

### ✅ 日志验证

#### 1. 入库成功 + 工序未开始
期望日志：
```
入库成功，工序状态为未开始，执行工序开始逻辑
开始工序，参数: {stepTaskId: xxx, stepName: xxx, currentStatus: 0}
更新工序任务状态API响应: {code: 0, ...}
工序已开始
```

#### 2. 入库成功 + 工序已开始
期望日志：
```
入库成功，工序已开始，无需重复开始工序，当前状态: 1
```

#### 3. 入库失败
期望日志：
```
入库操作失败，不执行开始工序: [错误信息]
```

### ✅ 界面状态验证

#### 1. 入库前状态
- [ ] "分类清点数量入库"工序显示"入库"按钮
- [ ] 工序状态显示为"未开始"
- [ ] 不显示"开始"按钮

#### 2. 入库后状态
- [ ] 工序状态显示为"执行中"
- [ ] 显示"完成"按钮
- [ ] 可能显示"提交缺陷"按钮
- [ ] 工单状态可能变为"执行中"

### ✅ 异常情况验证

#### 1. 网络异常
- [ ] 入库API调用失败
- [ ] 显示网络错误提示
- [ ] 工序状态保持不变

#### 2. 权限异常
- [ ] 工序开始API调用失败
- [ ] 显示权限错误提示
- [ ] 入库操作已完成，但工序状态未变更

#### 3. 数据异常
- [ ] 工序状态数据异常
- [ ] 系统能够正确处理
- [ ] 不影响入库操作

### ✅ 性能验证

#### 1. 响应时间
- [ ] 入库操作响应时间正常
- [ ] 工序开始操作响应时间正常
- [ ] 整体流程响应时间可接受

#### 2. 并发处理
- [ ] 多次快速点击不会重复执行
- [ ] 加载状态正确显示
- [ ] 防止重复提交

### ✅ 兼容性验证

#### 1. 不同工序状态
- [ ] 未开始状态正常处理
- [ ] 执行中状态正常处理
- [ ] 已完成状态正常处理

#### 2. 不同工单状态
- [ ] NEW状态工单正常处理
- [ ] IN_PROGRESS状态工单正常处理
- [ ] PAUSED状态工单正常处理

## 测试场景

### 场景1：首次入库（工序未开始）
1. 工序状态：未开始(0)
2. 执行入库操作
3. 验证：入库成功 + 工序自动开始
4. 结果：工序状态变为执行中(1)

### 场景2：再次入库（工序已开始）
1. 工序状态：执行中(1)
2. 执行入库操作
3. 验证：入库成功 + 工序状态不变
4. 结果：工序状态保持执行中(1)

### 场景3：入库失败
1. 工序状态：未开始(0)
2. 模拟入库失败
3. 验证：显示错误 + 工序状态不变
4. 结果：工序状态保持未开始(0)

## 验证通过标准

- ✅ 所有核心功能正常工作
- ✅ 状态管理逻辑正确
- ✅ 与领料功能行为一致
- ✅ 日志记录完整清晰
- ✅ 界面状态更新正确
- ✅ 异常情况处理得当
- ✅ 性能表现良好
- ✅ 兼容性测试通过

## 总结

入库功能现在具有与领料功能完全一致的双重功能：
1. **主要功能**：执行入库操作
2. **附加功能**：自动启动工序

这种设计确保了系统的一致性和用户体验的统一性。
