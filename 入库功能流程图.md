# 入库扫码功能流程图（完全复制领料功能）

## 完整流程

```
用户点击"入库"按钮
         ↓
    handleStorageScan()
         ↓
  调用getStepTaskDetail API
  获取已入库数据(上板/下板/单板)
         ↓
    检查入库完成状态
         ↓
      已完成？ → 显示完成提示，结束流程
         ↓ 否
buildAvailableStorageBoardOptions()
构建智能板型选择列表
         ↓
   显示智能板型选择对话框
   [显示已入库数量和剩余数量]
         ↓
    用户选择板型
         ↓
  startStorageScanCode()
         ↓
    调用uni.scanCode()
         ↓
      扫码成功？
         ↓
  processStorageScanResult()
         ↓
   解析zone_code成功？
         ↓
   显示确认对话框
   [产品/板型/区域]
         ↓
     用户确认？
         ↓
executeStorageInboundOperation()
         ↓
   调用inboundSfp API
         ↓
    API调用成功？
         ↓
   显示成功提示
```

## 错误处理分支

```
用户取消板型选择 → 结束流程

扫码失败 → handleStorageScanError() → 显示错误提示

zone_code解析失败 → 显示格式错误对话框

用户取消确认 → 显示取消提示

API调用失败 → 显示错误对话框
```

## 方法调用关系

1. **handleStorageScan(order, product, step)**
   - 调用 getStepTaskDetail API 获取已入库数据
   - 检查入库完成状态
   - 调用 buildAvailableStorageBoardOptions() 构建板型选择
   - 显示智能板型选择对话框
   - 调用 startStorageScanCode()

2. **buildAvailableStorageBoardOptions(onBoardCount, downBoardCount, oneBoardCount, orderQuantity)**
   - 实现与领料相同的互斥逻辑
   - 构建智能板型选择选项
   - 显示已入库数量和剩余数量信息

3. **startStorageScanCode(order, product, step, boardType, currentCount, stepTaskDetail)**
   - 启动扫码功能
   - 调用 processStorageScanResult()

4. **processStorageScanResult(scanData, order, product, step, boardType, currentCount, stepTaskDetail)**
   - 解析 zone_code
   - 显示确认对话框
   - 调用 executeStorageInboundOperation()

5. **executeStorageInboundOperation(scanData, product, boardType, step, zoneCode)**
   - 准备API参数
   - 调用 inboundSfp API
   - 处理响应结果

6. **extractZoneCode(scanData)**
   - 解析扫码数据
   - 提取 zone_code 字段

7. **handleStorageScanError(error)**
   - 处理扫码错误
   - 显示相应提示

## 数据流转

```
API调用: getStepTaskDetail(stepTaskId)
    ↓
已入库数据: {
  onBoard: 已入库上板数量,
  downBoard: 已入库下板数量,
  oneBoard: 已入库单板数量
}
    ↓
智能板型选择: 根据已入库数据构建选项
例如: "上板入库 (已入库: 5, 还需: 15)"
    ↓
用户选择: 板型 (上板/下板/单板)
    ↓
扫码输入: label_type:warehouse_zone|zone_code:SFQ20250601
    ↓
解析结果: zoneCode = "SFQ20250601"
    ↓
API参数: {
  orderCode: "工单编号",
  quantity: 产品数量,
  productNumber: "产品编号",
  itemName: "产品名称",
  boardType: "上板/下板/单板",
  stepTaskId: "工序任务ID",
  stepName: "工序名称",
  zoneCode: "SFQ20250601"
}
    ↓
API响应: { code: 0, message: "success", data: {...} }
    ↓
用户反馈: "入库成功" 提示
```

## 关键特性

1. **完全复制领料功能**: 与领料功能使用完全相同的用户交互流程和数据显示逻辑
2. **智能状态管理**: 自动获取已入库数据，智能判断完成状态，提供详细的数量信息
3. **互斥逻辑**: 实现与领料相同的板型互斥规则，确保数据一致性
4. **扫码解析**: 支持多种格式，容错性强，解析zone_code而非purchase_no
5. **错误处理**: 完整的错误处理机制，覆盖各种异常情况
6. **API集成**: 调用inboundSfp API执行实际入库操作
7. **用户体验**: 与领料功能保持完全一致的交互体验，降低学习成本

## 与领料功能的一致性

| 方面 | 领料功能 | 入库功能 | 一致性 |
|------|----------|----------|--------|
| 数据获取 | getStepTaskDetail | getStepTaskDetail | ✅ 完全相同 |
| 状态检查 | 检查领料完成状态 | 检查入库完成状态 | ✅ 逻辑相同 |
| 板型选择 | buildAvailableBoardOptions | buildAvailableStorageBoardOptions | ✅ 逻辑相同 |
| 互斥规则 | 单板与上下板互斥 | 单板与上下板互斥 | ✅ 完全相同 |
| 数量显示 | 显示已领取和剩余数量 | 显示已入库和剩余数量 | ✅ 格式相同 |
| 扫码流程 | 扫码→解析→确认→API | 扫码→解析→确认→API | ✅ 流程相同 |
| 错误处理 | 完整的错误处理机制 | 完整的错误处理机制 | ✅ 机制相同 |
