# 入库扫码功能测试说明

## 功能概述
在工序步骤列表中，当工序步骤名称为"分类清点数量入库"时，会显示一个紫色的"入库"按钮。点击该按钮会完全复制领料功能的用户交互流程：先获取已入库数据，显示详细的入库状态信息，然后进行板型选择和扫码操作。

## 实现的功能

### 1. 按钮显示条件
- 工序步骤名称必须为"分类清点数量入库"
- 工单状态不能为"暂停中"(PAUSED)
- 按钮样式为紫色背景，与现有按钮风格保持一致

### 2. 数据获取和状态检查
- 点击"入库"按钮后，首先调用 `getStepTaskDetail(step.stepTaskId)` 获取已入库数据
- 获取上板、下板、单板的已入库数量信息
- 检查入库完成状态，如果已完成则显示完成提示并阻止继续操作
- 与领料功能使用完全相同的完成状态判断逻辑

### 3. 智能板型选择
- 使用 `buildAvailableStorageBoardOptions()` 构建可选板型列表
- 实现与领料相同的互斥规则：单板与上下板互斥
- 显示详细的已入库数量和剩余数量信息
- 格式：`上板入库 (已入库: X, 还需: Y)` 或 `上板入库 (已入库: X - 已完成)`

### 4. 扫码功能
- 支持二维码和条形码扫描
- 使用uni-app的`uni.scanCode()` API
- 扫码成功后显示确认对话框，包含产品、板型、区域信息
- 用户可以选择确认或取消入库操作

### 5. 错误处理
- 用户取消板型选择：不进入扫码流程
- 用户取消扫码：显示"已取消入库扫码"提示
- 扫码失败：显示"入库扫码失败，请重试"提示
- API调用失败：显示具体错误信息
- 网络错误：显示网络连接失败提示
- 获取已入库数据失败：显示警告但允许继续操作

### 6. 扫码结果处理
- 自动解析扫码数据中的 `zone_code` 字段
- 支持格式：`label_type:warehouse_zone|zone_code:SFQ20250601`
- 显示扫码内容确认对话框，包含产品名称、板型、仓库区域等信息
- 如果无法解析 zone_code，会显示错误提示
- 用户确认后调用 `inboundSfp` API 执行入库操作

### 7. 双重功能实现
- **主要功能**: 执行入库操作（调用 inboundSfp API）
- **附加功能**: 自动启动工序（调用 handleStartStep）
- 入库成功后，如果工序状态为"未开始"，自动将工序状态变更为"执行中"
- 如果工序已经开始，不重复执行开始操作
- 与领料功能的双重功能逻辑保持完全一致

## 测试步骤

### 1. 准备测试环境
- 确保有包含"分类清点数量入库"工序步骤的工单数据
- 工单状态不为"暂停中"

### 2. 测试按钮显示
1. 打开任务管理页面
2. 展开包含"分类清点数量入库"工序的产品
3. 确认该工序步骤旁边显示紫色的"入库"按钮

### 3. 测试数据获取和状态检查
1. 点击"入库"按钮
2. 确认系统调用 `getStepTaskDetail` API 获取已入库数据
3. 检查控制台日志，确认获取到上板、下板、单板的已入库数量
4. 如果已入库完成，确认显示完成状态对话框并阻止继续操作

### 4. 测试智能板型选择
1. 在未完成入库的情况下，确认显示智能板型选择对话框
2. 验证选项显示格式：
   - 无已入库数量：`上板入库`、`下板入库`、`单板入库`
   - 有已入库数量：`上板入库 (已入库: X, 还需: Y)`
   - 已完成：`上板入库 (已入库: X - 已完成)`
3. 验证互斥逻辑：
   - 如果单板有已入库数量，只显示单板选项
   - 如果上板或下板有已入库数量，隐藏单板选项
4. 选择任意一个板型选项

### 5. 测试扫码和入库功能
1. 确认扫码界面正常打开
2. 扫描包含 `zone_code` 的二维码（格式：`label_type:warehouse_zone|zone_code:SFQ20250601`）
3. 确认显示扫码结果确认对话框，包含产品、板型、仓库区域信息
4. 点击"确定"按钮
5. 确认调用 `inboundSfp` API 执行入库操作
6. 确认显示"入库成功"提示或相应的错误信息
7. 检查控制台日志，确认参数正确传递

### 6. 测试双重功能（工序自动启动）
1. **测试未开始状态的工序**：
   - 确保工序状态为"未开始"(isCompleted = 0)
   - 执行入库操作
   - 验证入库成功后工序状态自动变为"执行中"(isCompleted = 1)
   - 检查工单状态是否从"NEW"变为"IN_PROGRESS"（如果适用）

2. **测试已开始状态的工序**：
   - 确保工序状态为"执行中"(isCompleted = 1)
   - 执行入库操作
   - 验证入库成功后工序状态保持不变
   - 确认不重复调用工序开始API

### 7. 测试错误处理
1. 点击"入库"按钮后在板型选择对话框中取消，确认不进入扫码流程
2. 选择板型后取消扫码，确认显示取消提示
3. 扫描格式错误的二维码（不包含 zone_code），确认显示格式错误提示
4. 在扫码结果确认对话框中点击"取消"，确认显示取消提示
5. 测试网络异常情况下的错误处理
6. 测试获取已入库数据失败的情况，确认显示警告但允许继续操作
7. **测试入库失败时的工序状态**：确认入库失败时不执行工序开始操作

## 代码修改说明

### 1. 模板修改
- 在步骤动作按钮区域添加了"入库"按钮
- 按钮只在特定条件下显示

### 2. 样式修改
- 添加了`.storage-btn`样式类
- 使用紫色主题色(#9c27b0)
- 包含hover和active状态样式
- 在响应式样式中也添加了相应的边距设置

### 3. 方法添加
- `handleStorageScan()`: 处理入库按钮点击，完全复制领料功能流程，获取已入库数据并显示智能板型选择
- `buildAvailableStorageBoardOptions()`: 构建可用的入库板型选择选项，实现与领料相同的互斥逻辑
- `startStorageScanCode()`: 选择板型后开始扫码流程，接收与领料相同的参数
- `processStorageScanResult()`: 处理扫码成功结果，解析 zone_code，接收与领料相同的参数结构
- `executeStorageInboundOperation()`: 执行入库操作，调用 inboundSfp API，优化错误处理
- `handleStorageScanError()`: 处理扫码错误
- `extractZoneCode()`: 从扫码数据中解析 zone_code 字段

## 扫码数据格式说明

### 支持的二维码格式
```
label_type:warehouse_zone|zone_code:SFQ20250601
```

### 解析逻辑
- 使用正则表达式 `/zone_code:([^|]+)/i` 提取 zone_code 值
- 如果正则匹配失败，会尝试手动按 `|` 分割并查找包含 `zone_code:` 的部分
- 解析出的 zone_code 会作为参数传递给后端接口

### inboundSfp API 参数
```javascript
const apiParams = {
  orderCode: order.orderCode,           // 工单编号
  quantity: product.orderItemQuantity,  // 产品数量
  productNumber: product.productId,     // 产品编号
  itemName: product.productName,        // 产品名称
  boardType: boardType,                 // 板型（上板/下板/单板）
  stepTaskId: step.stepTaskId,          // 工序任务ID
  stepName: step.stepName,              // 工序名称
  zoneCode: zoneCode,                   // 从扫码结果解析的仓库区域代码
}
```

### 与领料功能的对比
| 功能 | 领料 | 入库 |
|------|------|------|
| 数据获取 | getStepTaskDetail | getStepTaskDetail |
| 状态检查 | 检查领料完成状态 | 检查入库完成状态 |
| 板型选择 | buildAvailableBoardOptions | buildAvailableStorageBoardOptions |
| 扫码解析 | 解析purchase_no | 解析zone_code |
| API调用 | outboundInventory | inboundSfp |
| **双重功能** | **出库 + 工序开始** | **入库 + 工序开始** |
| 工序状态变更 | 0 → 1 (未开始 → 执行中) | 0 → 1 (未开始 → 执行中) |
| 工单状态变更 | NEW → IN_PROGRESS | NEW → IN_PROGRESS |
| 状态检查优化 | 无额外检查 | 检查 step.isCompleted |
| 用户体验 | 完全相同的交互流程 | 完全相同的交互流程 |

## 注意事项
1. **完全复制领料功能**: 入库功能与领料功能使用完全相同的用户交互流程和数据显示逻辑
2. **智能状态管理**: 自动获取已入库数据，智能判断完成状态，提供详细的数量信息显示
3. **互斥逻辑**: 实现与领料相同的板型互斥规则，确保数据一致性
4. **API集成**: 使用 `inboundSfp` API 执行实际入库操作，包含完整的错误处理
5. **数据解析**: zone_code 解析支持大小写不敏感匹配，容错性强
6. **错误处理**: 包含完整的错误处理机制，覆盖网络异常、数据格式错误等各种情况
7. **用户体验**: 与领料功能保持完全一致的交互体验，降低学习成本
8. **状态同步**: 建议在入库成功后刷新页面数据，确保状态同步
